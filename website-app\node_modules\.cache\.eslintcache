[{"F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js": "1", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js": "2", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js": "3", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js": "4", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js": "5", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js": "6", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js": "7", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js": "8", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js": "9", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js": "10", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js": "11", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js": "12", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js": "13", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js": "14", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js": "15", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js": "16", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js": "17", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js": "18", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js": "19", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js": "20", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js": "21", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js": "22", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js": "23", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js": "24", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js": "25", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js": "26", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js": "27", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js": "28", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js": "29", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js": "30", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js": "31", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js": "32", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js": "33", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js": "34", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js": "35", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js": "36", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js": "37", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js": "38", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js": "39", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js": "40", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js": "41", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js": "42", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js": "43", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js": "44", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js": "45", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js": "46", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js": "47", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js": "48", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js": "49", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js": "50", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index2.js": "51"}, {"size": 873, "mtime": 1748099557892, "results": "52", "hashOfConfig": "53"}, {"size": 2080, "mtime": 1748099557807, "results": "54", "hashOfConfig": "53"}, {"size": 375, "mtime": 1748099557918, "results": "55", "hashOfConfig": "53"}, {"size": 873, "mtime": 1748099557881, "results": "56", "hashOfConfig": "53"}, {"size": 1324, "mtime": 1748099557880, "results": "57", "hashOfConfig": "53"}, {"size": 2947, "mtime": 1748099557881, "results": "58", "hashOfConfig": "53"}, {"size": 778, "mtime": 1748099557888, "results": "59", "hashOfConfig": "53"}, {"size": 2107, "mtime": 1748099557862, "results": "60", "hashOfConfig": "53"}, {"size": 9872, "mtime": 1748502443203, "results": "61", "hashOfConfig": "53"}, {"size": 12494, "mtime": 1748511095235, "results": "62", "hashOfConfig": "53"}, {"size": 1975, "mtime": 1748099557914, "results": "63", "hashOfConfig": "53"}, {"size": 16874, "mtime": 1748502443083, "results": "64", "hashOfConfig": "53"}, {"size": 5142, "mtime": 1748506973873, "results": "65", "hashOfConfig": "53"}, {"size": 5569, "mtime": 1748099557913, "results": "66", "hashOfConfig": "53"}, {"size": 203, "mtime": 1748099557903, "results": "67", "hashOfConfig": "53"}, {"size": 40528, "mtime": 1748504662284, "results": "68", "hashOfConfig": "53"}, {"size": 13202, "mtime": 1748502443937, "results": "69", "hashOfConfig": "53"}, {"size": 550, "mtime": 1748099557908, "results": "70", "hashOfConfig": "53"}, {"size": 50900, "mtime": 1748507085743, "results": "71", "hashOfConfig": "53"}, {"size": 173, "mtime": 1748099557897, "results": "72", "hashOfConfig": "53"}, {"size": 2075, "mtime": 1748099557894, "results": "73", "hashOfConfig": "53"}, {"size": 1378, "mtime": 1748099557911, "results": "74", "hashOfConfig": "53"}, {"size": 181, "mtime": 1748099557890, "results": "75", "hashOfConfig": "53"}, {"size": 3693, "mtime": 1748504480080, "results": "76", "hashOfConfig": "53"}, {"size": 2687, "mtime": 1748504554127, "results": "77", "hashOfConfig": "53"}, {"size": 2385, "mtime": 1748099557919, "results": "78", "hashOfConfig": "53"}, {"size": 5232, "mtime": 1748502443971, "results": "79", "hashOfConfig": "53"}, {"size": 2838, "mtime": 1748502444197, "results": "80", "hashOfConfig": "53"}, {"size": 5291, "mtime": 1748099557858, "results": "81", "hashOfConfig": "53"}, {"size": 19243, "mtime": 1748502443913, "results": "82", "hashOfConfig": "53"}, {"size": 7039, "mtime": 1748502443954, "results": "83", "hashOfConfig": "53"}, {"size": 16375, "mtime": 1748502443393, "results": "84", "hashOfConfig": "53"}, {"size": 863, "mtime": 1748099557809, "results": "85", "hashOfConfig": "53"}, {"size": 7643, "mtime": 1748504280903, "results": "86", "hashOfConfig": "53"}, {"size": 13087, "mtime": 1748506879841, "results": "87", "hashOfConfig": "53"}, {"size": 17984, "mtime": 1748502444191, "results": "88", "hashOfConfig": "53"}, {"size": 7395, "mtime": 1748502443746, "results": "89", "hashOfConfig": "53"}, {"size": 5426, "mtime": 1748502443913, "results": "90", "hashOfConfig": "53"}, {"size": 14273, "mtime": 1748507051480, "results": "91", "hashOfConfig": "53"}, {"size": 616, "mtime": 1748099557814, "results": "92", "hashOfConfig": "53"}, {"size": 7289, "mtime": 1748099557872, "results": "93", "hashOfConfig": "53"}, {"size": 926, "mtime": 1748099557847, "results": "94", "hashOfConfig": "53"}, {"size": 6584, "mtime": 1748502443795, "results": "95", "hashOfConfig": "53"}, {"size": 6983, "mtime": 1748502443966, "results": "96", "hashOfConfig": "53"}, {"size": 1839, "mtime": 1748099557852, "results": "97", "hashOfConfig": "53"}, {"size": 4217, "mtime": 1748502443795, "results": "98", "hashOfConfig": "53"}, {"size": 258, "mtime": 1748099557848, "results": "99", "hashOfConfig": "53"}, {"size": 22766, "mtime": 1748502443451, "results": "100", "hashOfConfig": "53"}, {"size": 6277, "mtime": 1748502443935, "results": "101", "hashOfConfig": "53"}, {"size": 2375, "mtime": 1748099557861, "results": "102", "hashOfConfig": "53"}, {"size": 24591, "mtime": 1748505170630, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uca01g", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js", ["257"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js", ["258"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js", ["259", "260"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js", ["261", "262", "263", "264", "265", "266", "267", "268", "269"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js", ["270"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js", ["271", "272"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js", ["273", "274", "275", "276"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js", ["277", "278"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js", ["279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js", ["292", "293", "294", "295", "296", "297", "298"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js", ["299", "300"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js", ["301", "302", "303", "304", "305", "306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js", ["317", "318"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js", ["319", "320", "321"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js", ["322", "323"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js", ["324", "325", "326", "327"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js", ["328"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js", ["329"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js", ["330"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js", ["331", "332"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js", ["333", "334", "335"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js", ["336", "337", "338"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js", ["339", "340", "341"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js", ["342", "343", "344", "345", "346", "347", "348", "349"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js", ["350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js", ["364", "365", "366", "367", "368", "369", "370", "371", "372", "373", "374"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js", ["375", "376"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js", ["377", "378", "379", "380", "381", "382", "383"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js", ["384", "385", "386"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js", ["387", "388"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js", ["389", "390"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js", ["391"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js", ["392", "393", "394", "395", "396", "397", "398"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js", ["399", "400", "401", "402"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js", ["403"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index2.js", ["404", "405", "406", "407", "408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423"], [], {"ruleId": "424", "severity": 1, "message": "425", "line": 13, "column": 8, "nodeType": "426", "messageId": "427", "endLine": 13, "endColumn": 15}, {"ruleId": "428", "severity": 1, "message": "429", "line": 22, "column": 6, "nodeType": "430", "endLine": 22, "endColumn": 8, "suggestions": "431"}, {"ruleId": "424", "severity": 1, "message": "432", "line": 23, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 23, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "433", "line": 64, "column": 6, "nodeType": "430", "endLine": 64, "endColumn": 12, "suggestions": "434"}, {"ruleId": "424", "severity": 1, "message": "435", "line": 21, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 21, "endColumn": 17}, {"ruleId": "428", "severity": 1, "message": "436", "line": 60, "column": 6, "nodeType": "430", "endLine": 60, "endColumn": 18, "suggestions": "437"}, {"ruleId": "438", "severity": 1, "message": "439", "line": 94, "column": 19, "nodeType": "440", "messageId": "441", "endLine": 94, "endColumn": 21}, {"ruleId": "442", "severity": 1, "message": "443", "line": 214, "column": 13, "nodeType": "444", "endLine": 214, "endColumn": 25}, {"ruleId": "442", "severity": 1, "message": "443", "line": 219, "column": 13, "nodeType": "444", "endLine": 219, "endColumn": 25}, {"ruleId": "442", "severity": 1, "message": "443", "line": 224, "column": 13, "nodeType": "444", "endLine": 224, "endColumn": 25}, {"ruleId": "438", "severity": 1, "message": "445", "line": 229, "column": 31, "nodeType": "440", "messageId": "441", "endLine": 229, "endColumn": 33}, {"ruleId": "442", "severity": 1, "message": "443", "line": 236, "column": 13, "nodeType": "444", "endLine": 236, "endColumn": 25}, {"ruleId": "442", "severity": 1, "message": "443", "line": 241, "column": 13, "nodeType": "444", "endLine": 241, "endColumn": 25}, {"ruleId": "428", "severity": 1, "message": "446", "line": 33, "column": 6, "nodeType": "430", "endLine": 33, "endColumn": 8, "suggestions": "447"}, {"ruleId": "424", "severity": 1, "message": "448", "line": 20, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 20, "endColumn": 17}, {"ruleId": "428", "severity": 1, "message": "449", "line": 88, "column": 6, "nodeType": "430", "endLine": 88, "endColumn": 12, "suggestions": "450"}, {"ruleId": "428", "severity": 1, "message": "449", "line": 47, "column": 6, "nodeType": "430", "endLine": 47, "endColumn": 12, "suggestions": "451"}, {"ruleId": "438", "severity": 1, "message": "445", "line": 94, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 94, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 95, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 95, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 96, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 96, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "432", "line": 9, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 9, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "449", "line": 26, "column": 6, "nodeType": "430", "endLine": 26, "endColumn": 12, "suggestions": "452"}, {"ruleId": "424", "severity": 1, "message": "453", "line": 33, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 33, "endColumn": 14}, {"ruleId": "424", "severity": 1, "message": "454", "line": 33, "column": 16, "nodeType": "426", "messageId": "427", "endLine": 33, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "455", "line": 34, "column": 22, "nodeType": "426", "messageId": "427", "endLine": 34, "endColumn": 35}, {"ruleId": "428", "severity": 1, "message": "456", "line": 314, "column": 6, "nodeType": "430", "endLine": 314, "endColumn": 15, "suggestions": "457"}, {"ruleId": "442", "severity": 1, "message": "443", "line": 326, "column": 15, "nodeType": "444", "endLine": 326, "endColumn": 27}, {"ruleId": "442", "severity": 1, "message": "443", "line": 327, "column": 15, "nodeType": "444", "endLine": 327, "endColumn": 27}, {"ruleId": "442", "severity": 1, "message": "443", "line": 466, "column": 23, "nodeType": "444", "endLine": 469, "endColumn": 24}, {"ruleId": "442", "severity": 1, "message": "443", "line": 475, "column": 23, "nodeType": "444", "endLine": 478, "endColumn": 24}, {"ruleId": "458", "severity": 1, "message": "459", "line": 530, "column": 19, "nodeType": "444", "messageId": "460", "endLine": 530, "endColumn": 34}, {"ruleId": "442", "severity": 1, "message": "443", "line": 1024, "column": 17, "nodeType": "444", "endLine": 1024, "endColumn": 50}, {"ruleId": "442", "severity": 1, "message": "443", "line": 1029, "column": 17, "nodeType": "444", "endLine": 1029, "endColumn": 50}, {"ruleId": "442", "severity": 1, "message": "443", "line": 1035, "column": 17, "nodeType": "444", "endLine": 1035, "endColumn": 50}, {"ruleId": "461", "severity": 1, "message": "462", "line": 1048, "column": 13, "nodeType": "444", "messageId": "463", "endLine": 1048, "endColumn": 63, "fix": "464"}, {"ruleId": "458", "severity": 1, "message": "465", "line": 251, "column": 9, "nodeType": "444", "messageId": "460", "endLine": 256, "endColumn": 11}, {"ruleId": "458", "severity": 1, "message": "466", "line": 259, "column": 9, "nodeType": "444", "messageId": "460", "endLine": 266, "endColumn": 11}, {"ruleId": "442", "severity": 1, "message": "443", "line": 287, "column": 47, "nodeType": "444", "endLine": 287, "endColumn": 59}, {"ruleId": "442", "severity": 1, "message": "443", "line": 288, "column": 51, "nodeType": "444", "endLine": 288, "endColumn": 63}, {"ruleId": "467", "severity": 1, "message": "468", "line": 313, "column": 19, "nodeType": "444", "endLine": 313, "endColumn": 66}, {"ruleId": "442", "severity": 1, "message": "469", "line": 386, "column": 17, "nodeType": "444", "endLine": 390, "endColumn": 18}, {"ruleId": "442", "severity": 1, "message": "469", "line": 402, "column": 17, "nodeType": "444", "endLine": 407, "endColumn": 18}, {"ruleId": "442", "severity": 1, "message": "443", "line": 10, "column": 9, "nodeType": "444", "endLine": 10, "endColumn": 69}, {"ruleId": "470", "severity": 1, "message": "471", "line": 10, "column": 17, "nodeType": "472", "messageId": "473", "endLine": 10, "endColumn": 44}, {"ruleId": "428", "severity": 1, "message": "449", "line": 748, "column": 6, "nodeType": "430", "endLine": 748, "endColumn": 21, "suggestions": "474"}, {"ruleId": "424", "severity": 1, "message": "475", "line": 802, "column": 13, "nodeType": "426", "messageId": "427", "endLine": 802, "endColumn": 17}, {"ruleId": "428", "severity": 1, "message": "476", "line": 814, "column": 37, "nodeType": "426", "endLine": 814, "endColumn": 48}, {"ruleId": "438", "severity": 1, "message": "445", "line": 823, "column": 58, "nodeType": "440", "messageId": "441", "endLine": 823, "endColumn": 60}, {"ruleId": "424", "severity": 1, "message": "432", "line": 1001, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 1001, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "477", "line": 1003, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 1003, "endColumn": 17}, {"ruleId": "424", "severity": 1, "message": "478", "line": 1005, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 1005, "endColumn": 32}, {"ruleId": "424", "severity": 1, "message": "479", "line": 1008, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 1008, "endColumn": 26}, {"ruleId": "424", "severity": 1, "message": "480", "line": 1011, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 1011, "endColumn": 20}, {"ruleId": "424", "severity": 1, "message": "481", "line": 1011, "column": 22, "nodeType": "426", "messageId": "427", "endLine": 1011, "endColumn": 35}, {"ruleId": "424", "severity": 1, "message": "482", "line": 1015, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 1015, "endColumn": 16}, {"ruleId": "428", "severity": 1, "message": "483", "line": 1032, "column": 6, "nodeType": "430", "endLine": 1032, "endColumn": 15, "suggestions": "484"}, {"ruleId": "424", "severity": 1, "message": "485", "line": 1033, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 1033, "endColumn": 29}, {"ruleId": "424", "severity": 1, "message": "486", "line": 1054, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 1054, "endColumn": 28}, {"ruleId": "424", "severity": 1, "message": "487", "line": 1068, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 1068, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "488", "line": 1077, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 1077, "endColumn": 28}, {"ruleId": "424", "severity": 1, "message": "489", "line": 4, "column": 27, "nodeType": "426", "messageId": "427", "endLine": 4, "endColumn": 36}, {"ruleId": "438", "severity": 1, "message": "445", "line": 14, "column": 13, "nodeType": "440", "messageId": "441", "endLine": 14, "endColumn": 15}, {"ruleId": "424", "severity": 1, "message": "489", "line": 1, "column": 24, "nodeType": "426", "messageId": "427", "endLine": 1, "endColumn": 33}, {"ruleId": "424", "severity": 1, "message": "490", "line": 3, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 3, "endColumn": 16}, {"ruleId": "461", "severity": 1, "message": "462", "line": 12, "column": 9, "nodeType": "444", "messageId": "463", "endLine": 12, "endColumn": 41, "fix": "491"}, {"ruleId": "424", "severity": 1, "message": "492", "line": 7, "column": 11, "nodeType": "426", "messageId": "427", "endLine": 7, "endColumn": 15}, {"ruleId": "424", "severity": 1, "message": "493", "line": 10, "column": 14, "nodeType": "426", "messageId": "427", "endLine": 10, "endColumn": 19}, {"ruleId": "424", "severity": 1, "message": "489", "line": 1, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 1, "endColumn": 26}, {"ruleId": "424", "severity": 1, "message": "492", "line": 7, "column": 11, "nodeType": "426", "messageId": "427", "endLine": 7, "endColumn": 15}, {"ruleId": "424", "severity": 1, "message": "432", "line": 7, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 7, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "493", "line": 8, "column": 14, "nodeType": "426", "messageId": "427", "endLine": 8, "endColumn": 19}, {"ruleId": "438", "severity": 1, "message": "445", "line": 67, "column": 20, "nodeType": "440", "messageId": "441", "endLine": 67, "endColumn": 22}, {"ruleId": "438", "severity": 1, "message": "439", "line": 9, "column": 18, "nodeType": "440", "messageId": "441", "endLine": 9, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "449", "line": 72, "column": 6, "nodeType": "430", "endLine": 72, "endColumn": 9, "suggestions": "494"}, {"ruleId": "424", "severity": 1, "message": "432", "line": 10, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 10, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "456", "line": 59, "column": 6, "nodeType": "430", "endLine": 59, "endColumn": 8, "suggestions": "495"}, {"ruleId": "424", "severity": 1, "message": "496", "line": 25, "column": 12, "nodeType": "426", "messageId": "427", "endLine": 25, "endColumn": 23}, {"ruleId": "428", "severity": 1, "message": "476", "line": 60, "column": 39, "nodeType": "426", "endLine": 60, "endColumn": 50}, {"ruleId": "428", "severity": 1, "message": "497", "line": 93, "column": 8, "nodeType": "430", "endLine": 93, "endColumn": 50, "suggestions": "498"}, {"ruleId": "424", "severity": 1, "message": "499", "line": 3, "column": 46, "nodeType": "426", "messageId": "427", "endLine": 3, "endColumn": 56}, {"ruleId": "424", "severity": 1, "message": "500", "line": 4, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 4, "endColumn": 21}, {"ruleId": "428", "severity": 1, "message": "501", "line": 98, "column": 6, "nodeType": "430", "endLine": 98, "endColumn": 24, "suggestions": "502"}, {"ruleId": "424", "severity": 1, "message": "503", "line": 2, "column": 8, "nodeType": "426", "messageId": "427", "endLine": 2, "endColumn": 10}, {"ruleId": "424", "severity": 1, "message": "504", "line": 12, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 12, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "505", "line": 101, "column": 13, "nodeType": "426", "messageId": "427", "endLine": 101, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "499", "line": 5, "column": 3, "nodeType": "426", "messageId": "427", "endLine": 5, "endColumn": 13}, {"ruleId": "424", "severity": 1, "message": "500", "line": 10, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 10, "endColumn": 21}, {"ruleId": "424", "severity": 1, "message": "506", "line": 36, "column": 13, "nodeType": "426", "messageId": "427", "endLine": 36, "endColumn": 25}, {"ruleId": "424", "severity": 1, "message": "507", "line": 36, "column": 27, "nodeType": "426", "messageId": "427", "endLine": 36, "endColumn": 38}, {"ruleId": "508", "severity": 1, "message": "509", "line": 95, "column": 11, "nodeType": "510", "messageId": "511", "endLine": 95, "endColumn": 20}, {"ruleId": "428", "severity": 1, "message": "476", "line": 107, "column": 39, "nodeType": "426", "endLine": 107, "endColumn": 50}, {"ruleId": "428", "severity": 1, "message": "512", "line": 117, "column": 8, "nodeType": "430", "endLine": 117, "endColumn": 34, "suggestions": "513"}, {"ruleId": "428", "severity": 1, "message": "497", "line": 120, "column": 8, "nodeType": "430", "endLine": 120, "endColumn": 20, "suggestions": "514"}, {"ruleId": "424", "severity": 1, "message": "506", "line": 11, "column": 11, "nodeType": "426", "messageId": "427", "endLine": 11, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "507", "line": 11, "column": 25, "nodeType": "426", "messageId": "427", "endLine": 11, "endColumn": 36}, {"ruleId": "438", "severity": 1, "message": "445", "line": 45, "column": 25, "nodeType": "440", "messageId": "441", "endLine": 45, "endColumn": 27}, {"ruleId": "438", "severity": 1, "message": "445", "line": 59, "column": 25, "nodeType": "440", "messageId": "441", "endLine": 59, "endColumn": 27}, {"ruleId": "428", "severity": 1, "message": "456", "line": 66, "column": 6, "nodeType": "430", "endLine": 66, "endColumn": 15, "suggestions": "515"}, {"ruleId": "438", "severity": 1, "message": "439", "line": 69, "column": 14, "nodeType": "440", "messageId": "441", "endLine": 69, "endColumn": 16}, {"ruleId": "438", "severity": 1, "message": "439", "line": 72, "column": 21, "nodeType": "440", "messageId": "441", "endLine": 72, "endColumn": 23}, {"ruleId": "438", "severity": 1, "message": "445", "line": 73, "column": 11, "nodeType": "440", "messageId": "441", "endLine": 73, "endColumn": 13}, {"ruleId": "438", "severity": 1, "message": "445", "line": 77, "column": 34, "nodeType": "440", "messageId": "441", "endLine": 77, "endColumn": 36}, {"ruleId": "438", "severity": 1, "message": "445", "line": 81, "column": 25, "nodeType": "440", "messageId": "441", "endLine": 81, "endColumn": 27}, {"ruleId": "438", "severity": 1, "message": "445", "line": 93, "column": 57, "nodeType": "440", "messageId": "441", "endLine": 93, "endColumn": 59}, {"ruleId": "438", "severity": 1, "message": "445", "line": 121, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 121, "endColumn": 17}, {"ruleId": "438", "severity": 1, "message": "439", "line": 154, "column": 40, "nodeType": "440", "messageId": "441", "endLine": 154, "endColumn": 42}, {"ruleId": "438", "severity": 1, "message": "439", "line": 388, "column": 36, "nodeType": "440", "messageId": "441", "endLine": 388, "endColumn": 38}, {"ruleId": "428", "severity": 1, "message": "516", "line": 62, "column": 6, "nodeType": "430", "endLine": 62, "endColumn": 15, "suggestions": "517"}, {"ruleId": "438", "severity": 1, "message": "445", "line": 95, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 95, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 98, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 98, "endColumn": 17}, {"ruleId": "438", "severity": 1, "message": "439", "line": 119, "column": 18, "nodeType": "440", "messageId": "441", "endLine": 119, "endColumn": 20}, {"ruleId": "438", "severity": 1, "message": "445", "line": 125, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 125, "endColumn": 17}, {"ruleId": "438", "severity": 1, "message": "445", "line": 131, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 131, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 137, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 137, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 140, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 140, "endColumn": 17}, {"ruleId": "438", "severity": 1, "message": "445", "line": 185, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 185, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 188, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 188, "endColumn": 17}, {"ruleId": "518", "severity": 1, "message": "519", "line": 224, "column": 15, "nodeType": "444", "endLine": 232, "endColumn": 17}, {"ruleId": "428", "severity": 1, "message": "520", "line": 41, "column": 6, "nodeType": "430", "endLine": 41, "endColumn": 8, "suggestions": "521"}, {"ruleId": "424", "severity": 1, "message": "522", "line": 44, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 44, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "523", "line": 58, "column": 6, "nodeType": "430", "endLine": 58, "endColumn": 9, "suggestions": "524"}, {"ruleId": "424", "severity": 1, "message": "522", "line": 64, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 64, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "525", "line": 105, "column": 7, "nodeType": "426", "messageId": "427", "endLine": 105, "endColumn": 12}, {"ruleId": "438", "severity": 1, "message": "445", "line": 134, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 134, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 158, "column": 15, "nodeType": "440", "messageId": "441", "endLine": 158, "endColumn": 17}, {"ruleId": "438", "severity": 1, "message": "445", "line": 187, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 187, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "439", "line": 194, "column": 32, "nodeType": "440", "messageId": "441", "endLine": 194, "endColumn": 34}, {"ruleId": "424", "severity": 1, "message": "526", "line": 1, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 1, "endColumn": 25}, {"ruleId": "424", "severity": 1, "message": "527", "line": 2, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 2, "endColumn": 19}, {"ruleId": "424", "severity": 1, "message": "528", "line": 3, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 3, "endColumn": 19}, {"ruleId": "428", "severity": 1, "message": "436", "line": 41, "column": 6, "nodeType": "430", "endLine": 41, "endColumn": 8, "suggestions": "529"}, {"ruleId": "424", "severity": 1, "message": "522", "line": 44, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 44, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "432", "line": 23, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 23, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "436", "line": 45, "column": 6, "nodeType": "430", "endLine": 45, "endColumn": 8, "suggestions": "530"}, {"ruleId": "428", "severity": 1, "message": "531", "line": 57, "column": 6, "nodeType": "430", "endLine": 57, "endColumn": 21, "suggestions": "532"}, {"ruleId": "424", "severity": 1, "message": "507", "line": 8, "column": 25, "nodeType": "426", "messageId": "427", "endLine": 8, "endColumn": 36}, {"ruleId": "424", "severity": 1, "message": "432", "line": 9, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 9, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "456", "line": 53, "column": 6, "nodeType": "430", "endLine": 53, "endColumn": 8, "suggestions": "533"}, {"ruleId": "438", "severity": 1, "message": "439", "line": 108, "column": 24, "nodeType": "440", "messageId": "441", "endLine": 108, "endColumn": 26}, {"ruleId": "438", "severity": 1, "message": "439", "line": 133, "column": 41, "nodeType": "440", "messageId": "441", "endLine": 133, "endColumn": 43}, {"ruleId": "518", "severity": 1, "message": "519", "line": 315, "column": 13, "nodeType": "444", "endLine": 323, "endColumn": 15}, {"ruleId": "518", "severity": 1, "message": "519", "line": 488, "column": 17, "nodeType": "444", "endLine": 492, "endColumn": 19}, {"ruleId": "424", "severity": 1, "message": "432", "line": 7, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 7, "endColumn": 24}, {"ruleId": "438", "severity": 1, "message": "445", "line": 56, "column": 22, "nodeType": "440", "messageId": "441", "endLine": 56, "endColumn": 24}, {"ruleId": "467", "severity": 1, "message": "468", "line": 148, "column": 23, "nodeType": "444", "endLine": 151, "endColumn": 25}, {"ruleId": "534", "severity": 1, "message": "535", "line": 153, "column": 23, "nodeType": "444", "endLine": 153, "endColumn": 27}, {"ruleId": "428", "severity": 1, "message": "536", "line": 26, "column": 6, "nodeType": "430", "endLine": 26, "endColumn": 14, "suggestions": "537"}, {"ruleId": "424", "severity": 1, "message": "499", "line": 10, "column": 3, "nodeType": "426", "messageId": "427", "endLine": 10, "endColumn": 13}, {"ruleId": "424", "severity": 1, "message": "506", "line": 45, "column": 11, "nodeType": "426", "messageId": "427", "endLine": 45, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "507", "line": 45, "column": 25, "nodeType": "426", "messageId": "427", "endLine": 45, "endColumn": 36}, {"ruleId": "428", "severity": 1, "message": "538", "line": 60, "column": 6, "nodeType": "430", "endLine": 60, "endColumn": 15, "suggestions": "539"}, {"ruleId": "428", "severity": 1, "message": "476", "line": 119, "column": 37, "nodeType": "426", "endLine": 119, "endColumn": 48}, {"ruleId": "438", "severity": 1, "message": "445", "line": 128, "column": 58, "nodeType": "440", "messageId": "441", "endLine": 128, "endColumn": 60}, {"ruleId": "424", "severity": 1, "message": "506", "line": 315, "column": 11, "nodeType": "426", "messageId": "427", "endLine": 315, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "507", "line": 315, "column": 25, "nodeType": "426", "messageId": "427", "endLine": 315, "endColumn": 36}, {"ruleId": "424", "severity": 1, "message": "432", "line": 316, "column": 17, "nodeType": "426", "messageId": "427", "endLine": 316, "endColumn": 24}, {"ruleId": "424", "severity": 1, "message": "477", "line": 318, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 318, "endColumn": 17}, {"ruleId": "424", "severity": 1, "message": "478", "line": 320, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 320, "endColumn": 32}, {"ruleId": "424", "severity": 1, "message": "479", "line": 323, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 323, "endColumn": 26}, {"ruleId": "424", "severity": 1, "message": "480", "line": 326, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 326, "endColumn": 20}, {"ruleId": "424", "severity": 1, "message": "481", "line": 326, "column": 22, "nodeType": "426", "messageId": "427", "endLine": 326, "endColumn": 35}, {"ruleId": "424", "severity": 1, "message": "482", "line": 330, "column": 10, "nodeType": "426", "messageId": "427", "endLine": 330, "endColumn": 16}, {"ruleId": "428", "severity": 1, "message": "483", "line": 347, "column": 6, "nodeType": "430", "endLine": 347, "endColumn": 15, "suggestions": "540"}, {"ruleId": "424", "severity": 1, "message": "485", "line": 348, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 348, "endColumn": 29}, {"ruleId": "424", "severity": 1, "message": "486", "line": 369, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 369, "endColumn": 28}, {"ruleId": "424", "severity": 1, "message": "487", "line": 383, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 383, "endColumn": 23}, {"ruleId": "424", "severity": 1, "message": "488", "line": 392, "column": 9, "nodeType": "426", "messageId": "427", "endLine": 392, "endColumn": 28}, "no-unused-vars", "'Cookies' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'location.state'. Either include it or remove the dependency array.", "ArrayExpression", ["541"], "'loading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchEvents', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array.", ["542"], "'refresh' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', and 'user'. Either include them or remove the dependency array.", ["543"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "Expected '===' and instead saw '=='.", "React Hook useEffect has a missing dependency: 'fullMessage'. Either include it or remove the dependency array.", ["544"], "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading' and 'stopLoading'. Either include them or remove the dependency array.", ["545"], ["546"], ["547"], "'data' is assigned a value but never used.", "'setData' is assigned a value but never used.", "'setTopproduct' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["548"], "react/jsx-pascal-case", "Imported JSX component Sales_daily must be in PascalCase or SCREAMING_SNAKE_CASE", "usePascalOrSnakeCase", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "noTargetBlankWithoutNoreferrer", {"range": "549", "text": "550"}, "Imported JSX component Change_password must be in PascalCase or SCREAMING_SNAKE_CASE", "Imported JSX component Forgot_password must be in PascalCase or SCREAMING_SNAKE_CASE", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "no-script-url", "Script URL is a form of eval.", "Literal", "unexpectedScriptURL", ["551"], "'sugg' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'listItem' is assigned a value but never used.", "'isDropdownOpenSupplier' is assigned a value but never used.", "'selectedSupplier' is assigned a value but never used.", "'quantities' is assigned a value but never used.", "'setQuantities' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'listProductWereAdded' and 'setIdProductAdded'. Either include them or remove the dependency array. If 'setIdProductAdded' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["552"], "'handleSupplierChange' is assigned a value but never used.", "'handleSupplierClick' is assigned a value but never used.", "'toggleDropdown' is assigned a value but never used.", "'dropdownRefSupplier' is assigned a value but never used.", "'useEffect' is defined but never used.", "'notify' is defined but never used.", {"range": "553", "text": "550"}, "'user' is assigned a value but never used.", "'Setdt' is assigned a value but never used.", ["554"], ["555"], "'selectedRow' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'debouncedFetchSuggestions'. Either include it or remove the dependency array.", ["556"], "'useContext' is defined but never used.", "'AuthContext' is defined but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'getSupplierByOrderId'. Either include them or remove the dependency array.", ["557"], "'io' is defined but never used.", "'messageHandled' is assigned a value but never used.", "'newMessage' is assigned a value but never used.", "'startLoading' is assigned a value but never used.", "'stopLoading' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "React Hook useEffect has missing dependencies: 'debouncedFetchSuggestions' and 'searchTerm'. Either include them or remove the dependency array.", ["558"], ["559"], ["560"], "React Hook useEffect has missing dependencies: 'loading', 'reload', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array. If 'reload' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["561"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "React Hook useEffect has missing dependencies: 'customer', 'startLoading', 'stopLoading', 'supplier', and 'user'. Either include them or remove the dependency array.", ["562"], "'selectedOrders' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', 'supplier', and 'user'. Either include them or remove the dependency array.", ["563"], "'count' is assigned a value but never used.", "'RiSettings4Line' is defined but never used.", "'FaRegBell' is defined but never used.", "'FaRegUser' is defined but never used.", ["564"], ["565"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["566"], ["567"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "React Hook useEffect has a missing dependency: 'toggleModal'. Either include it or remove the dependency array.", ["568"], "React Hook useEffect has a missing dependency: 'user.id_owner'. Either include it or remove the dependency array.", ["569"], ["570"], {"desc": "571", "fix": "572"}, {"desc": "573", "fix": "574"}, {"desc": "575", "fix": "576"}, {"desc": "577", "fix": "578"}, {"desc": "579", "fix": "580"}, {"desc": "579", "fix": "581"}, {"desc": "579", "fix": "582"}, {"desc": "583", "fix": "584"}, [40187, 40187], " rel=\"noreferrer\"", {"desc": "585", "fix": "586"}, {"desc": "587", "fix": "588"}, [559, 559], {"desc": "589", "fix": "590"}, {"desc": "591", "fix": "592"}, {"desc": "593", "fix": "594"}, {"desc": "595", "fix": "596"}, {"desc": "597", "fix": "598"}, {"desc": "599", "fix": "600"}, {"desc": "583", "fix": "601"}, {"desc": "602", "fix": "603"}, {"desc": "604", "fix": "605"}, {"desc": "606", "fix": "607"}, {"desc": "579", "fix": "608"}, {"desc": "579", "fix": "609"}, {"desc": "610", "fix": "611"}, {"desc": "591", "fix": "612"}, {"desc": "613", "fix": "614"}, {"desc": "615", "fix": "616"}, {"desc": "587", "fix": "617"}, "Update the dependencies array to be: [location.state]", {"range": "618", "text": "619"}, "Update the dependencies array to be: [fetchEvents, startLoading, stopLoading, user]", {"range": "620", "text": "621"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user, x]", {"range": "622", "text": "623"}, "Update the dependencies array to be: [fullMessage]", {"range": "624", "text": "625"}, "Update the dependencies array to be: [startLoading, stopLoading, user]", {"range": "626", "text": "627"}, {"range": "628", "text": "627"}, {"range": "629", "text": "627"}, "Update the dependencies array to be: [loading, user]", {"range": "630", "text": "631"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user]", {"range": "632", "text": "633"}, "Update the dependencies array to be: [dataHis, listProductWereAdded, setIdProductAdded]", {"range": "634", "text": "635"}, "Update the dependencies array to be: [c, startLoading, stopLoading]", {"range": "636", "text": "637"}, "Update the dependencies array to be: [user]", {"range": "638", "text": "639"}, "Update the dependencies array to be: [searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", {"range": "640", "text": "641"}, "Update the dependencies array to be: [getData, getSupplierByOrderId, idOrder, loading]", {"range": "642", "text": "643"}, "Update the dependencies array to be: [loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", {"range": "644", "text": "645"}, "Update the dependencies array to be: [debouncedFetchSuggestions, searchTerm]", {"range": "646", "text": "647"}, {"range": "648", "text": "631"}, "Update the dependencies array to be: [loading, reload, startLoading, stopLoading, user, x]", {"range": "649", "text": "650"}, "Update the dependencies array to be: [customer, startLoading, stopLoading, supplier, user]", {"range": "651", "text": "652"}, "Update the dependencies array to be: [startLoading, stopLoading, supplier, user, x]", {"range": "653", "text": "654"}, {"range": "655", "text": "627"}, {"range": "656", "text": "627"}, "Update the dependencies array to be: [user, loading, fetchProducts]", {"range": "657", "text": "658"}, {"range": "659", "text": "639"}, "Update the dependencies array to be: [isOpen, toggleModal]", {"range": "660", "text": "661"}, "Update the dependencies array to be: [loading, user.id_owner]", {"range": "662", "text": "663"}, {"range": "664", "text": "635"}, [668, 670], "[location.state]", [1984, 1990], "[fetchEvents, startLoading, stopLoading, user]", [2221, 2233], "[loading, startLoading, stopLoading, user, x]", [1417, 1419], "[fullMessage]", [2697, 2703], "[startLoading, stopLoading, user]", [1388, 1394], [1012, 1018], [8608, 8617], "[loading, user]", [28450, 28465], "[loading, startLoading, stopLoading, user]", [37985, 37994], "[dataHis, listProductWereAdded, setIdProductAdded]", [1972, 1975], "[c, startLoading, stopLoading]", [2040, 2042], "[user]", [2705, 2747], "[searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", [3134, 3152], "[getData, getSupplierByOrderId, idOrder, loading]", [3694, 3720], "[loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", [3807, 3819], "[debouncedFetchSuggestions, searchTerm]", [2404, 2413], [1763, 1772], "[loading, reload, startLoading, stopLoading, user, x]", [1328, 1330], "[customer, startLoading, stopLoading, supplier, user]", [2027, 2030], "[startLoading, stopLoading, supplier, user, x]", [1271, 1273], [1458, 1460], [1894, 1909], "[user, loading, fetchProducts]", [2054, 2056], [782, 790], "[isOpen, toggleModal]", [2435, 2444], "[loading, user.id_owner]", [12037, 12046]]