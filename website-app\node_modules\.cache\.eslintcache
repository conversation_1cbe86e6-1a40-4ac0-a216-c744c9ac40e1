[{"F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js": "1", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js": "2", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js": "3", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js": "4", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js": "5", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js": "6", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js": "7", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js": "8", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js": "9", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js": "10", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js": "11", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js": "12", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js": "13", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js": "14", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js": "15", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js": "16", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js": "17", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js": "18", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js": "19", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js": "20", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js": "21", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js": "22", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js": "23", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js": "24", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js": "25", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js": "26", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js": "27", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js": "28", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js": "29", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js": "30", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js": "31", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js": "32", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js": "33", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js": "34", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js": "35", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js": "36", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js": "37", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js": "38", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js": "39", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js": "40", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js": "41", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js": "42", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js": "43", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js": "44", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js": "45", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js": "46", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js": "47", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js": "48", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js": "49", "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js": "50"}, {"size": 873, "mtime": 1748099557892, "results": "51", "hashOfConfig": "52"}, {"size": 2080, "mtime": 1748099557807, "results": "53", "hashOfConfig": "52"}, {"size": 375, "mtime": 1748099557918, "results": "54", "hashOfConfig": "52"}, {"size": 873, "mtime": 1748099557881, "results": "55", "hashOfConfig": "52"}, {"size": 1324, "mtime": 1748099557880, "results": "56", "hashOfConfig": "52"}, {"size": 2947, "mtime": 1748099557881, "results": "57", "hashOfConfig": "52"}, {"size": 778, "mtime": 1748099557888, "results": "58", "hashOfConfig": "52"}, {"size": 2107, "mtime": 1748099557862, "results": "59", "hashOfConfig": "52"}, {"size": 9872, "mtime": 1748502443203, "results": "60", "hashOfConfig": "52"}, {"size": 12386, "mtime": 1748502462807, "results": "61", "hashOfConfig": "52"}, {"size": 1975, "mtime": 1748099557914, "results": "62", "hashOfConfig": "52"}, {"size": 16874, "mtime": 1748502443083, "results": "63", "hashOfConfig": "52"}, {"size": 5142, "mtime": 1748502443233, "results": "64", "hashOfConfig": "52"}, {"size": 5569, "mtime": 1748099557913, "results": "65", "hashOfConfig": "52"}, {"size": 203, "mtime": 1748099557903, "results": "66", "hashOfConfig": "52"}, {"size": 40378, "mtime": 1748502700516, "results": "67", "hashOfConfig": "52"}, {"size": 13202, "mtime": 1748502443937, "results": "68", "hashOfConfig": "52"}, {"size": 550, "mtime": 1748099557908, "results": "69", "hashOfConfig": "52"}, {"size": 50873, "mtime": 1748502443174, "results": "70", "hashOfConfig": "52"}, {"size": 173, "mtime": 1748099557897, "results": "71", "hashOfConfig": "52"}, {"size": 2075, "mtime": 1748099557894, "results": "72", "hashOfConfig": "52"}, {"size": 1378, "mtime": 1748099557911, "results": "73", "hashOfConfig": "52"}, {"size": 181, "mtime": 1748099557890, "results": "74", "hashOfConfig": "52"}, {"size": 3627, "mtime": 1748502443517, "results": "75", "hashOfConfig": "52"}, {"size": 2603, "mtime": 1748502443527, "results": "76", "hashOfConfig": "52"}, {"size": 2385, "mtime": 1748099557919, "results": "77", "hashOfConfig": "52"}, {"size": 5232, "mtime": 1748502443971, "results": "78", "hashOfConfig": "52"}, {"size": 2838, "mtime": 1748502444197, "results": "79", "hashOfConfig": "52"}, {"size": 5291, "mtime": 1748099557858, "results": "80", "hashOfConfig": "52"}, {"size": 19243, "mtime": 1748502443913, "results": "81", "hashOfConfig": "52"}, {"size": 7039, "mtime": 1748502443954, "results": "82", "hashOfConfig": "52"}, {"size": 16375, "mtime": 1748502443393, "results": "83", "hashOfConfig": "52"}, {"size": 863, "mtime": 1748099557809, "results": "84", "hashOfConfig": "52"}, {"size": 8469, "mtime": 1748503785329, "results": "85", "hashOfConfig": "52"}, {"size": 13085, "mtime": 1748502443161, "results": "86", "hashOfConfig": "52"}, {"size": 17984, "mtime": 1748502444191, "results": "87", "hashOfConfig": "52"}, {"size": 7395, "mtime": 1748502443746, "results": "88", "hashOfConfig": "52"}, {"size": 5426, "mtime": 1748502443913, "results": "89", "hashOfConfig": "52"}, {"size": 14255, "mtime": 1748502444062, "results": "90", "hashOfConfig": "52"}, {"size": 616, "mtime": 1748099557814, "results": "91", "hashOfConfig": "52"}, {"size": 7289, "mtime": 1748099557872, "results": "92", "hashOfConfig": "52"}, {"size": 926, "mtime": 1748099557847, "results": "93", "hashOfConfig": "52"}, {"size": 6584, "mtime": 1748502443795, "results": "94", "hashOfConfig": "52"}, {"size": 6983, "mtime": 1748502443966, "results": "95", "hashOfConfig": "52"}, {"size": 1839, "mtime": 1748099557852, "results": "96", "hashOfConfig": "52"}, {"size": 4217, "mtime": 1748502443795, "results": "97", "hashOfConfig": "52"}, {"size": 258, "mtime": 1748099557848, "results": "98", "hashOfConfig": "52"}, {"size": 22766, "mtime": 1748502443451, "results": "99", "hashOfConfig": "52"}, {"size": 6277, "mtime": 1748502443935, "results": "100", "hashOfConfig": "52"}, {"size": 2375, "mtime": 1748099557861, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uca01g", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\App.js", ["252"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\reportWebVitals.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Loading.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\AuthContext.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\Main_intro.js", ["253"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\protect.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Notification\\notification.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Calendar\\index.js", ["254", "255"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\index.js", ["256", "257", "258", "259", "260", "261", "262", "263", "264", "265"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Surprised\\index.js", ["266"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageAccount\\index.js", ["267", "268"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Permission\\index.js", ["269", "270", "271", "272"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\RolesGroup\\index.js", ["273", "274"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\ManageProduct\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\index.js", ["275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\intro.js", ["286", "287", "288", "289", "290", "291", "292"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Page404\\index.js", ["293", "294"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\index.js", ["295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305", "306", "307", "308", "309", "310"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Export\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\layouts\\LayoutDefault\\index.js", ["311", "312"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Profile\\image.js", ["313", "314", "315"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\useAuth.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\sale_daily.js", ["316"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\useronlinecard.js", ["317"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\services\\Roles\\rolesService.js", ["318"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\forgot_password.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\introduce\\resetpassword.js", ["319"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\index.js", ["320"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\ProductForm.js", ["321", "322"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalHistory.js", ["323", "324", "325"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\Import\\ModalDetail.js", ["326", "327", "328"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Avatar\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\pages\\home\\chat.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\test\\index.js", ["329", "330", "331", "332", "333", "334", "335", "336"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\main.js", ["337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\item.js", ["351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\history.js", ["362", "363"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\form_show.js", ["364", "365", "366", "367", "368", "369", "370"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\ComponentExport\\Modal\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Sidebar\\index.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\index.js", ["371", "372", "373"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\history.js", ["374", "375"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\thanh_toan.js", ["376", "377"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Form_delete.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\noti.js", ["378"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Header\\music.js", [], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Manage_product\\Product_detail.js", ["379", "380", "381", "382", "383", "384", "385"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\export\\formcustomer.js", ["386", "387", "388", "389"], [], "F:\\File\\Web\\BTLNMCNNPM\\Front\\NMCNPM_IT3180_20241\\website-app\\src\\components\\Modal\\index.js", ["390"], [], {"ruleId": "391", "severity": 1, "message": "392", "line": 13, "column": 8, "nodeType": "393", "messageId": "394", "endLine": 13, "endColumn": 15}, {"ruleId": "395", "severity": 1, "message": "396", "line": 22, "column": 6, "nodeType": "397", "endLine": 22, "endColumn": 8, "suggestions": "398"}, {"ruleId": "391", "severity": 1, "message": "399", "line": 23, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 23, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "400", "line": 64, "column": 6, "nodeType": "397", "endLine": 64, "endColumn": 12, "suggestions": "401"}, {"ruleId": "391", "severity": 1, "message": "402", "line": 21, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 21, "endColumn": 17}, {"ruleId": "395", "severity": 1, "message": "403", "line": 60, "column": 6, "nodeType": "397", "endLine": 60, "endColumn": 18, "suggestions": "404"}, {"ruleId": "405", "severity": 1, "message": "406", "line": 94, "column": 19, "nodeType": "407", "messageId": "408", "endLine": 94, "endColumn": 21}, {"ruleId": "405", "severity": 1, "message": "406", "line": 127, "column": 19, "nodeType": "407", "messageId": "408", "endLine": 127, "endColumn": 21}, {"ruleId": "409", "severity": 1, "message": "410", "line": 212, "column": 13, "nodeType": "411", "endLine": 212, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "410", "line": 217, "column": 13, "nodeType": "411", "endLine": 217, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "410", "line": 222, "column": 13, "nodeType": "411", "endLine": 222, "endColumn": 25}, {"ruleId": "405", "severity": 1, "message": "412", "line": 227, "column": 31, "nodeType": "407", "messageId": "408", "endLine": 227, "endColumn": 33}, {"ruleId": "409", "severity": 1, "message": "410", "line": 234, "column": 13, "nodeType": "411", "endLine": 234, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "410", "line": 239, "column": 13, "nodeType": "411", "endLine": 239, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "413", "line": 33, "column": 6, "nodeType": "397", "endLine": 33, "endColumn": 8, "suggestions": "414"}, {"ruleId": "391", "severity": 1, "message": "415", "line": 20, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 20, "endColumn": 17}, {"ruleId": "395", "severity": 1, "message": "416", "line": 88, "column": 6, "nodeType": "397", "endLine": 88, "endColumn": 12, "suggestions": "417"}, {"ruleId": "395", "severity": 1, "message": "416", "line": 47, "column": 6, "nodeType": "397", "endLine": 47, "endColumn": 12, "suggestions": "418"}, {"ruleId": "405", "severity": 1, "message": "412", "line": 94, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 94, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 95, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 95, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 96, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 96, "endColumn": 24}, {"ruleId": "391", "severity": 1, "message": "399", "line": 9, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 9, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "416", "line": 26, "column": 6, "nodeType": "397", "endLine": 26, "endColumn": 12, "suggestions": "419"}, {"ruleId": "391", "severity": 1, "message": "420", "line": 33, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 33, "endColumn": 14}, {"ruleId": "395", "severity": 1, "message": "421", "line": 314, "column": 6, "nodeType": "397", "endLine": 314, "endColumn": 15, "suggestions": "422"}, {"ruleId": "409", "severity": 1, "message": "410", "line": 326, "column": 15, "nodeType": "411", "endLine": 326, "endColumn": 27}, {"ruleId": "409", "severity": 1, "message": "410", "line": 327, "column": 15, "nodeType": "411", "endLine": 327, "endColumn": 27}, {"ruleId": "409", "severity": 1, "message": "410", "line": 466, "column": 23, "nodeType": "411", "endLine": 469, "endColumn": 24}, {"ruleId": "409", "severity": 1, "message": "410", "line": 475, "column": 23, "nodeType": "411", "endLine": 478, "endColumn": 24}, {"ruleId": "423", "severity": 1, "message": "424", "line": 530, "column": 19, "nodeType": "411", "messageId": "425", "endLine": 530, "endColumn": 34}, {"ruleId": "409", "severity": 1, "message": "410", "line": 1024, "column": 17, "nodeType": "411", "endLine": 1024, "endColumn": 50}, {"ruleId": "409", "severity": 1, "message": "410", "line": 1029, "column": 17, "nodeType": "411", "endLine": 1029, "endColumn": 50}, {"ruleId": "409", "severity": 1, "message": "410", "line": 1035, "column": 17, "nodeType": "411", "endLine": 1035, "endColumn": 50}, {"ruleId": "426", "severity": 1, "message": "427", "line": 1048, "column": 13, "nodeType": "411", "messageId": "428", "endLine": 1048, "endColumn": 63, "fix": "429"}, {"ruleId": "423", "severity": 1, "message": "430", "line": 251, "column": 9, "nodeType": "411", "messageId": "425", "endLine": 256, "endColumn": 11}, {"ruleId": "423", "severity": 1, "message": "431", "line": 259, "column": 9, "nodeType": "411", "messageId": "425", "endLine": 266, "endColumn": 11}, {"ruleId": "409", "severity": 1, "message": "410", "line": 287, "column": 47, "nodeType": "411", "endLine": 287, "endColumn": 59}, {"ruleId": "409", "severity": 1, "message": "410", "line": 288, "column": 51, "nodeType": "411", "endLine": 288, "endColumn": 63}, {"ruleId": "432", "severity": 1, "message": "433", "line": 313, "column": 19, "nodeType": "411", "endLine": 313, "endColumn": 66}, {"ruleId": "409", "severity": 1, "message": "434", "line": 386, "column": 17, "nodeType": "411", "endLine": 390, "endColumn": 18}, {"ruleId": "409", "severity": 1, "message": "434", "line": 402, "column": 17, "nodeType": "411", "endLine": 407, "endColumn": 18}, {"ruleId": "409", "severity": 1, "message": "410", "line": 10, "column": 9, "nodeType": "411", "endLine": 10, "endColumn": 69}, {"ruleId": "435", "severity": 1, "message": "436", "line": 10, "column": 17, "nodeType": "437", "messageId": "438", "endLine": 10, "endColumn": 44}, {"ruleId": "395", "severity": 1, "message": "416", "line": 748, "column": 6, "nodeType": "397", "endLine": 748, "endColumn": 21, "suggestions": "439"}, {"ruleId": "391", "severity": 1, "message": "440", "line": 802, "column": 13, "nodeType": "393", "messageId": "394", "endLine": 802, "endColumn": 17}, {"ruleId": "395", "severity": 1, "message": "441", "line": 814, "column": 37, "nodeType": "393", "endLine": 814, "endColumn": 48}, {"ruleId": "405", "severity": 1, "message": "412", "line": 823, "column": 58, "nodeType": "407", "messageId": "408", "endLine": 823, "endColumn": 60}, {"ruleId": "391", "severity": 1, "message": "399", "line": 1001, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 1001, "endColumn": 24}, {"ruleId": "391", "severity": 1, "message": "442", "line": 1003, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 1003, "endColumn": 17}, {"ruleId": "391", "severity": 1, "message": "443", "line": 1005, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 1005, "endColumn": 32}, {"ruleId": "391", "severity": 1, "message": "444", "line": 1008, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 1008, "endColumn": 26}, {"ruleId": "391", "severity": 1, "message": "445", "line": 1011, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 1011, "endColumn": 20}, {"ruleId": "391", "severity": 1, "message": "446", "line": 1011, "column": 22, "nodeType": "393", "messageId": "394", "endLine": 1011, "endColumn": 35}, {"ruleId": "391", "severity": 1, "message": "447", "line": 1015, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 1015, "endColumn": 16}, {"ruleId": "395", "severity": 1, "message": "448", "line": 1032, "column": 6, "nodeType": "397", "endLine": 1032, "endColumn": 15, "suggestions": "449"}, {"ruleId": "391", "severity": 1, "message": "450", "line": 1033, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 1033, "endColumn": 29}, {"ruleId": "391", "severity": 1, "message": "451", "line": 1054, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 1054, "endColumn": 28}, {"ruleId": "391", "severity": 1, "message": "452", "line": 1068, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 1068, "endColumn": 23}, {"ruleId": "391", "severity": 1, "message": "453", "line": 1077, "column": 9, "nodeType": "393", "messageId": "394", "endLine": 1077, "endColumn": 28}, {"ruleId": "391", "severity": 1, "message": "454", "line": 4, "column": 27, "nodeType": "393", "messageId": "394", "endLine": 4, "endColumn": 36}, {"ruleId": "405", "severity": 1, "message": "412", "line": 14, "column": 13, "nodeType": "407", "messageId": "408", "endLine": 14, "endColumn": 15}, {"ruleId": "391", "severity": 1, "message": "454", "line": 1, "column": 24, "nodeType": "393", "messageId": "394", "endLine": 1, "endColumn": 33}, {"ruleId": "391", "severity": 1, "message": "455", "line": 3, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 3, "endColumn": 16}, {"ruleId": "426", "severity": 1, "message": "427", "line": 12, "column": 9, "nodeType": "411", "messageId": "428", "endLine": 12, "endColumn": 41, "fix": "456"}, {"ruleId": "395", "severity": 1, "message": "421", "line": 64, "column": 6, "nodeType": "397", "endLine": 64, "endColumn": 15, "suggestions": "457"}, {"ruleId": "395", "severity": 1, "message": "421", "line": 62, "column": 6, "nodeType": "397", "endLine": 62, "endColumn": 15, "suggestions": "458"}, {"ruleId": "405", "severity": 1, "message": "412", "line": 67, "column": 20, "nodeType": "407", "messageId": "408", "endLine": 67, "endColumn": 22}, {"ruleId": "405", "severity": 1, "message": "406", "line": 9, "column": 18, "nodeType": "407", "messageId": "408", "endLine": 9, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "416", "line": 72, "column": 6, "nodeType": "397", "endLine": 72, "endColumn": 9, "suggestions": "459"}, {"ruleId": "391", "severity": 1, "message": "399", "line": 10, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 10, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "421", "line": 59, "column": 6, "nodeType": "397", "endLine": 59, "endColumn": 8, "suggestions": "460"}, {"ruleId": "391", "severity": 1, "message": "461", "line": 25, "column": 12, "nodeType": "393", "messageId": "394", "endLine": 25, "endColumn": 23}, {"ruleId": "395", "severity": 1, "message": "441", "line": 60, "column": 39, "nodeType": "393", "endLine": 60, "endColumn": 50}, {"ruleId": "395", "severity": 1, "message": "462", "line": 93, "column": 8, "nodeType": "397", "endLine": 93, "endColumn": 50, "suggestions": "463"}, {"ruleId": "391", "severity": 1, "message": "464", "line": 3, "column": 46, "nodeType": "393", "messageId": "394", "endLine": 3, "endColumn": 56}, {"ruleId": "391", "severity": 1, "message": "465", "line": 4, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 4, "endColumn": 21}, {"ruleId": "395", "severity": 1, "message": "466", "line": 98, "column": 6, "nodeType": "397", "endLine": 98, "endColumn": 24, "suggestions": "467"}, {"ruleId": "391", "severity": 1, "message": "464", "line": 5, "column": 3, "nodeType": "393", "messageId": "394", "endLine": 5, "endColumn": 13}, {"ruleId": "391", "severity": 1, "message": "465", "line": 10, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 10, "endColumn": 21}, {"ruleId": "391", "severity": 1, "message": "468", "line": 36, "column": 13, "nodeType": "393", "messageId": "394", "endLine": 36, "endColumn": 25}, {"ruleId": "391", "severity": 1, "message": "469", "line": 36, "column": 27, "nodeType": "393", "messageId": "394", "endLine": 36, "endColumn": 38}, {"ruleId": "470", "severity": 1, "message": "471", "line": 95, "column": 11, "nodeType": "472", "messageId": "473", "endLine": 95, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "441", "line": 107, "column": 39, "nodeType": "393", "endLine": 107, "endColumn": 50}, {"ruleId": "395", "severity": 1, "message": "474", "line": 117, "column": 8, "nodeType": "397", "endLine": 117, "endColumn": 34, "suggestions": "475"}, {"ruleId": "395", "severity": 1, "message": "462", "line": 120, "column": 8, "nodeType": "397", "endLine": 120, "endColumn": 20, "suggestions": "476"}, {"ruleId": "391", "severity": 1, "message": "468", "line": 11, "column": 11, "nodeType": "393", "messageId": "394", "endLine": 11, "endColumn": 23}, {"ruleId": "391", "severity": 1, "message": "469", "line": 11, "column": 25, "nodeType": "393", "messageId": "394", "endLine": 11, "endColumn": 36}, {"ruleId": "405", "severity": 1, "message": "412", "line": 45, "column": 25, "nodeType": "407", "messageId": "408", "endLine": 45, "endColumn": 27}, {"ruleId": "405", "severity": 1, "message": "412", "line": 59, "column": 25, "nodeType": "407", "messageId": "408", "endLine": 59, "endColumn": 27}, {"ruleId": "395", "severity": 1, "message": "421", "line": 66, "column": 6, "nodeType": "397", "endLine": 66, "endColumn": 15, "suggestions": "477"}, {"ruleId": "405", "severity": 1, "message": "406", "line": 69, "column": 14, "nodeType": "407", "messageId": "408", "endLine": 69, "endColumn": 16}, {"ruleId": "405", "severity": 1, "message": "406", "line": 72, "column": 21, "nodeType": "407", "messageId": "408", "endLine": 72, "endColumn": 23}, {"ruleId": "405", "severity": 1, "message": "412", "line": 73, "column": 11, "nodeType": "407", "messageId": "408", "endLine": 73, "endColumn": 13}, {"ruleId": "405", "severity": 1, "message": "412", "line": 77, "column": 34, "nodeType": "407", "messageId": "408", "endLine": 77, "endColumn": 36}, {"ruleId": "405", "severity": 1, "message": "412", "line": 81, "column": 25, "nodeType": "407", "messageId": "408", "endLine": 81, "endColumn": 27}, {"ruleId": "405", "severity": 1, "message": "412", "line": 93, "column": 57, "nodeType": "407", "messageId": "408", "endLine": 93, "endColumn": 59}, {"ruleId": "405", "severity": 1, "message": "412", "line": 121, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 121, "endColumn": 17}, {"ruleId": "405", "severity": 1, "message": "406", "line": 154, "column": 40, "nodeType": "407", "messageId": "408", "endLine": 154, "endColumn": 42}, {"ruleId": "405", "severity": 1, "message": "406", "line": 388, "column": 36, "nodeType": "407", "messageId": "408", "endLine": 388, "endColumn": 38}, {"ruleId": "395", "severity": 1, "message": "478", "line": 62, "column": 6, "nodeType": "397", "endLine": 62, "endColumn": 15, "suggestions": "479"}, {"ruleId": "405", "severity": 1, "message": "412", "line": 95, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 95, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 98, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 98, "endColumn": 17}, {"ruleId": "405", "severity": 1, "message": "406", "line": 119, "column": 18, "nodeType": "407", "messageId": "408", "endLine": 119, "endColumn": 20}, {"ruleId": "405", "severity": 1, "message": "412", "line": 125, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 125, "endColumn": 17}, {"ruleId": "405", "severity": 1, "message": "412", "line": 131, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 131, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 137, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 137, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 140, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 140, "endColumn": 17}, {"ruleId": "405", "severity": 1, "message": "412", "line": 185, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 185, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 188, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 188, "endColumn": 17}, {"ruleId": "480", "severity": 1, "message": "481", "line": 224, "column": 15, "nodeType": "411", "endLine": 232, "endColumn": 17}, {"ruleId": "395", "severity": 1, "message": "482", "line": 41, "column": 6, "nodeType": "397", "endLine": 41, "endColumn": 8, "suggestions": "483"}, {"ruleId": "391", "severity": 1, "message": "484", "line": 44, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 44, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "485", "line": 58, "column": 6, "nodeType": "397", "endLine": 58, "endColumn": 9, "suggestions": "486"}, {"ruleId": "391", "severity": 1, "message": "484", "line": 64, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 64, "endColumn": 24}, {"ruleId": "391", "severity": 1, "message": "487", "line": 105, "column": 7, "nodeType": "393", "messageId": "394", "endLine": 105, "endColumn": 12}, {"ruleId": "405", "severity": 1, "message": "412", "line": 134, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 134, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 158, "column": 15, "nodeType": "407", "messageId": "408", "endLine": 158, "endColumn": 17}, {"ruleId": "405", "severity": 1, "message": "412", "line": 187, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 187, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "406", "line": 194, "column": 32, "nodeType": "407", "messageId": "408", "endLine": 194, "endColumn": 34}, {"ruleId": "391", "severity": 1, "message": "488", "line": 1, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 1, "endColumn": 25}, {"ruleId": "391", "severity": 1, "message": "489", "line": 2, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 2, "endColumn": 19}, {"ruleId": "391", "severity": 1, "message": "490", "line": 3, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 3, "endColumn": 19}, {"ruleId": "395", "severity": 1, "message": "403", "line": 41, "column": 6, "nodeType": "397", "endLine": 41, "endColumn": 8, "suggestions": "491"}, {"ruleId": "391", "severity": 1, "message": "484", "line": 44, "column": 10, "nodeType": "393", "messageId": "394", "endLine": 44, "endColumn": 24}, {"ruleId": "391", "severity": 1, "message": "399", "line": 23, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 23, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "403", "line": 45, "column": 6, "nodeType": "397", "endLine": 45, "endColumn": 8, "suggestions": "492"}, {"ruleId": "395", "severity": 1, "message": "493", "line": 57, "column": 6, "nodeType": "397", "endLine": 57, "endColumn": 21, "suggestions": "494"}, {"ruleId": "391", "severity": 1, "message": "469", "line": 8, "column": 25, "nodeType": "393", "messageId": "394", "endLine": 8, "endColumn": 36}, {"ruleId": "391", "severity": 1, "message": "399", "line": 9, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 9, "endColumn": 24}, {"ruleId": "395", "severity": 1, "message": "421", "line": 53, "column": 6, "nodeType": "397", "endLine": 53, "endColumn": 8, "suggestions": "495"}, {"ruleId": "405", "severity": 1, "message": "406", "line": 108, "column": 24, "nodeType": "407", "messageId": "408", "endLine": 108, "endColumn": 26}, {"ruleId": "405", "severity": 1, "message": "406", "line": 133, "column": 41, "nodeType": "407", "messageId": "408", "endLine": 133, "endColumn": 43}, {"ruleId": "480", "severity": 1, "message": "481", "line": 315, "column": 13, "nodeType": "411", "endLine": 323, "endColumn": 15}, {"ruleId": "480", "severity": 1, "message": "481", "line": 488, "column": 17, "nodeType": "411", "endLine": 492, "endColumn": 19}, {"ruleId": "391", "severity": 1, "message": "399", "line": 7, "column": 17, "nodeType": "393", "messageId": "394", "endLine": 7, "endColumn": 24}, {"ruleId": "405", "severity": 1, "message": "412", "line": 56, "column": 22, "nodeType": "407", "messageId": "408", "endLine": 56, "endColumn": 24}, {"ruleId": "432", "severity": 1, "message": "433", "line": 148, "column": 23, "nodeType": "411", "endLine": 151, "endColumn": 25}, {"ruleId": "496", "severity": 1, "message": "497", "line": 153, "column": 23, "nodeType": "411", "endLine": 153, "endColumn": 27}, {"ruleId": "395", "severity": 1, "message": "498", "line": 26, "column": 6, "nodeType": "397", "endLine": 26, "endColumn": 14, "suggestions": "499"}, "no-unused-vars", "'Cookies' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'location.state'. Either include it or remove the dependency array.", "ArrayExpression", ["500"], "'loading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchEvents', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array.", ["501"], "'refresh' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', and 'user'. Either include them or remove the dependency array.", ["502"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "Expected '===' and instead saw '=='.", "React Hook useEffect has a missing dependency: 'fullMessage'. Either include it or remove the dependency array.", ["503"], "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading' and 'stopLoading'. Either include them or remove the dependency array.", ["504"], ["505"], ["506"], "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["507"], "react/jsx-pascal-case", "Imported JSX component Sales_daily must be in PascalCase or SCREAMING_SNAKE_CASE", "usePascalOrSnakeCase", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "noTargetBlankWithoutNoreferrer", {"range": "508", "text": "509"}, "Imported JSX component Change_password must be in PascalCase or SCREAMING_SNAKE_CASE", "Imported JSX component Forgot_password must be in PascalCase or SCREAMING_SNAKE_CASE", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "no-script-url", "Script URL is a form of eval.", "Literal", "unexpectedScriptURL", ["510"], "'sugg' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'listItem' is assigned a value but never used.", "'isDropdownOpenSupplier' is assigned a value but never used.", "'selectedSupplier' is assigned a value but never used.", "'quantities' is assigned a value but never used.", "'setQuantities' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'listProductWereAdded' and 'setIdProductAdded'. Either include them or remove the dependency array. If 'setIdProductAdded' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["511"], "'handleSupplierChange' is assigned a value but never used.", "'handleSupplierClick' is assigned a value but never used.", "'toggleDropdown' is assigned a value but never used.", "'dropdownRefSupplier' is assigned a value but never used.", "'useEffect' is defined but never used.", "'notify' is defined but never used.", {"range": "512", "text": "509"}, ["513"], ["514"], ["515"], ["516"], "'selectedRow' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'debouncedFetchSuggestions'. Either include it or remove the dependency array.", ["517"], "'useContext' is defined but never used.", "'AuthContext' is defined but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'getSupplierByOrderId'. Either include them or remove the dependency array.", ["518"], "'startLoading' is assigned a value but never used.", "'stopLoading' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "React Hook useEffect has missing dependencies: 'debouncedFetchSuggestions' and 'searchTerm'. Either include them or remove the dependency array.", ["519"], ["520"], ["521"], "React Hook useEffect has missing dependencies: 'loading', 'reload', 'startLoading', and 'stopLoading'. Either include them or remove the dependency array. If 'reload' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["522"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "React Hook useEffect has missing dependencies: 'customer', 'startLoading', 'stopLoading', 'supplier', and 'user'. Either include them or remove the dependency array.", ["523"], "'selectedOrders' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'startLoading', 'stopLoading', 'supplier', and 'user'. Either include them or remove the dependency array.", ["524"], "'count' is assigned a value but never used.", "'RiSettings4Line' is defined but never used.", "'FaRegBell' is defined but never used.", "'FaRegUser' is defined but never used.", ["525"], ["526"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["527"], ["528"], "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "React Hook useEffect has a missing dependency: 'toggleModal'. Either include it or remove the dependency array.", ["529"], {"desc": "530", "fix": "531"}, {"desc": "532", "fix": "533"}, {"desc": "534", "fix": "535"}, {"desc": "536", "fix": "537"}, {"desc": "538", "fix": "539"}, {"desc": "538", "fix": "540"}, {"desc": "538", "fix": "541"}, {"desc": "542", "fix": "543"}, [40037, 40037], " rel=\"noreferrer\"", {"desc": "544", "fix": "545"}, {"desc": "546", "fix": "547"}, [559, 559], {"desc": "542", "fix": "548"}, {"desc": "542", "fix": "549"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "558", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "542", "fix": "562"}, {"desc": "563", "fix": "564"}, {"desc": "565", "fix": "566"}, {"desc": "567", "fix": "568"}, {"desc": "538", "fix": "569"}, {"desc": "538", "fix": "570"}, {"desc": "571", "fix": "572"}, {"desc": "552", "fix": "573"}, {"desc": "574", "fix": "575"}, "Update the dependencies array to be: [location.state]", {"range": "576", "text": "577"}, "Update the dependencies array to be: [fetchEvents, startLoading, stopLoading, user]", {"range": "578", "text": "579"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user, x]", {"range": "580", "text": "581"}, "Update the dependencies array to be: [fullMessage]", {"range": "582", "text": "583"}, "Update the dependencies array to be: [startLoading, stopLoading, user]", {"range": "584", "text": "585"}, {"range": "586", "text": "585"}, {"range": "587", "text": "585"}, "Update the dependencies array to be: [loading, user]", {"range": "588", "text": "589"}, "Update the dependencies array to be: [loading, startLoading, stopLoading, user]", {"range": "590", "text": "591"}, "Update the dependencies array to be: [dataHis, listProductWereAdded, setIdProductAdded]", {"range": "592", "text": "593"}, {"range": "594", "text": "589"}, {"range": "595", "text": "589"}, "Update the dependencies array to be: [c, startLoading, stopLoading]", {"range": "596", "text": "597"}, "Update the dependencies array to be: [user]", {"range": "598", "text": "599"}, "Update the dependencies array to be: [searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [getData, getSupplierByOrderId, idOrder, loading]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [debouncedFetchSuggestions, searchTerm]", {"range": "606", "text": "607"}, {"range": "608", "text": "589"}, "Update the dependencies array to be: [loading, reload, startLoading, stopLoading, user, x]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [customer, startLoading, stopLoading, supplier, user]", {"range": "611", "text": "612"}, "Update the dependencies array to be: [startLoading, stopLoading, supplier, user, x]", {"range": "613", "text": "614"}, {"range": "615", "text": "585"}, {"range": "616", "text": "585"}, "Update the dependencies array to be: [user, loading, fetchProducts]", {"range": "617", "text": "618"}, {"range": "619", "text": "599"}, "Update the dependencies array to be: [isOpen, toggleModal]", {"range": "620", "text": "621"}, [668, 670], "[location.state]", [1984, 1990], "[fetchEvents, startLoading, stopLoading, user]", [2221, 2233], "[loading, startLoading, stopLoading, user, x]", [1417, 1419], "[fullMessage]", [2697, 2703], "[startLoading, stopLoading, user]", [1388, 1394], [1012, 1018], [8458, 8467], "[loading, user]", [28450, 28465], "[loading, startLoading, stopLoading, user]", [37985, 37994], "[dataHis, listProductWereAdded, setIdProductAdded]", [1747, 1756], [1689, 1698], [1972, 1975], "[c, startLoading, stopLoading]", [2040, 2042], "[user]", [2705, 2747], "[searchTerm, page, loading, loadLog, user, debouncedFetchSuggestions]", [3134, 3152], "[getData, getSupplierByOrderId, idOrder, loading]", [3692, 3718], "[loading, loadOrder, user, debouncedFetchSuggestions, searchTerm]", [3805, 3817], "[debouncedFetchSuggestions, searchTerm]", [2404, 2413], [1763, 1772], "[loading, reload, startLoading, stopLoading, user, x]", [1328, 1330], "[customer, startLoading, stopLoading, supplier, user]", [2027, 2030], "[startLoading, stopLoading, supplier, user, x]", [1271, 1273], [1458, 1460], [1894, 1909], "[user, loading, fetchProducts]", [2054, 2056], [782, 790], "[isOpen, toggleModal]"]