{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\Permission\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./Permission.css\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { getRoles } from \"../../services/Roles/rolesService\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Permissions = () => {\n  _s();\n  const rights = [\"add_product\", \"edit_product\", \"delete_product\", \"create_order\", \"edit_order\", \"create-customer\", \"edit-customer\", \"create-suplier\", \"edit-suplier\", \"delete_suplier\", \"*role\"];\n  const [rolesData, setRolesData] = useState([]);\n  const {\n    user\n  } = useAuth();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [permissions, setPermissions] = useState({});\n  useEffect(() => {\n    const fetchRoles = async () => {\n      if (user) {\n        startLoading();\n        const roles = await getRoles(user.id_owner);\n        setRolesData(roles);\n        console.log(document.cookie);\n        console.log(\"OK\");\n        const initialPermissions = {};\n        roles.forEach(role => {\n          initialPermissions[role.role] = {\n            permissions: role.permissions || []\n          };\n        });\n        setPermissions(initialPermissions);\n        stopLoading();\n      }\n    };\n    fetchRoles();\n  }, [user]);\n  const handleCheckboxChange = (role, permission, checked) => {\n    setPermissions(prev => {\n      var _prev$role;\n      // Lấy các quyền hiện tại của vai trò này từ `prev`\n      const rolePermissions = ((_prev$role = prev[role]) === null || _prev$role === void 0 ? void 0 : _prev$role.permissions) || [];\n\n      // Tạo một bản sao của các quyền đã có, sau đó cập nhật\n      const updatedPermissions = checked ? [...rolePermissions, permission] // Thêm quyền nếu checkbox được chọn\n      : rolePermissions.filter(perm => perm !== permission); // Xóa quyền nếu checkbox bỏ chọn\n\n      // Trả về đối tượng cập nhật với quyền của vai trò đã được thay đổi\n      return {\n        ...prev,\n        [role]: {\n          permissions: updatedPermissions\n        }\n      };\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const rolesWithPermissions = rolesData.map(role => {\n      var _permissions$role$rol;\n      return {\n        _id: role._id,\n        permissions: ((_permissions$role$rol = permissions[role.role]) === null || _permissions$role$rol === void 0 ? void 0 : _permissions$role$rol.permissions) || []\n      };\n    });\n    await updatePermissions(rolesWithPermissions);\n  };\n  const updatePermissions = async rolesWithPermissions => {\n    try {\n      const response = await fetch(\"http://localhost:8080/api/roles/edit\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          rolesWithPermissions,\n          user: user\n        })\n      });\n      console.log(rolesWithPermissions);\n      const data = await response.json();\n      console.log(data);\n      if (data.message == \"Không có quyền truy cập\" || data.message == \"Vai trò không tồn tại\" || data.message == \"Lỗi máy chủ nội bộ\") {\n        notify(2, \"Lỗi khi cập nhật phân quyền\", \"Lỗi rồi ba\");\n      } else {\n        notify(1, \"Cập nhật quyền thành công\", \"Thành công\");\n      }\n    } catch (error) {\n      notify(2, \"Lỗi khi cập nhật phân quyền\", \"Lỗi rồi ba\");\n      console.error(\"Lỗi khi cập nhật phân quyền:\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    className: \"permissions-container\",\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Permission\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Thi\\u1EBFt l\\u1EADp ph\\xE2n quy\\u1EC1n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"uy-tabs\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"permissions-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              style: {\n                width: \"150px\"\n              },\n              children: \"T\\xEDnh n\\u0103ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), rolesData.map((role, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n              children: role.role\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: rights.map(perm => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: perm.charAt(0).toUpperCase() + perm.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), rolesData.map((role, index) => {\n              var _permissions$role$rol2;\n              return /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  onChange: e => handleCheckboxChange(role.role, perm, e.target.checked),\n                  checked: ((_permissions$role$rol2 = permissions[role.role]) === null || _permissions$role$rol2 === void 0 ? void 0 : _permissions$role$rol2.permissions.includes(perm)) || false\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this);\n            })]\n          }, perm, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        marginBottom: \"5px\",\n        color: \"red\"\n      },\n      children: [\"L\\u01B0u \\xFD khi trao quy\\u1EC1n \\\"*role\\\" v\\xEC n\\xF3 c\\xF3 th\\u1EC3 thao t\\xE1c \\u0111\\u01B0\\u1EE3c v\\u1EDBi quy\\u1EC1n(bao g\\u1ED3m th\\xEAm, ch\\u1EC9nh s\\u1EEDa, x\\xF3a)\", \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"update-btn\",\n      children: \"C\\u1EADp nh\\u1EADt\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(Permissions, \"1Ha8yV7IWmnY6m35I9CQJA7sEvM=\", false, function () {\n  return [useAuth, useLoading];\n});\n_c = Permissions;\nexport default Permissions;\nvar _c;\n$RefreshReg$(_c, \"Permissions\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "useLoading", "getRoles", "notify", "jsxDEV", "_jsxDEV", "Permissions", "_s", "rights", "rolesData", "setRolesData", "user", "startLoading", "stopLoading", "permissions", "setPermissions", "fetchRoles", "roles", "id_owner", "console", "log", "document", "cookie", "initialPermissions", "for<PERSON>ach", "role", "handleCheckboxChange", "permission", "checked", "prev", "_prev$role", "rolePermissions", "updatedPermissions", "filter", "perm", "handleSubmit", "e", "preventDefault", "rolesWithPermissions", "map", "_permissions$role$rol", "_id", "updatePermissions", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "message", "error", "className", "onSubmit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "index", "char<PERSON>t", "toUpperCase", "slice", "_permissions$role$rol2", "type", "onChange", "target", "includes", "marginBottom", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/Permission/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./Permission.css\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { getRoles } from \"../../services/Roles/rolesService\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\n\r\nconst Permissions = () => {\r\n  const rights = [\r\n    \"add_product\",\r\n    \"edit_product\",\r\n    \"delete_product\",\r\n    \"create_order\",\r\n    \"edit_order\",\r\n    \"create-customer\",\r\n    \"edit-customer\",\r\n    \"create-suplier\",\r\n    \"edit-suplier\",\r\n    \"delete_suplier\",\r\n    \"*role\",\r\n  ];\r\n  const [rolesData, setRolesData] = useState([]);\r\n  const { user } = useAuth();\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [permissions, setPermissions] = useState({});\r\n\r\n  useEffect(() => {\r\n    const fetchRoles = async () => {\r\n      if (user) {\r\n        startLoading();\r\n        const roles = await getRoles(user.id_owner);\r\n        setRolesData(roles);\r\n        console.log(document.cookie);\r\n        console.log(\"OK\");\r\n\r\n        const initialPermissions = {};\r\n        roles.forEach((role) => {\r\n          initialPermissions[role.role] = {\r\n            permissions: role.permissions || [],\r\n          };\r\n        });\r\n        setPermissions(initialPermissions);\r\n        stopLoading();\r\n      }\r\n    };\r\n    fetchRoles();\r\n  }, [user]);\r\n\r\n  const handleCheckboxChange = (role, permission, checked) => {\r\n    setPermissions((prev) => {\r\n      // Lấy các quyền hiện tại của vai trò này từ `prev`\r\n      const rolePermissions = prev[role]?.permissions || [];\r\n\r\n      // Tạo một bản sao của các quyền đã có, sau đó cập nhật\r\n      const updatedPermissions = checked\r\n        ? [...rolePermissions, permission] // Thêm quyền nếu checkbox được chọn\r\n        : rolePermissions.filter((perm) => perm !== permission); // Xóa quyền nếu checkbox bỏ chọn\r\n\r\n      // Trả về đối tượng cập nhật với quyền của vai trò đã được thay đổi\r\n      return {\r\n        ...prev,\r\n        [role]: { permissions: updatedPermissions },\r\n      };\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    const rolesWithPermissions = rolesData.map((role) => ({\r\n      _id: role._id,\r\n      permissions: permissions[role.role]?.permissions || [],\r\n    }));\r\n\r\n    await updatePermissions(rolesWithPermissions);\r\n  };\r\n\r\n  const updatePermissions = async (rolesWithPermissions) => {\r\n    try {\r\n      const response = await fetch(\"http://localhost:8080/api/roles/edit\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          rolesWithPermissions,\r\n          user: user,\r\n        }),\r\n      });\r\n      console.log(rolesWithPermissions);\r\n\r\n      const data = await response.json();\r\n      console.log(data);\r\n      if (\r\n        data.message == \"Không có quyền truy cập\" ||\r\n        data.message == \"Vai trò không tồn tại\" ||\r\n        data.message == \"Lỗi máy chủ nội bộ\"\r\n      ) {\r\n        notify(2, \"Lỗi khi cập nhật phân quyền\", \"Lỗi rồi ba\");\r\n      } else {\r\n        notify(1, \"Cập nhật quyền thành công\", \"Thành công\");\r\n      }\r\n    } catch (error) {\r\n      notify(2, \"Lỗi khi cập nhật phân quyền\", \"Lỗi rồi ba\");\r\n      console.error(\"Lỗi khi cập nhật phân quyền:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form className=\"permissions-container\" onSubmit={handleSubmit}>\r\n      <h2>Permission</h2>\r\n      <h3>Thiết lập phân quyền</h3>\r\n\r\n      <div className=\"uy-tabs\">\r\n        <table className=\"permissions-table\">\r\n          <thead>\r\n            <tr>\r\n              <th style={{ width: \"150px\" }}>Tính năng</th>\r\n              {rolesData.map((role, index) => (\r\n                <th key={index}>{role.role}</th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {rights.map((perm) => (\r\n              <tr key={perm}>\r\n                <td>{perm.charAt(0).toUpperCase() + perm.slice(1)}</td>\r\n                {rolesData.map((role, index) => (\r\n                  <td key={index}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      onChange={(e) =>\r\n                        handleCheckboxChange(role.role, perm, e.target.checked)\r\n                      }\r\n                      checked={\r\n                        permissions[role.role]?.permissions.includes(perm) ||\r\n                        false\r\n                      }\r\n                    />\r\n                  </td>\r\n                ))}\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <p style={{ marginBottom: \"5px\", color: \"red\" }}>\r\n        Lưu ý khi trao quyền \"*role\" vì nó có thể thao tác được với quyền(bao\r\n        gồm thêm, chỉnh sửa, xóa){\" \"}\r\n      </p>\r\n      <button type=\"submit\" className=\"update-btn\">\r\n        Cập nhật\r\n      </button>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default Permissions;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,kBAAkB;AACzB,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,MAAM,GAAG,CACb,aAAa,EACb,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,OAAO,CACR;EACD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM;IAAEY;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEY,YAAY;IAAEC;EAAY,CAAC,GAAGZ,UAAU,CAAC,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAElDD,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAIL,IAAI,EAAE;QACRC,YAAY,CAAC,CAAC;QACd,MAAMK,KAAK,GAAG,MAAMf,QAAQ,CAACS,IAAI,CAACO,QAAQ,CAAC;QAC3CR,YAAY,CAACO,KAAK,CAAC;QACnBE,OAAO,CAACC,GAAG,CAACC,QAAQ,CAACC,MAAM,CAAC;QAC5BH,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;QAEjB,MAAMG,kBAAkB,GAAG,CAAC,CAAC;QAC7BN,KAAK,CAACO,OAAO,CAAEC,IAAI,IAAK;UACtBF,kBAAkB,CAACE,IAAI,CAACA,IAAI,CAAC,GAAG;YAC9BX,WAAW,EAAEW,IAAI,CAACX,WAAW,IAAI;UACnC,CAAC;QACH,CAAC,CAAC;QACFC,cAAc,CAACQ,kBAAkB,CAAC;QAClCV,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IACDG,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC;EAEV,MAAMe,oBAAoB,GAAGA,CAACD,IAAI,EAAEE,UAAU,EAAEC,OAAO,KAAK;IAC1Db,cAAc,CAAEc,IAAI,IAAK;MAAA,IAAAC,UAAA;MACvB;MACA,MAAMC,eAAe,GAAG,EAAAD,UAAA,GAAAD,IAAI,CAACJ,IAAI,CAAC,cAAAK,UAAA,uBAAVA,UAAA,CAAYhB,WAAW,KAAI,EAAE;;MAErD;MACA,MAAMkB,kBAAkB,GAAGJ,OAAO,GAC9B,CAAC,GAAGG,eAAe,EAAEJ,UAAU,CAAC,CAAC;MAAA,EACjCI,eAAe,CAACE,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKP,UAAU,CAAC,CAAC,CAAC;;MAE3D;MACA,OAAO;QACL,GAAGE,IAAI;QACP,CAACJ,IAAI,GAAG;UAAEX,WAAW,EAAEkB;QAAmB;MAC5C,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,oBAAoB,GAAG7B,SAAS,CAAC8B,GAAG,CAAEd,IAAI;MAAA,IAAAe,qBAAA;MAAA,OAAM;QACpDC,GAAG,EAAEhB,IAAI,CAACgB,GAAG;QACb3B,WAAW,EAAE,EAAA0B,qBAAA,GAAA1B,WAAW,CAACW,IAAI,CAACA,IAAI,CAAC,cAAAe,qBAAA,uBAAtBA,qBAAA,CAAwB1B,WAAW,KAAI;MACtD,CAAC;IAAA,CAAC,CAAC;IAEH,MAAM4B,iBAAiB,CAACJ,oBAAoB,CAAC;EAC/C,CAAC;EAED,MAAMI,iBAAiB,GAAG,MAAOJ,oBAAoB,IAAK;IACxD,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBX,oBAAoB;UACpB3B,IAAI,EAAEA;QACR,CAAC;MACH,CAAC,CAAC;MACFQ,OAAO,CAACC,GAAG,CAACkB,oBAAoB,CAAC;MAEjC,MAAMY,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClChC,OAAO,CAACC,GAAG,CAAC8B,IAAI,CAAC;MACjB,IACEA,IAAI,CAACE,OAAO,IAAI,yBAAyB,IACzCF,IAAI,CAACE,OAAO,IAAI,uBAAuB,IACvCF,IAAI,CAACE,OAAO,IAAI,oBAAoB,EACpC;QACAjD,MAAM,CAAC,CAAC,EAAE,6BAA6B,EAAE,YAAY,CAAC;MACxD,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAE,2BAA2B,EAAE,YAAY,CAAC;MACtD;IACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdlD,MAAM,CAAC,CAAC,EAAE,6BAA6B,EAAE,YAAY,CAAC;MACtDgB,OAAO,CAACkC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,oBACEhD,OAAA;IAAMiD,SAAS,EAAC,uBAAuB;IAACC,QAAQ,EAAEpB,YAAa;IAAAqB,QAAA,gBAC7DnD,OAAA;MAAAmD,QAAA,EAAI;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnBvD,OAAA;MAAAmD,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE7BvD,OAAA;MAAKiD,SAAS,EAAC,SAAS;MAAAE,QAAA,eACtBnD,OAAA;QAAOiD,SAAS,EAAC,mBAAmB;QAAAE,QAAA,gBAClCnD,OAAA;UAAAmD,QAAA,eACEnD,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAIwD,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAQ,CAAE;cAAAN,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC5CnD,SAAS,CAAC8B,GAAG,CAAC,CAACd,IAAI,EAAEsC,KAAK,kBACzB1D,OAAA;cAAAmD,QAAA,EAAiB/B,IAAI,CAACA;YAAI,GAAjBsC,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRvD,OAAA;UAAAmD,QAAA,EACGhD,MAAM,CAAC+B,GAAG,CAAEL,IAAI,iBACf7B,OAAA;YAAAmD,QAAA,gBACEnD,OAAA;cAAAmD,QAAA,EAAKtB,IAAI,CAAC8B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/B,IAAI,CAACgC,KAAK,CAAC,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACtDnD,SAAS,CAAC8B,GAAG,CAAC,CAACd,IAAI,EAAEsC,KAAK;cAAA,IAAAI,sBAAA;cAAA,oBACzB9D,OAAA;gBAAAmD,QAAA,eACEnD,OAAA;kBACE+D,IAAI,EAAC,UAAU;kBACfC,QAAQ,EAAGjC,CAAC,IACVV,oBAAoB,CAACD,IAAI,CAACA,IAAI,EAAES,IAAI,EAAEE,CAAC,CAACkC,MAAM,CAAC1C,OAAO,CACvD;kBACDA,OAAO,EACL,EAAAuC,sBAAA,GAAArD,WAAW,CAACW,IAAI,CAACA,IAAI,CAAC,cAAA0C,sBAAA,uBAAtBA,sBAAA,CAAwBrD,WAAW,CAACyD,QAAQ,CAACrC,IAAI,CAAC,KAClD;gBACD;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAVKG,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CAAC;YAAA,CACN,CAAC;UAAA,GAfK1B,IAAI;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBT,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNvD,OAAA;MAAGwD,KAAK,EAAE;QAAEW,YAAY,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAjB,QAAA,GAAC,+KAEtB,EAAC,GAAG;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACJvD,OAAA;MAAQ+D,IAAI,EAAC,QAAQ;MAACd,SAAS,EAAC,YAAY;MAAAE,QAAA,EAAC;IAE7C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACrD,EAAA,CAnJID,WAAW;EAAA,QAeEN,OAAO,EACcC,UAAU;AAAA;AAAAyE,EAAA,GAhB5CpE,WAAW;AAqJjB,eAAeA,WAAW;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}