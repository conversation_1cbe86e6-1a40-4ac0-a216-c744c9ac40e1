{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\ManageAccount\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from \"react\";\nimport \"./ManageAccount.css\";\nimport { getRoles } from \"../../services/Roles/rolesService\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useNavigate } from \"react-router-dom\";\n\n// Icons\nimport { FaSearch, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaTimes, FaCheck, FaUser, FaEnvelope, FaLock, FaUserTag, FaCode, FaRefreshCw, FaSort, FaSortUp, FaSortDown, Fa<PERSON>ilter, Fa<PERSON><PERSON>s, FaShieldAlt } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManageAccount = () => {\n  _s();\n  // State management\n  const [accounts, setAccounts] = useState([]);\n  const [rolesData, setRolesData] = useState([]);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [filterRole, setFilterRole] = useState(\"\");\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState(null);\n\n  // Form states\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    role: \"\",\n    code: \"\"\n  });\n  const [confirmOtp, setConfirmOtp] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n\n  // Hooks\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const dropdownRef = useRef(null);\n\n  // API Functions\n  const getAccounts = useCallback(async userId => {\n    if (!userId) {\n      console.error(\"Lỗi: userId không hợp lệ!\");\n      return;\n    }\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/list?userId=${userId}`, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Network response was not ok: ${response.statusText}`);\n      }\n      const data = await response.json();\n      setAccounts(data);\n    } catch (error) {\n      console.error(\"Lỗi khi gọi API:\", error);\n      notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\n    } finally {\n      stopLoading();\n    }\n  }, [startLoading, stopLoading]);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setShowMenuIndex(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const fetchRoles = async () => {\n      if (user) {\n        startLoading();\n        await getAccounts(user.id_owner);\n        const roles = await getRoles(user.id_owner);\n        setRolesData(roles);\n        stopLoading();\n        setFormData(prevData => ({\n          ...prevData,\n          id_owner: user._id\n        })); // cập nhật id_owner\n      }\n    };\n    fetchRoles();\n  }, [user]);\n  const toggleMenu = index => {\n    setShowMenuIndex(showMenuIndex === index ? null : index);\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // ✅ Đảm bảo accounts luôn là array trước khi filter\n  const filteredAccounts = Array.isArray(accounts) ? accounts.filter(account => {\n    const name = account.name ? account.name.toLowerCase() : \"\";\n    const email = account.email ? account.email.toLowerCase() : \"\";\n    const role = account.role ? account.role.toLowerCase() : \"\";\n    return name.includes(searchTerm.toLowerCase()) || email.includes(searchTerm.toLowerCase()) || role.includes(searchTerm.toLowerCase());\n  }) : []; // Trả về array rỗng nếu accounts không phải array\n\n  const handleCreateAccount = async e => {\n    e.preventDefault();\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: formData.role,\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      console.log(data);\n      if (confirmOtp) {\n        if (data.message === \"Staff is created successfully\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\n        }\n      } else {\n        if (data.message === \"Confirmation code sent\") {\n          setConfirmOtp(true);\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n        } else if (data.message === \"User_new updated successfully!\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const sentAgain = async () => {\n    setConfirmOtp(true);\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: user ? user.role : \"\",\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      // Khi gửi mã xác nhận\n      if (data.message === \"Confirmation code sent\") {\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\n        setFormData(prev => ({\n          ...prev,\n          code: \"\"\n        }));\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n      } else {\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const handleOpenEditModal = account => {\n    setFormData({\n      id: account._id,\n      name: account.name,\n      email: account.email,\n      role: account.role,\n      password: account.password // Assuming password can be left blank for editing\n    });\n    setShowEditModal(true);\n  };\n  const handleEditAccount = async e => {\n    e.preventDefault();\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${formData.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          role: formData.role\n        })\n      });\n      const data = await response.json();\n      console.log(\"Success:\", data);\n      stopLoading();\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\n      await getAccounts(user.id_owner);\n      setShowModal(false);\n    } catch (error) {\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\n      console.error(\"Error edit:\", error);\n    }\n  };\n\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\n  const handleDeleteAccount = async accountId => {\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${accountId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Failed to delete account: ${response.statusText}`);\n      }\n      if (user._id === accountId) {\n        logout();\n        navigate(\"/\");\n      } else {\n        await getAccounts(user.id_owner);\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting account:\", error);\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\n    } finally {\n      stopLoading();\n      setShowDeleteModal(false);\n      setSelectedAccount(null);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-table\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Qu\\u1EA3n l\\xED t\\xE0i kho\\u1EA3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"uy-search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"search-input\",\n          placeholder: \"Search for...\",\n          value: searchTerm,\n          onChange: handleSearchChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"create-order-btn\",\n          onClick: () => setShowModal(true),\n          children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleCreateAccount,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: \"10px\"\n            },\n            children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), confirmOtp && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"code\",\n              placeholder: \"\\u0110i\\u1EC1n m\\xE3 x\\xE1c nh\\u1EADn \",\n              value: formData.code,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"uy-sentagain\",\n              onClick: sentAgain,\n              children: \"G\\u1EEDi l\\u1EA1i m\\xE3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowEditModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleEditAccount,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Edit Staff Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowEditModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\u1ECD T\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ph\\xE2n Quy\\u1EC1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tr\\u1EA1ng Th\\xE1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"L\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\xE0nh \\u0110\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredAccounts.map(account => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${account.status ? account.status.toLowerCase() : \"active\"}`,\n              children: account.status || \"Acctive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.salary || \"N/A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uy-action\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleMenu(account._id),\n                className: \"menu-btn\",\n                children: \"\\u22EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), showMenuIndex === account._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uy-dropdown-menu\",\n                ref: dropdownRef,\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleOpenEditModal(account),\n                    children: \"Ch\\u1EC9nh s\\u1EEDa\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleDeleteAccount(account._id),\n                    children: \"X\\xF3a\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this)]\n        }, account._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        marginBottom: \"5px\",\n        color: \"red\"\n      },\n      children: \"L\\u01B0u \\xFD n\\u1EBFu s\\u1EEDa quy\\u1EC1n c\\u1EE7a Admin sang m\\u1ED9t quy\\u1EC1n kh\\xE1c m\\xE0 kh\\xF4ng bao g\\u1ED3m (\\\"*role\\\") b\\u1EA1n s\\u1EBD kh\\xF4ng th\\u1EC3 ph\\xE2n quy\\u1EC1n n\\u1EEFa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"deleteAccountBtn\",\n      onClick: () => handleDeleteAccount(user._id),\n      children: \"X\\xF3a T\\xE0i Kho\\u1EA3n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 353,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageAccount, \"YEPjCGWO4eOxu8ig+k3ZE2Td7Kw=\", false, function () {\n  return [useLoading, useAuth, useNavigate];\n});\n_c = ManageAccount;\nexport default ManageAccount;\nvar _c;\n$RefreshReg$(_c, \"ManageAccount\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "getRoles", "useAuth", "useLoading", "notify", "useNavigate", "FaSearch", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaEyeSlash", "FaTimes", "FaCheck", "FaUser", "FaEnvelope", "FaLock", "FaUserTag", "FaCode", "FaRefreshCw", "FaSort", "FaSortUp", "FaSortDown", "FaFilter", "FaUsers", "FaShieldAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManageAccount", "_s", "accounts", "setAccounts", "rolesData", "setRolesData", "searchTerm", "setSearchTerm", "sortConfig", "setSortConfig", "key", "direction", "filterRole", "setFilterRole", "showMenuIndex", "setShowMenuIndex", "showModal", "setShowModal", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedAccount", "setSelectedAccount", "formData", "setFormData", "name", "email", "password", "role", "code", "confirmOtp", "setConfirmOtp", "showPassword", "setShowPassword", "formErrors", "setFormErrors", "startLoading", "stopLoading", "user", "logout", "navigate", "dropdownRef", "getAccounts", "userId", "console", "error", "response", "fetch", "method", "headers", "Authorization", "localStorage", "getItem", "ok", "Error", "statusText", "data", "json", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "fetchRoles", "id_owner", "roles", "prevData", "_id", "toggleMenu", "index", "handleSearchChange", "e", "value", "filteredAccounts", "Array", "isArray", "filter", "account", "toLowerCase", "includes", "handleCreateAccount", "preventDefault", "dataUser", "id", "body", "JSON", "stringify", "log", "message", "sentAgain", "prev", "handleOpenEditModal", "handleEditAccount", "handleDeleteAccount", "accountId", "handleInputChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "style", "marginBottom", "required", "disabled", "map", "status", "salary", "ref", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/ManageAccount/index.js"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from \"react\";\r\nimport \"./ManageAccount.css\";\r\nimport { getRoles } from \"../../services/Roles/rolesService\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// Icons\r\nimport {\r\n  FaSearch,\r\n  FaPlus,\r\n  FaEdit,\r\n  FaTrash,\r\n  FaEye,\r\n  FaEyeSlash,\r\n  FaTimes,\r\n  FaCheck,\r\n  FaUser,\r\n  FaEnvelope,\r\n  FaLock,\r\n  FaUserTag,\r\n  FaCode,\r\n  FaRefreshCw,\r\n  FaSort,\r\n  FaSortUp,\r\n  FaSortDown,\r\n  FaFilter,\r\n  FaUsers,\r\n  FaShieldAlt,\r\n} from \"react-icons/fa\";\r\n\r\nconst ManageAccount = () => {\r\n  // State management\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [rolesData, setRolesData] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\r\n  const [filterRole, setFilterRole] = useState(\"\");\r\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [selectedAccount, setSelectedAccount] = useState(null);\r\n\r\n  // Form states\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    role: \"\",\r\n    code: \"\",\r\n  });\r\n  const [confirmOtp, setConfirmOtp] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [formErrors, setFormErrors] = useState({});\r\n\r\n  // Hooks\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, logout } = useAuth();\r\n  const navigate = useNavigate();\r\n  const dropdownRef = useRef(null);\r\n\r\n  // API Functions\r\n  const getAccounts = useCallback(\r\n    async (userId) => {\r\n      if (!userId) {\r\n        console.error(\"Lỗi: userId không hợp lệ!\");\r\n        return;\r\n      }\r\n\r\n      try {\r\n        startLoading();\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/user/list?userId=${userId}`,\r\n          {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\r\n            `Network response was not ok: ${response.statusText}`\r\n          );\r\n        }\r\n\r\n        const data = await response.json();\r\n        setAccounts(data);\r\n      } catch (error) {\r\n        console.error(\"Lỗi khi gọi API:\", error);\r\n        notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\r\n      } finally {\r\n        stopLoading();\r\n      }\r\n    },\r\n    [startLoading, stopLoading]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setShowMenuIndex(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchRoles = async () => {\r\n      if (user) {\r\n        startLoading();\r\n        await getAccounts(user.id_owner);\r\n        const roles = await getRoles(user.id_owner);\r\n        setRolesData(roles);\r\n        stopLoading();\r\n        setFormData((prevData) => ({ ...prevData, id_owner: user._id })); // cập nhật id_owner\r\n      }\r\n    };\r\n    fetchRoles();\r\n  }, [user]);\r\n\r\n  const toggleMenu = (index) => {\r\n    setShowMenuIndex(showMenuIndex === index ? null : index);\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  // ✅ Đảm bảo accounts luôn là array trước khi filter\r\n  const filteredAccounts = Array.isArray(accounts)\r\n    ? accounts.filter((account) => {\r\n        const name = account.name ? account.name.toLowerCase() : \"\";\r\n        const email = account.email ? account.email.toLowerCase() : \"\";\r\n        const role = account.role ? account.role.toLowerCase() : \"\";\r\n\r\n        return (\r\n          name.includes(searchTerm.toLowerCase()) ||\r\n          email.includes(searchTerm.toLowerCase()) ||\r\n          role.includes(searchTerm.toLowerCase())\r\n        );\r\n      })\r\n    : []; // Trả về array rỗng nếu accounts không phải array\r\n\r\n  const handleCreateAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: formData.role,\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      console.log(data);\r\n\r\n      if (confirmOtp) {\r\n        if (data.message === \"Staff is created successfully\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\r\n        }\r\n      } else {\r\n        if (data.message === \"Confirmation code sent\") {\r\n          setConfirmOtp(true);\r\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n        } else if (data.message === \"User_new updated successfully!\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const sentAgain = async () => {\r\n    setConfirmOtp(true);\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: user ? user.role : \"\",\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      // Khi gửi mã xác nhận\r\n      if (data.message === \"Confirmation code sent\") {\r\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\r\n        setFormData((prev) => ({ ...prev, code: \"\" }));\r\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n      } else {\r\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleOpenEditModal = (account) => {\r\n    setFormData({\r\n      id: account._id,\r\n      name: account.name,\r\n      email: account.email,\r\n      role: account.role,\r\n      password: account.password, // Assuming password can be left blank for editing\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const handleEditAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${formData.id}`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n          body: JSON.stringify({\r\n            name: formData.name,\r\n            email: formData.email,\r\n            password: formData.password,\r\n            role: formData.role,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      console.log(\"Success:\", data);\r\n      stopLoading();\r\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\r\n      await getAccounts(user.id_owner);\r\n      setShowModal(false);\r\n    } catch (error) {\r\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\r\n      console.error(\"Error edit:\", error);\r\n    }\r\n  };\r\n\r\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\r\n  const handleDeleteAccount = async (accountId) => {\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${accountId}`,\r\n        {\r\n          method: \"DELETE\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to delete account: ${response.statusText}`);\r\n      }\r\n\r\n      if (user._id === accountId) {\r\n        logout();\r\n        navigate(\"/\");\r\n      } else {\r\n        await getAccounts(user.id_owner);\r\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting account:\", error);\r\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\r\n    } finally {\r\n      stopLoading();\r\n      setShowDeleteModal(false);\r\n      setSelectedAccount(null);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  return (\r\n    <div className=\"account-table\">\r\n      <div className=\"account-header\">\r\n        <h2>Quản lí tài khoản</h2>\r\n        <div className=\"uy-search-container\">\r\n          <input\r\n            type=\"text\"\r\n            className=\"search-input\"\r\n            placeholder=\"Search for...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n          />\r\n          <button\r\n            className=\"create-order-btn\"\r\n            onClick={() => setShowModal(true)}\r\n          >\r\n            Tạo tài khoản nhân viên\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {showModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button className=\"close-modal\" onClick={() => setShowModal(false)}>\r\n              ✖\r\n            </button>\r\n            <form\r\n              className=\"create-account-form\"\r\n              onSubmit={handleCreateAccount}\r\n            >\r\n              <h3 style={{ marginBottom: \"10px\" }}>Tạo tài khoản nhân viên</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n\r\n              {confirmOtp && (\r\n                <>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"code\"\r\n                    placeholder=\"Điền mã xác nhận \"\r\n                    value={formData.code}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                  />\r\n                  <p className=\"uy-sentagain\" onClick={sentAgain}>\r\n                    Gửi lại mã\r\n                  </p>\r\n                </>\r\n              )}\r\n\r\n              <button type=\"submit\">\r\n                {confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"}\r\n              </button>\r\n              <button type=\"button\" onClick={() => setShowModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showEditModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button\r\n              className=\"close-modal\"\r\n              onClick={() => setShowEditModal(false)}\r\n            >\r\n              ✖\r\n            </button>\r\n            <form className=\"create-account-form\" onSubmit={handleEditAccount}>\r\n              {\" \"}\r\n              {/* Changed class name here */}\r\n              <h3>Edit Staff Account</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <button type=\"submit\">Submit</button>\r\n              <button type=\"button\" onClick={() => setShowEditModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <table>\r\n        <thead>\r\n          <tr>\r\n            <th>Họ Tên</th>\r\n            <th>Phân Quyền</th>\r\n            <th>Email</th>\r\n            <th>Trạng Thái</th>\r\n            <th>Lương</th>\r\n            <th>Hành Động</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredAccounts.map((account) => (\r\n            <tr key={account._id}>\r\n              <td>{account.name}</td>\r\n              <td>{account.role}</td>\r\n              <td>{account.email}</td>\r\n              <td>\r\n                <span\r\n                  className={`status ${\r\n                    account.status ? account.status.toLowerCase() : \"active\"\r\n                  }`}\r\n                >\r\n                  {account.status || \"Acctive\"}\r\n                </span>\r\n              </td>\r\n              <td>{account.salary || \"N/A\"}</td>\r\n              <td>\r\n                <div className=\"uy-action\">\r\n                  <button\r\n                    onClick={() => toggleMenu(account._id)}\r\n                    className=\"menu-btn\"\r\n                  >\r\n                    ⋮\r\n                  </button>\r\n                  {showMenuIndex === account._id && (\r\n                    <div className=\"uy-dropdown-menu\" ref={dropdownRef}>\r\n                      <ul>\r\n                        <li onClick={() => handleOpenEditModal(account)}>\r\n                          Chỉnh sửa\r\n                        </li>\r\n                        <li onClick={() => handleDeleteAccount(account._id)}>\r\n                          Xóa\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      <p style={{ marginBottom: \"5px\", color: \"red\" }}>\r\n        Lưu ý nếu sửa quyền của Admin sang một quyền khác mà không bao gồm\r\n        (\"*role\") bạn sẽ không thể phân quyền nữa\r\n      </p>\r\n      <button\r\n        className=\"deleteAccountBtn\"\r\n        onClick={() => handleDeleteAccount(user._id)}\r\n      >\r\n        Xóa Tài Khoản\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ManageAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,SACEC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM;IAAEsE,YAAY;IAAEC;EAAY,CAAC,GAAGnE,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEoE,IAAI;IAAEC;EAAO,CAAC,GAAGtE,OAAO,CAAC,CAAC;EAClC,MAAMuE,QAAQ,GAAGpE,WAAW,CAAC,CAAC;EAC9B,MAAMqE,WAAW,GAAG5E,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM6E,WAAW,GAAG3E,WAAW,CAC7B,MAAO4E,MAAM,IAAK;IAChB,IAAI,CAACA,MAAM,EAAE;MACXC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC1C;IACF;IAEA,IAAI;MACFT,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8CJ,MAAM,EAAE,EACtD;QACEK,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CACb,gCAAgCR,QAAQ,CAACS,UAAU,EACrD,CAAC;MACH;MAEA,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCzD,WAAW,CAACwD,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC1E,MAAM,CAAC,CAAC,EAAE,mCAAmC,EAAE,KAAK,CAAC;IACvD,CAAC,SAAS;MACRkE,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EACD,CAACD,YAAY,EAAEC,WAAW,CAC5B,CAAC;EAEDzE,SAAS,CAAC,MAAM;IACd,MAAM8F,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIlB,WAAW,CAACmB,OAAO,IAAI,CAACnB,WAAW,CAACmB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtElD,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC;IAEDmD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAE1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9F,SAAS,CAAC,MAAM;IACd,MAAMsG,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI5B,IAAI,EAAE;QACRF,YAAY,CAAC,CAAC;QACd,MAAMM,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;QAChC,MAAMC,KAAK,GAAG,MAAMpG,QAAQ,CAACsE,IAAI,CAAC6B,QAAQ,CAAC;QAC3CjE,YAAY,CAACkE,KAAK,CAAC;QACnB/B,WAAW,CAAC,CAAC;QACbb,WAAW,CAAE6C,QAAQ,KAAM;UAAE,GAAGA,QAAQ;UAAEF,QAAQ,EAAE7B,IAAI,CAACgC;QAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE;IACF,CAAC;IACDJ,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,MAAMiC,UAAU,GAAIC,KAAK,IAAK;IAC5B5D,gBAAgB,CAACD,aAAa,KAAK6D,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChCtE,aAAa,CAACsE,CAAC,CAACZ,MAAM,CAACa,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAAC/E,QAAQ,CAAC,GAC5CA,QAAQ,CAACgF,MAAM,CAAEC,OAAO,IAAK;IAC3B,MAAMvD,IAAI,GAAGuD,OAAO,CAACvD,IAAI,GAAGuD,OAAO,CAACvD,IAAI,CAACwD,WAAW,CAAC,CAAC,GAAG,EAAE;IAC3D,MAAMvD,KAAK,GAAGsD,OAAO,CAACtD,KAAK,GAAGsD,OAAO,CAACtD,KAAK,CAACuD,WAAW,CAAC,CAAC,GAAG,EAAE;IAC9D,MAAMrD,IAAI,GAAGoD,OAAO,CAACpD,IAAI,GAAGoD,OAAO,CAACpD,IAAI,CAACqD,WAAW,CAAC,CAAC,GAAG,EAAE;IAE3D,OACExD,IAAI,CAACyD,QAAQ,CAAC/E,UAAU,CAAC8E,WAAW,CAAC,CAAC,CAAC,IACvCvD,KAAK,CAACwD,QAAQ,CAAC/E,UAAU,CAAC8E,WAAW,CAAC,CAAC,CAAC,IACxCrD,IAAI,CAACsD,QAAQ,CAAC/E,UAAU,CAAC8E,WAAW,CAAC,CAAC,CAAC;EAE3C,CAAC,CAAC,GACF,EAAE,CAAC,CAAC;;EAER,MAAME,mBAAmB,GAAG,MAAOT,CAAC,IAAK;IACvCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAEhD,IAAI,GAAGA,IAAI,CAACgD,EAAE,GAAG,EAAE;QACvB1D,IAAI,EAAEL,QAAQ,CAACK,IAAI;QACnBuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;QACnCzC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBK,UAAU,EAAEA,UAAU;QACtBD,IAAI,EAAEN,QAAQ,CAACM;MACjB,CAAC;MACDO,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAE/C;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCpB,WAAW,CAAC,CAAC;MACbO,OAAO,CAAC8C,GAAG,CAAClC,IAAI,CAAC;MAEjB,IAAI1B,UAAU,EAAE;QACd,IAAI0B,IAAI,CAACmC,OAAO,KAAK,+BAA+B,EAAE;UACpDxH,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDqD,WAAW,CAAC;YACV8D,EAAE,EAAEhD,IAAI,GAAGA,IAAI,CAACgD,EAAE,GAAG,EAAE;YACvB7D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;YACnCtC,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBjB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM4B,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLhG,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACmC,OAAO,IAAI,iBAAiB,EAAE,UAAU,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAInC,IAAI,CAACmC,OAAO,KAAK,wBAAwB,EAAE;UAC7C5D,aAAa,CAAC,IAAI,CAAC;UACnB5D,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;QACpD,CAAC,MAAM,IAAIqF,IAAI,CAACmC,OAAO,KAAK,gCAAgC,EAAE;UAC5DxH,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDqD,WAAW,CAAC;YACV8D,EAAE,EAAEhD,IAAI,GAAGA,IAAI,CAACgD,EAAE,GAAG,EAAE;YACvB7D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;YACnCtC,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBjB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM4B,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLhG,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACmC,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;QACpE;MACF;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAM+C,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B7D,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMsD,QAAQ,GAAG;QACfC,EAAE,EAAEhD,IAAI,GAAGA,IAAI,CAACgD,EAAE,GAAG,EAAE;QACvB1D,IAAI,EAAEU,IAAI,GAAGA,IAAI,CAACV,IAAI,GAAG,EAAE;QAC3BuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;QACnCzC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBK,UAAU,EAAEA,UAAU;QACtBD,IAAI,EAAEN,QAAQ,CAACM;MACjB,CAAC;MACDO,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAE/C;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCpB,WAAW,CAAC,CAAC;MACb;MACA,IAAImB,IAAI,CAACmC,OAAO,KAAK,wBAAwB,EAAE;QAC7C5D,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QACrBP,WAAW,CAAEqE,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAEhE,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;QAC9C1D,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;MACpD,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACmC,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;MACpE;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMiD,mBAAmB,GAAId,OAAO,IAAK;IACvCxD,WAAW,CAAC;MACV8D,EAAE,EAAEN,OAAO,CAACV,GAAG;MACf7C,IAAI,EAAEuD,OAAO,CAACvD,IAAI;MAClBC,KAAK,EAAEsD,OAAO,CAACtD,KAAK;MACpBE,IAAI,EAAEoD,OAAO,CAACpD,IAAI;MAClBD,QAAQ,EAAEqD,OAAO,CAACrD,QAAQ,CAAE;IAC9B,CAAC,CAAC;IACFT,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM6E,iBAAiB,GAAG,MAAOrB,CAAC,IAAK;IACrCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,IAAI;MACFhD,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCxB,QAAQ,CAAC+D,EAAE,EAAE,EAC/C;QACEtC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDmC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBhE,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,IAAI,EAAEL,QAAQ,CAACK;QACjB,CAAC;MACH,CACF,CAAC;MAED,MAAM4B,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCb,OAAO,CAAC8C,GAAG,CAAC,UAAU,EAAElC,IAAI,CAAC;MAC7BnB,WAAW,CAAC,CAAC;MACblE,MAAM,CAAC,CAAC,EAAE,gCAAgC,EAAE,YAAY,CAAC;MACzD,MAAMuE,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;MAChCrD,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACd1E,MAAM,CAAC,CAAC,EAAE,8BAA8B,EAAE,UAAU,CAAC;MACrDyE,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMmD,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACF7D,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCkD,SAAS,EAAE,EAC7C;QACEjD,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6BR,QAAQ,CAACS,UAAU,EAAE,CAAC;MACrE;MAEA,IAAIjB,IAAI,CAACgC,GAAG,KAAK2B,SAAS,EAAE;QAC1B1D,MAAM,CAAC,CAAC;QACRC,QAAQ,CAAC,GAAG,CAAC;MACf,CAAC,MAAM;QACL,MAAME,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;QAChChG,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;MACrD;IACF,CAAC,CAAC,OAAO0E,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C1E,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;IACjD,CAAC,SAAS;MACRkE,WAAW,CAAC,CAAC;MACbjB,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM4E,iBAAiB,GAAIxB,CAAC,IAAK;IAC/B,MAAM;MAAEjD,IAAI;MAAEkD;IAAM,CAAC,GAAGD,CAAC,CAACZ,MAAM;IAChCtC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGkD;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,oBACEjF,OAAA;IAAKyG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B1G,OAAA;MAAKyG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1G,OAAA;QAAA0G,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B9G,OAAA;QAAKyG,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC1G,OAAA;UACE+G,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,cAAc;UACxBO,WAAW,EAAC,eAAe;UAC3B/B,KAAK,EAAExE,UAAW;UAClBwG,QAAQ,EAAElC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF9G,OAAA;UACEyG,SAAS,EAAC,kBAAkB;UAC5BS,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,IAAI,CAAE;UAAAsF,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3F,SAAS,iBACRnB,OAAA;MAAKyG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1G,OAAA;QAAKyG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1G,OAAA;UAAQyG,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,KAAK,CAAE;UAAAsF,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UACEyG,SAAS,EAAC,qBAAqB;UAC/BU,QAAQ,EAAE1B,mBAAoB;UAAAiB,QAAA,gBAE9B1G,OAAA;YAAIoH,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE9G,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXhF,IAAI,EAAC,MAAM;YACXiF,WAAW,EAAC,WAAW;YACvB/B,KAAK,EAAEpD,QAAQ,CAACE,IAAK;YACrBkF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF9G,OAAA;YACE+G,IAAI,EAAC,OAAO;YACZhF,IAAI,EAAC,OAAO;YACZiF,WAAW,EAAC,OAAO;YACnB/B,KAAK,EAAEpD,QAAQ,CAACG,KAAM;YACtBiF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF9G,OAAA;YACE+G,IAAI,EAAC,UAAU;YACfhF,IAAI,EAAC,UAAU;YACfiF,WAAW,EAAC,UAAU;YACtB/B,KAAK,EAAEpD,QAAQ,CAACI,QAAS;YACzBgF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF9G,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXkD,KAAK,EAAEpD,QAAQ,CAACK,IAAK;YACrB+E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAER1G,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAACsC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRvG,SAAS,CAACiH,GAAG,CAAEtF,IAAI,iBAClBlC,OAAA;cAAuBiF,KAAK,EAAE/C,IAAI,CAACA,IAAK;cAAAwE,QAAA,EACrCxE,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC0C,GAAG;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAER1E,UAAU,iBACTpC,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA;cACE+G,IAAI,EAAC,MAAM;cACXhF,IAAI,EAAC,MAAM;cACXiF,WAAW,EAAC,wCAAmB;cAC/B/B,KAAK,EAAEpD,QAAQ,CAACM,IAAK;cACrB8E,QAAQ,EAAET,iBAAkB;cAC5Bc,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9G,OAAA;cAAGyG,SAAS,EAAC,cAAc;cAACS,OAAO,EAAEhB,SAAU;cAAAQ,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CACH,eAED9G,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAClBtE,UAAU,GAAG,2BAA2B,GAAG;UAAY;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACT9G,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM9F,YAAY,CAAC,KAAK,CAAE;YAAAsF,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvF,aAAa,iBACZvB,OAAA;MAAKyG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1G,OAAA;QAAKyG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1G,OAAA;UACEyG,SAAS,EAAC,aAAa;UACvBS,OAAO,EAAEA,CAAA,KAAM1F,gBAAgB,CAAC,KAAK,CAAE;UAAAkF,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9G,OAAA;UAAMyG,SAAS,EAAC,qBAAqB;UAACU,QAAQ,EAAEd,iBAAkB;UAAAK,QAAA,GAC/D,GAAG,eAEJ1G,OAAA;YAAA0G,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B9G,OAAA;YACE+G,IAAI,EAAC,MAAM;YACXhF,IAAI,EAAC,MAAM;YACXiF,WAAW,EAAC,WAAW;YACvB/B,KAAK,EAAEpD,QAAQ,CAACE,IAAK;YACrBkF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF9G,OAAA;YACE+G,IAAI,EAAC,OAAO;YACZhF,IAAI,EAAC,OAAO;YACZiF,WAAW,EAAC,OAAO;YACnB/B,KAAK,EAAEpD,QAAQ,CAACG,KAAM;YACtBiF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF9G,OAAA;YACE+G,IAAI,EAAC,UAAU;YACfhF,IAAI,EAAC,UAAU;YACfiF,WAAW,EAAC,UAAU;YACtB/B,KAAK,EAAEpD,QAAQ,CAACI,QAAS;YACzBgF,QAAQ,EAAET;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF9G,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXkD,KAAK,EAAEpD,QAAQ,CAACK,IAAK;YACrB+E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAER1G,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAACsC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRvG,SAAS,CAACiH,GAAG,CAAEtF,IAAI,iBAClBlC,OAAA;cAAuBiF,KAAK,EAAE/C,IAAI,CAACA,IAAK;cAAAwE,QAAA,EACrCxE,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC0C,GAAG;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACT9G,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC9G,OAAA;YAAQ+G,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM1F,gBAAgB,CAAC,KAAK,CAAE;YAAAkF,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9G,OAAA;MAAA0G,QAAA,gBACE1G,OAAA;QAAA0G,QAAA,eACE1G,OAAA;UAAA0G,QAAA,gBACE1G,OAAA;YAAA0G,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf9G,OAAA;YAAA0G,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB9G,OAAA;YAAA0G,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd9G,OAAA;YAAA0G,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB9G,OAAA;YAAA0G,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd9G,OAAA;YAAA0G,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR9G,OAAA;QAAA0G,QAAA,EACGxB,gBAAgB,CAACsC,GAAG,CAAElC,OAAO,iBAC5BtF,OAAA;UAAA0G,QAAA,gBACE1G,OAAA;YAAA0G,QAAA,EAAKpB,OAAO,CAACvD;UAAI;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB9G,OAAA;YAAA0G,QAAA,EAAKpB,OAAO,CAACpD;UAAI;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB9G,OAAA;YAAA0G,QAAA,EAAKpB,OAAO,CAACtD;UAAK;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB9G,OAAA;YAAA0G,QAAA,eACE1G,OAAA;cACEyG,SAAS,EAAE,UACTnB,OAAO,CAACmC,MAAM,GAAGnC,OAAO,CAACmC,MAAM,CAAClC,WAAW,CAAC,CAAC,GAAG,QAAQ,EACvD;cAAAmB,QAAA,EAEFpB,OAAO,CAACmC,MAAM,IAAI;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL9G,OAAA;YAAA0G,QAAA,EAAKpB,OAAO,CAACoC,MAAM,IAAI;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClC9G,OAAA;YAAA0G,QAAA,eACE1G,OAAA;cAAKyG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1G,OAAA;gBACEkH,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACS,OAAO,CAACV,GAAG,CAAE;gBACvC6B,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACrB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACR7F,aAAa,KAAKqE,OAAO,CAACV,GAAG,iBAC5B5E,OAAA;gBAAKyG,SAAS,EAAC,kBAAkB;gBAACkB,GAAG,EAAE5E,WAAY;gBAAA2D,QAAA,eACjD1G,OAAA;kBAAA0G,QAAA,gBACE1G,OAAA;oBAAIkH,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAACd,OAAO,CAAE;oBAAAoB,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9G,OAAA;oBAAIkH,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAAChB,OAAO,CAACV,GAAG,CAAE;oBAAA8B,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAnCExB,OAAO,CAACV,GAAG;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoChB,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACR9G,OAAA;MAAGoH,KAAK,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAM,CAAE;MAAAlB,QAAA,EAAC;IAGjD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACJ9G,OAAA;MACEyG,SAAS,EAAC,kBAAkB;MAC5BS,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAAC1D,IAAI,CAACgC,GAAG,CAAE;MAAA8B,QAAA,EAC9C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1G,EAAA,CA/hBID,aAAa;EAAA,QA6BqB3B,UAAU,EACvBD,OAAO,EACfG,WAAW;AAAA;AAAAmJ,EAAA,GA/BxB1H,aAAa;AAiiBnB,eAAeA,aAAa;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}