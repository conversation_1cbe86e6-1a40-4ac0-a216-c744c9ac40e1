{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\useronlinecard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport { Line } from \"react-chartjs-2\";\nimport \"chart.js/auto\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UsersOnlineCard() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [dt, Setdt] = useState({\n    labels: [0, 0, 0, 0, 0, 0, 0, 0],\n    data: [0, 0, 0, 0, 0, 0, 0, 0]\n  });\n  const data = {\n    labels: dt.labels,\n    datasets: [{\n      data: dt.data,\n      borderColor: \"#1e88e5\",\n      backgroundColor: \"rgba(30, 136, 229, 0.2)\",\n      pointBackgroundColor: \"#ff5722\",\n      fill: true,\n      tension: 0.4\n    }]\n  };\n  const options = {\n    plugins: {\n      legend: {\n        display: false\n      }\n    },\n    scales: {\n      x: {\n        display: false\n      },\n      y: {\n        display: false\n      }\n    },\n    responsive: true,\n    maintainAspectRatio: false\n  };\n  // useEffect(() => {\n  //   const fetchData = async () => {\n  //     if (loading) return;\n  //     try {\n  //       const response = await fetch(\n  //         \"http://localhost:8080/api/home/<USER>\",\n  //         {\n  //           method: \"POST\",\n  //           headers: {\n  //             \"Content-Type\": \"application/json\",\n  //           },\n  //           body: JSON.stringify({\n  //             user: user,\n  //           }),\n  //         }\n  //       );\n\n  //       if (!response.ok) {\n  //         throw new Error(\"Network response was not ok\");\n  //       }\n\n  //       const data = await response.json();\n  //       console.log(\"generatedailyuser:\", data);\n  //       Setdt(data);\n  //     } catch (error) {\n  //       console.error(\"Error fetching revenue:\", error);\n  //     }\n  //   };\n\n  //   fetchData();\n  // }, [loading]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: \"#fff\",\n      color: \"#000\",\n      borderRadius: 2,\n      padding: 2,\n      width: \"100%\",\n      textAlign: \"left\",\n      boxShadow: \"0 4px 8px rgba(0, 0, 0, 0.1)\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        fontWeight: \"bold\"\n      },\n      children: Math.max(...dt.data)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        color: \"text.secondary\"\n      },\n      children: \"New customers\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        color: \"#1e88e5\",\n        fontWeight: \"bold\",\n        position: \"absolute\",\n        top: 16,\n        right: 16\n      },\n      children: \"+5%\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginTop: 2,\n        height: 60\n      },\n      children: /*#__PURE__*/_jsxDEV(Line, {\n        data: data,\n        options: options\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}\n_s(UsersOnlineCard, \"nmL53KEcclaf1x78uGmCuTKKLgc=\", false, function () {\n  return [useAuth];\n});\n_c = UsersOnlineCard;\nexport default UsersOnlineCard;\nvar _c;\n$RefreshReg$(_c, \"UsersOnlineCard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Line", "useAuth", "jsxDEV", "_jsxDEV", "UsersOnlineCard", "_s", "user", "loading", "dt", "<PERSON>dt", "labels", "data", "datasets", "borderColor", "backgroundColor", "pointBackgroundColor", "fill", "tension", "options", "plugins", "legend", "display", "scales", "x", "y", "responsive", "maintainAspectRatio", "sx", "color", "borderRadius", "padding", "width", "textAlign", "boxShadow", "children", "variant", "fontWeight", "Math", "max", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "right", "marginTop", "height", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography } from \"@mui/material\";\r\nimport { Line } from \"react-chartjs-2\";\r\nimport \"chart.js/auto\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nfunction UsersOnlineCard() {\r\n  const { user, loading } = useAuth();\r\n  const [dt, Setdt] = useState({\r\n    labels: [0, 0, 0, 0, 0, 0, 0, 0],\r\n    data: [0, 0, 0, 0, 0, 0, 0, 0],\r\n  });\r\n  const data = {\r\n    labels: dt.labels,\r\n    datasets: [\r\n      {\r\n        data: dt.data,\r\n        borderColor: \"#1e88e5\",\r\n        backgroundColor: \"rgba(30, 136, 229, 0.2)\",\r\n        pointBackgroundColor: \"#ff5722\",\r\n        fill: true,\r\n        tension: 0.4,\r\n      },\r\n    ],\r\n  };\r\n\r\n  const options = {\r\n    plugins: { legend: { display: false } },\r\n    scales: { x: { display: false }, y: { display: false } },\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n  };\r\n  // useEffect(() => {\r\n  //   const fetchData = async () => {\r\n  //     if (loading) return;\r\n  //     try {\r\n  //       const response = await fetch(\r\n  //         \"http://localhost:8080/api/home/<USER>\",\r\n  //         {\r\n  //           method: \"POST\",\r\n  //           headers: {\r\n  //             \"Content-Type\": \"application/json\",\r\n  //           },\r\n  //           body: JSON.stringify({\r\n  //             user: user,\r\n  //           }),\r\n  //         }\r\n  //       );\r\n\r\n  //       if (!response.ok) {\r\n  //         throw new Error(\"Network response was not ok\");\r\n  //       }\r\n\r\n  //       const data = await response.json();\r\n  //       console.log(\"generatedailyuser:\", data);\r\n  //       Setdt(data);\r\n  //     } catch (error) {\r\n  //       console.error(\"Error fetching revenue:\", error);\r\n  //     }\r\n  //   };\r\n\r\n  //   fetchData();\r\n  // }, [loading]);\r\n  return (\r\n    <Box\r\n      sx={{\r\n        backgroundColor: \"#fff\",\r\n        color: \"#000\",\r\n        borderRadius: 2,\r\n        padding: 2,\r\n        width: \"100%\",\r\n        textAlign: \"left\",\r\n        boxShadow: \"0 4px 8px rgba(0, 0, 0, 0.1)\",\r\n      }}\r\n    >\r\n      <Typography variant=\"h4\" sx={{ fontWeight: \"bold\" }}>\r\n        {Math.max(...dt.data)}\r\n      </Typography>\r\n      <Typography variant=\"body2\" sx={{ color: \"text.secondary\" }}>\r\n        New customers\r\n      </Typography>\r\n      <Typography\r\n        variant=\"h6\"\r\n        sx={{\r\n          color: \"#1e88e5\",\r\n          fontWeight: \"bold\",\r\n          position: \"absolute\",\r\n          top: 16,\r\n          right: 16,\r\n        }}\r\n      >\r\n        +5%\r\n      </Typography>\r\n\r\n      <Box sx={{ marginTop: 2, height: 60 }}>\r\n        <Line data={data} options={options} />\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default UsersOnlineCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,eAAe;AACtB,SAASC,OAAO,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC7D,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGN,OAAO,CAAC,CAAC;EACnC,MAAM,CAACO,EAAE,EAAEC,KAAK,CAAC,GAAGZ,QAAQ,CAAC;IAC3Ba,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChCC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EAC/B,CAAC,CAAC;EACF,MAAMA,IAAI,GAAG;IACXD,MAAM,EAAEF,EAAE,CAACE,MAAM;IACjBE,QAAQ,EAAE,CACR;MACED,IAAI,EAAEH,EAAE,CAACG,IAAI;MACbE,WAAW,EAAE,SAAS;MACtBC,eAAe,EAAE,yBAAyB;MAC1CC,oBAAoB,EAAE,SAAS;MAC/BC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EAED,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAE;MAAEC,MAAM,EAAE;QAAEC,OAAO,EAAE;MAAM;IAAE,CAAC;IACvCC,MAAM,EAAE;MAAEC,CAAC,EAAE;QAAEF,OAAO,EAAE;MAAM,CAAC;MAAEG,CAAC,EAAE;QAAEH,OAAO,EAAE;MAAM;IAAE,CAAC;IACxDI,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE;EACvB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA,oBACEvB,OAAA,CAACL,GAAG;IACF6B,EAAE,EAAE;MACFb,eAAe,EAAE,MAAM;MACvBc,KAAK,EAAE,MAAM;MACbC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,gBAEF/B,OAAA,CAACJ,UAAU;MAACoC,OAAO,EAAC,IAAI;MAACR,EAAE,EAAE;QAAES,UAAU,EAAE;MAAO,CAAE;MAAAF,QAAA,EACjDG,IAAI,CAACC,GAAG,CAAC,GAAG9B,EAAE,CAACG,IAAI;IAAC;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACbvC,OAAA,CAACJ,UAAU;MAACoC,OAAO,EAAC,OAAO;MAACR,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAiB,CAAE;MAAAM,QAAA,EAAC;IAE7D;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbvC,OAAA,CAACJ,UAAU;MACToC,OAAO,EAAC,IAAI;MACZR,EAAE,EAAE;QACFC,KAAK,EAAE,SAAS;QAChBQ,UAAU,EAAE,MAAM;QAClBO,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE;MACT,CAAE;MAAAX,QAAA,EACH;IAED;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbvC,OAAA,CAACL,GAAG;MAAC6B,EAAE,EAAE;QAAEmB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAE;MAAAb,QAAA,eACpC/B,OAAA,CAACH,IAAI;QAACW,IAAI,EAAEA,IAAK;QAACO,OAAO,EAAEA;MAAQ;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrC,EAAA,CA7FQD,eAAe;EAAA,QACIH,OAAO;AAAA;AAAA+C,EAAA,GAD1B5C,eAAe;AA+FxB,eAAeA,eAAe;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}