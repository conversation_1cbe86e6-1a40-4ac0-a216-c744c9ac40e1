{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\export\\\\thanh_toan.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"./thanh_toan.css\";\nimport { useLoading } from \"../introduce/Loading\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PaymentComponent({\n  close,\n  products,\n  totalAmount,\n  customers,\n  discount,\n  vat\n}) {\n  _s();\n  const [customerPhone, setCustomerPhone] = useState(\"\");\n  const [customerPaid, setCustomerPaid] = useState(0);\n  const [change, setChange] = useState(0);\n  const [selectedBank, setSelectedBank] = useState(\"\");\n  const [selectedBankDetails, setSelectedBankDetails] = useState(null);\n  const [suggestions, setSuggestion] = useState([]);\n  const [banks, setBanks] = useState([]);\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    loading\n  } = useAuth();\n  useEffect(() => {\n    const fetchBanks = async () => {\n      try {\n        startLoading();\n        const response = await fetch(\"http://localhost:8080/api/bank/get_bank\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            user\n          })\n        });\n        const data = await response.json();\n        stopLoading();\n        if (data) setBanks(data);\n      } catch (error) {\n        console.error(\"Error fetching banks:\", error);\n        stopLoading();\n      }\n    };\n    fetchBanks();\n  }, []);\n  const handleCustomerPaidChange = e => {\n    const amount = parseFloat(e.target.value) || 0;\n    setCustomerPaid(amount);\n    setChange(amount - totalAmount > 0 ? (amount - totalAmount).toLocaleString(\"vi-VN\") : 0);\n  };\n  const handleBankChange = e => {\n    const selectedName = e.target.value;\n    setSelectedBank(selectedName);\n    const bankDetails = banks.find(bank => bank.name === selectedName);\n    setSelectedBankDetails(bankDetails || null);\n  };\n  const success = async () => {\n    try {\n      const billData = {\n        creater: user._id,\n        discount: String(discount),\n        vat: String(vat),\n        owner: products[0].owner,\n        customerId: customerPhone,\n        totalAmount: totalAmount.toLocaleString(\"vi-VN\"),\n        items: products.map(product => ({\n          productID: product._id,\n          name: product.name,\n          quantity: product.quantity,\n          price: product.price,\n          discount: product.discount,\n          totalAmount: product.total.toLocaleString(\"vi-VN\")\n        })),\n        paymentMethod: selectedBank ? \"Ngân hàng\" : \"Tiền mặt\",\n        notes: \"\"\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/sell/history\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(billData)\n      });\n      stopLoading();\n      if (response.ok) {\n        notify(1, \"Lưu hóa đơn thành công\", \"Thành công\");\n        await close();\n      } else {\n        notify(2, \"Không thể lưu hóa đơn\", \"Thất bại\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n      notify(2, \"\", \"Có lỗi xảy ra\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"KH\\xC1CH THANH TO\\xC1N\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"delete_bill\",\n          onClick: close,\n          children: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Ch\\u1ECDn m\\xE1y in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            children: /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"Microsoft Print to PDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"M\\u1EABu in h\\xF3a \\u0111\\u01A1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            children: /*#__PURE__*/_jsxDEV(\"option\", {\n              children: \"H\\xF3a \\u0111\\u01A1n b\\xE1n h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Kh\\xE1ch h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",\n            value: customerPhone,\n            onChange: e => {\n              setCustomerPhone(e.target.value);\n              const filtered = e.target.value ? customers.filter(customer => customer.phone.includes(e.target.value)) : [];\n              setSuggestion(filtered);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            id: \"suggestions-sell\",\n            children: suggestions.map((customer, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              onClick: () => {\n                setCustomerPhone(customer.phone);\n                setSuggestion([]);\n              },\n              children: customer.phone\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"total-amount\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"T\\u1ED5ng ti\\u1EC1n ph\\u1EA3i tr\\u1EA3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: \"5px\"\n            },\n            children: [totalAmount.toLocaleString(\"vi-VN\"), \" VND\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Ti\\u1EC1n kh\\xE1ch \\u0111\\u01B0a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: customerPaid,\n            onChange: handleCustomerPaidChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Ti\\u1EC1n tr\\u1EA3 l\\u1EA1i kh\\xE1ch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: change,\n            readOnly: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Ng\\xE2n h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedBank,\n            onChange: handleBankChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"--Vui l\\xF2ng ch\\u1ECDn Ng\\xE2n h\\xE0ng--\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), banks.map(bank => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: bank.name,\n              children: bank.name\n            }, bank.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), selectedBankDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bank-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Th\\xF4ng tin ng\\xE2n h\\xE0ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\xEAn:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), \" \", selectedBankDetails.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"S\\u1ED1 t\\xE0i kho\\u1EA3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), \" \", selectedBankDetails.accountNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qr-code\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"M\\xE3 QR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `https://img.vietqr.io/image/${selectedBankDetails.bankName}-${selectedBankDetails.accountNumber}-compact.jpg?amount=${totalAmount}`,\n              alt: \"QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: success,\n            children: \"F5 In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-button\",\n            onClick: close,\n            children: \"ESC B\\u1ECF qua\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n}\n_s(PaymentComponent, \"G2TcZ8K+jTCWTIxxZ2anqeUPMqY=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = PaymentComponent;\nexport default PaymentComponent;\nvar _c;\n$RefreshReg$(_c, \"PaymentComponent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLoading", "useAuth", "notify", "jsxDEV", "_jsxDEV", "PaymentComponent", "close", "products", "totalAmount", "customers", "discount", "vat", "_s", "customerPhone", "setCustomerPhone", "customerPaid", "setCustomerPaid", "change", "set<PERSON>hange", "selectedBank", "setSelectedBank", "selectedBankDetails", "setSelectedBankDetails", "suggestions", "setSuggestion", "banks", "setBanks", "startLoading", "stopLoading", "user", "loading", "fetchBanks", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "error", "console", "handleCustomerPaidChange", "e", "amount", "parseFloat", "target", "value", "toLocaleString", "handleBankChange", "<PERSON><PERSON><PERSON>", "bankDetails", "find", "bank", "name", "success", "billData", "creater", "_id", "String", "owner", "customerId", "items", "map", "product", "productID", "quantity", "price", "total", "paymentMethod", "notes", "ok", "children", "className", "onClick", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "filtered", "filter", "customer", "phone", "includes", "id", "index", "style", "marginTop", "readOnly", "accountNumber", "src", "bankName", "alt", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/export/thanh_toan.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./thanh_toan.css\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\n\r\nfunction PaymentComponent({\r\n  close,\r\n  products,\r\n  totalAmount,\r\n  customers,\r\n  discount,\r\n  vat,\r\n}) {\r\n  const [customerPhone, setCustomerPhone] = useState(\"\");\r\n  const [customerPaid, setCustomerPaid] = useState(0);\r\n  const [change, setChange] = useState(0);\r\n  const [selectedBank, setSelectedBank] = useState(\"\");\r\n  const [selectedBankDetails, setSelectedBankDetails] = useState(null);\r\n  const [suggestions, setSuggestion] = useState([]);\r\n  const [banks, setBanks] = useState([]);\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, loading } = useAuth();\r\n  useEffect(() => {\r\n    const fetchBanks = async () => {\r\n      try {\r\n        startLoading();\r\n        const response = await fetch(\r\n          \"http://localhost:8080/api/bank/get_bank\",\r\n          {\r\n            method: \"POST\",\r\n            headers: { \"Content-Type\": \"application/json\" },\r\n            body: JSON.stringify({ user }),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        stopLoading();\r\n        if (data) setBanks(data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching banks:\", error);\r\n        stopLoading();\r\n      }\r\n    };\r\n    fetchBanks();\r\n  }, []);\r\n\r\n  const handleCustomerPaidChange = (e) => {\r\n    const amount = parseFloat(e.target.value) || 0;\r\n    setCustomerPaid(amount);\r\n    setChange(\r\n      amount - totalAmount > 0\r\n        ? (amount - totalAmount).toLocaleString(\"vi-VN\")\r\n        : 0\r\n    );\r\n  };\r\n\r\n  const handleBankChange = (e) => {\r\n    const selectedName = e.target.value;\r\n    setSelectedBank(selectedName);\r\n    const bankDetails = banks.find((bank) => bank.name === selectedName);\r\n    setSelectedBankDetails(bankDetails || null);\r\n  };\r\n\r\n  const success = async () => {\r\n    try {\r\n      const billData = {\r\n        creater: user._id,\r\n        discount: String(discount),\r\n        vat: String(vat),\r\n        owner: products[0].owner,\r\n        customerId: customerPhone,\r\n        totalAmount: totalAmount.toLocaleString(\"vi-VN\"),\r\n        items: products.map((product) => ({\r\n          productID: product._id,\r\n          name: product.name,\r\n          quantity: product.quantity,\r\n          price: product.price,\r\n          discount: product.discount,\r\n          totalAmount: product.total.toLocaleString(\"vi-VN\"),\r\n        })),\r\n        paymentMethod: selectedBank ? \"Ngân hàng\" : \"Tiền mặt\",\r\n        notes: \"\",\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/sell/history\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify(billData),\r\n      });\r\n      stopLoading();\r\n      if (response.ok) {\r\n        notify(1, \"Lưu hóa đơn thành công\", \"Thành công\");\r\n        await close();\r\n      } else {\r\n        notify(2, \"Không thể lưu hóa đơn\", \"Thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n      notify(2, \"\", \"Có lỗi xảy ra\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"modal-overlay\">\r\n        <div className=\"payment-modal\" onClick={(e) => e.stopPropagation()}>\r\n          <h2>KHÁCH THANH TOÁN</h2>\r\n          <p className=\"delete_bill\" onClick={close}>\r\n            x\r\n          </p>\r\n          <div>\r\n            <label>Chọn máy in</label>\r\n            <select>\r\n              <option>Microsoft Print to PDF</option>\r\n            </select>\r\n          </div>\r\n          <div>\r\n            <label>Mẫu in hóa đơn</label>\r\n            <select>\r\n              <option>Hóa đơn bán hàng</option>\r\n            </select>\r\n          </div>\r\n          <div>\r\n            <label>Khách hàng</label>\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Số điện thoại\"\r\n              value={customerPhone}\r\n              onChange={(e) => {\r\n                setCustomerPhone(e.target.value);\r\n                const filtered = e.target.value\r\n                  ? customers.filter((customer) =>\r\n                      customer.phone.includes(e.target.value)\r\n                    )\r\n                  : [];\r\n                setSuggestion(filtered);\r\n              }}\r\n            />\r\n            <ul id=\"suggestions-sell\">\r\n              {suggestions.map((customer, index) => (\r\n                <li\r\n                  key={index}\r\n                  onClick={() => {\r\n                    setCustomerPhone(customer.phone);\r\n                    setSuggestion([]);\r\n                  }}\r\n                >\r\n                  {customer.phone}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n          <div className=\"total-amount\">\r\n            <label>Tổng tiền phải trả</label>\r\n            <p style={{ marginTop: \"5px\" }}>\r\n              {totalAmount.toLocaleString(\"vi-VN\")} VND\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <label>Tiền khách đưa</label>\r\n            <input\r\n              type=\"number\"\r\n              value={customerPaid}\r\n              onChange={handleCustomerPaidChange}\r\n            />\r\n          </div>\r\n          <div>\r\n            <label>Tiền trả lại khách</label>\r\n            <input type=\"text\" value={change} readOnly />\r\n          </div>\r\n          <div>\r\n            <label>Ngân hàng</label>\r\n            <select value={selectedBank} onChange={handleBankChange}>\r\n              <option value=\"\">--Vui lòng chọn Ngân hàng--</option>\r\n              {banks.map((bank) => (\r\n                <option key={bank.id} value={bank.name}>\r\n                  {bank.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n          {selectedBankDetails && (\r\n            <div className=\"bank-details\">\r\n              <h3>Thông tin ngân hàng</h3>\r\n              <p>\r\n                <strong>Tên:</strong> {selectedBankDetails.name}\r\n              </p>\r\n              <p>\r\n                <strong>Số tài khoản:</strong>{\" \"}\r\n                {selectedBankDetails.accountNumber}\r\n              </p>\r\n              <div className=\"qr-code\">\r\n                <h4>Mã QR</h4>\r\n                <img\r\n                  src={`https://img.vietqr.io/image/${selectedBankDetails.bankName}-${selectedBankDetails.accountNumber}-compact.jpg?amount=${totalAmount}`}\r\n                  alt=\"QR Code\"\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div className=\"button-group\">\r\n            <button onClick={success}>F5 In</button>\r\n            <button className=\"cancel-button\" onClick={close}>\r\n              ESC Bỏ qua\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PaymentComponent;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,kBAAkB;AACzB,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,SAASC,gBAAgBA,CAAC;EACxBC,KAAK;EACLC,QAAQ;EACRC,WAAW;EACXC,SAAS;EACTC,QAAQ;EACRC;AACF,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyB,WAAW,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAE6B,YAAY;IAAEC;EAAY,CAAC,GAAG5B,UAAU,CAAC,CAAC;EAClD,MAAM;IAAE6B,IAAI;IAAEC;EAAQ,CAAC,GAAG7B,OAAO,CAAC,CAAC;EACnCF,SAAS,CAAC,MAAM;IACd,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFJ,YAAY,CAAC,CAAC;QACd,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAC1B,yCAAyC,EACzC;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAET;UAAK,CAAC;QAC/B,CACF,CAAC;QACD,MAAMU,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCZ,WAAW,CAAC,CAAC;QACb,IAAIW,IAAI,EAAEb,QAAQ,CAACa,IAAI,CAAC;MAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7Cb,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IACDG,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,wBAAwB,GAAIC,CAAC,IAAK;IACtC,MAAMC,MAAM,GAAGC,UAAU,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC;IAC9ChC,eAAe,CAAC6B,MAAM,CAAC;IACvB3B,SAAS,CACP2B,MAAM,GAAGrC,WAAW,GAAG,CAAC,GACpB,CAACqC,MAAM,GAAGrC,WAAW,EAAEyC,cAAc,CAAC,OAAO,CAAC,GAC9C,CACN,CAAC;EACH,CAAC;EAED,MAAMC,gBAAgB,GAAIN,CAAC,IAAK;IAC9B,MAAMO,YAAY,GAAGP,CAAC,CAACG,MAAM,CAACC,KAAK;IACnC5B,eAAe,CAAC+B,YAAY,CAAC;IAC7B,MAAMC,WAAW,GAAG3B,KAAK,CAAC4B,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAKJ,YAAY,CAAC;IACpE7B,sBAAsB,CAAC8B,WAAW,IAAI,IAAI,CAAC;EAC7C,CAAC;EAED,MAAMI,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMC,QAAQ,GAAG;QACfC,OAAO,EAAE7B,IAAI,CAAC8B,GAAG;QACjBjD,QAAQ,EAAEkD,MAAM,CAAClD,QAAQ,CAAC;QAC1BC,GAAG,EAAEiD,MAAM,CAACjD,GAAG,CAAC;QAChBkD,KAAK,EAAEtD,QAAQ,CAAC,CAAC,CAAC,CAACsD,KAAK;QACxBC,UAAU,EAAEjD,aAAa;QACzBL,WAAW,EAAEA,WAAW,CAACyC,cAAc,CAAC,OAAO,CAAC;QAChDc,KAAK,EAAExD,QAAQ,CAACyD,GAAG,CAAEC,OAAO,KAAM;UAChCC,SAAS,EAAED,OAAO,CAACN,GAAG;UACtBJ,IAAI,EAAEU,OAAO,CAACV,IAAI;UAClBY,QAAQ,EAAEF,OAAO,CAACE,QAAQ;UAC1BC,KAAK,EAAEH,OAAO,CAACG,KAAK;UACpB1D,QAAQ,EAAEuD,OAAO,CAACvD,QAAQ;UAC1BF,WAAW,EAAEyD,OAAO,CAACI,KAAK,CAACpB,cAAc,CAAC,OAAO;QACnD,CAAC,CAAC,CAAC;QACHqB,aAAa,EAAEnD,YAAY,GAAG,WAAW,GAAG,UAAU;QACtDoD,KAAK,EAAE;MACT,CAAC;MACD5C,YAAY,CAAC,CAAC;MACd,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;QACrEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmB,QAAQ;MAC/B,CAAC,CAAC;MACF7B,WAAW,CAAC,CAAC;MACb,IAAII,QAAQ,CAACwC,EAAE,EAAE;QACftE,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,YAAY,CAAC;QACjD,MAAMI,KAAK,CAAC,CAAC;MACf,CAAC,MAAM;QACLJ,MAAM,CAAC,CAAC,EAAE,uBAAuB,EAAE,UAAU,CAAC;MAChD;IACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9BvC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,eAAe,CAAC;IAChC;EACF,CAAC;EAED,oBACEE,OAAA;IAAAqE,QAAA,eACErE,OAAA;MAAKsE,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BrE,OAAA;QAAKsE,SAAS,EAAC,eAAe;QAACC,OAAO,EAAG/B,CAAC,IAAKA,CAAC,CAACgC,eAAe,CAAC,CAAE;QAAAH,QAAA,gBACjErE,OAAA;UAAAqE,QAAA,EAAI;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB5E,OAAA;UAAGsE,SAAS,EAAC,aAAa;UAACC,OAAO,EAAErE,KAAM;UAAAmE,QAAA,EAAC;QAE3C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1B5E,OAAA;YAAAqE,QAAA,eACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7B5E,OAAA;YAAAqE,QAAA,eACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzB5E,OAAA;YACE6E,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mCAAe;YAC3BlC,KAAK,EAAEnC,aAAc;YACrBsE,QAAQ,EAAGvC,CAAC,IAAK;cACf9B,gBAAgB,CAAC8B,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC;cAChC,MAAMoC,QAAQ,GAAGxC,CAAC,CAACG,MAAM,CAACC,KAAK,GAC3BvC,SAAS,CAAC4E,MAAM,CAAEC,QAAQ,IACxBA,QAAQ,CAACC,KAAK,CAACC,QAAQ,CAAC5C,CAAC,CAACG,MAAM,CAACC,KAAK,CACxC,CAAC,GACD,EAAE;cACNxB,aAAa,CAAC4D,QAAQ,CAAC;YACzB;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5E,OAAA;YAAIqF,EAAE,EAAC,kBAAkB;YAAAhB,QAAA,EACtBlD,WAAW,CAACyC,GAAG,CAAC,CAACsB,QAAQ,EAAEI,KAAK,kBAC/BtF,OAAA;cAEEuE,OAAO,EAAEA,CAAA,KAAM;gBACb7D,gBAAgB,CAACwE,QAAQ,CAACC,KAAK,CAAC;gBAChC/D,aAAa,CAAC,EAAE,CAAC;cACnB,CAAE;cAAAiD,QAAA,EAEDa,QAAQ,CAACC;YAAK,GANVG,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOR,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN5E,OAAA;UAAKsE,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BrE,OAAA;YAAAqE,QAAA,EAAO;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC5E,OAAA;YAAGuF,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAnB,QAAA,GAC5BjE,WAAW,CAACyC,cAAc,CAAC,OAAO,CAAC,EAAC,MACvC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7B5E,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbjC,KAAK,EAAEjC,YAAa;YACpBoE,QAAQ,EAAExC;UAAyB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC5E,OAAA;YAAO6E,IAAI,EAAC,MAAM;YAACjC,KAAK,EAAE/B,MAAO;YAAC4E,QAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN5E,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAAqE,QAAA,EAAO;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxB5E,OAAA;YAAQ4C,KAAK,EAAE7B,YAAa;YAACgE,QAAQ,EAAEjC,gBAAiB;YAAAuB,QAAA,gBACtDrE,OAAA;cAAQ4C,KAAK,EAAC,EAAE;cAAAyB,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACpDvD,KAAK,CAACuC,GAAG,CAAEV,IAAI,iBACdlD,OAAA;cAAsB4C,KAAK,EAAEM,IAAI,CAACC,IAAK;cAAAkB,QAAA,EACpCnB,IAAI,CAACC;YAAI,GADCD,IAAI,CAACmC,EAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL3D,mBAAmB,iBAClBjB,OAAA;UAAKsE,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BrE,OAAA;YAAAqE,QAAA,EAAI;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B5E,OAAA;YAAAqE,QAAA,gBACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,mBAAmB,CAACkC,IAAI;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACJ5E,OAAA;YAAAqE,QAAA,gBACErE,OAAA;cAAAqE,QAAA,EAAQ;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACjC3D,mBAAmB,CAACyE,aAAa;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACJ5E,OAAA;YAAKsE,SAAS,EAAC,SAAS;YAAAD,QAAA,gBACtBrE,OAAA;cAAAqE,QAAA,EAAI;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd5E,OAAA;cACE2F,GAAG,EAAE,+BAA+B1E,mBAAmB,CAAC2E,QAAQ,IAAI3E,mBAAmB,CAACyE,aAAa,uBAAuBtF,WAAW,EAAG;cAC1IyF,GAAG,EAAC;YAAS;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACD5E,OAAA;UAAKsE,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BrE,OAAA;YAAQuE,OAAO,EAAEnB,OAAQ;YAAAiB,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5E,OAAA;YAAQsE,SAAS,EAAC,eAAe;YAACC,OAAO,EAAErE,KAAM;YAAAmE,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpE,EAAA,CA5MQP,gBAAgB;EAAA,QAeeL,UAAU,EACtBC,OAAO;AAAA;AAAAiG,EAAA,GAhB1B7F,gBAAgB;AA8MzB,eAAeA,gBAAgB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}