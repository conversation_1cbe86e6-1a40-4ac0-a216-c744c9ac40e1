{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport Sales_daily from \"./sale_daily\";\nimport Useronline from \"./useronlinecard\";\n// src/index.js hoặc src/App.js'\nimport CalendarComponent from \"../Calendar/index.js\";\n// import React from 'react';\nimport { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from \"recharts\";\nimport \"./x1.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [totalrevenue, setTotalrevenue] = useState({\n    percentChange: \"0%\",\n    totalRevenueToday: \"0\",\n    state: \"\"\n  });\n  const [totalincome, setTotalincome] = useState({\n    profitToday: 0,\n    profitYesterday: 0,\n    percentChange: \"0%\",\n    message: \"notchange\"\n  });\n  const [data, setData] = useState([]);\n  const [topproduct, setTopproduct] = useState([]);\n  const [newcustomer, setNewcustomer] = useState({\n    customerToday: 0,\n    customerYesterday: 0,\n    percentChange: \"0%\",\n    state: \"notchange\"\n  });\n  const [pending, setPending] = useState({\n    total: 0,\n    percent: \"0%\"\n  });\n  const [act, setAct] = useState([]);\n  const datas = [{\n    name: \"Jan\",\n    \"Khách hàng trung thành\": 270,\n    \"khách hàng mới\": 150,\n    \"Khách hàng quay lại\": 542\n  }, {\n    name: \"Feb\",\n    \"Khách hàng trung thành\": 310,\n    \"khách hàng mới\": 180,\n    \"Khách hàng quay lại\": 520\n  }, {\n    name: \"Mar\",\n    \"Khách hàng trung thành\": 350,\n    \"khách hàng mới\": 200,\n    \"Khách hàng quay lại\": 560\n  }, {\n    name: \"Apr\",\n    \"Khách hàng trung thành\": 330,\n    \"khách hàng mới\": 220,\n    \"Khách hàng quay lại\": 480\n  }, {\n    name: \"May\",\n    \"Khách hàng trung thành\": 450,\n    \"khách hàng mới\": 260,\n    \"Khách hàng quay lại\": 550\n  }, {\n    name: \"Jun\",\n    \"Khách hàng trung thành\": 400,\n    \"khách hàng mới\": 290,\n    \"Khách hàng quay lại\": 580\n  }, {\n    name: \"Jul\",\n    \"Khách hàng trung thành\": 460,\n    \"khách hàng mới\": 320,\n    \"Khách hàng quay lại\": 620\n  }, {\n    name: \"Aug\",\n    \"Khách hàng trung thành\": 510,\n    \"khách hàng mới\": 340,\n    \"Khách hàng quay lại\": 680\n  }, {\n    name: \"Sep\",\n    \"Khách hàng trung thành\": 252,\n    \"khách hàng mới\": 360,\n    \"Khách hàng quay lại\": 740\n  }, {\n    name: \"Oct\",\n    \"Khách hàng trung thành\": 680,\n    \"khách hàng mới\": 390,\n    \"Khách hàng quay lại\": 820\n  }, {\n    name: \"Nov\",\n    \"Khách hàng trung thành\": 780,\n    \"khách hàng mới\": 420,\n    \"Khách hàng quay lại\": 890\n  }, {\n    name: \"Dec\",\n    \"Khách hàng trung thành\": 900,\n    \"khách hàng mới\": 450,\n    \"Khách hàng quay lại\": 980\n  }];\n\n  // if (!user) {\n  //   return <div>Không có người dùng nào đăng nhập.</div>;\n  // }\n  useEffect(() => {\n    const fetchData = async () => {\n      if (loading) return;\n      const get_revenue = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"Revenue:\", data);\n          setTotalrevenue(data);\n        } catch (error) {\n          console.error(\"Error fetching revenue:\", error);\n        }\n      };\n      const get_income = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"Income:\", data);\n          setTotalincome(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_customer = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"customer:\", data);\n          setNewcustomer(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      // const get_report_customer = async () => {\n      //   try {\n      //     const response = await fetch(\n      //       \"http://localhost:8080/api/home/<USER>\",\n      //       {\n      //         method: \"POST\",\n      //         headers: {\n      //           \"Content-Type\": \"application/json\",\n      //         },\n      //         body: JSON.stringify({\n      //           user: user,\n      //         }),\n      //       }\n      //     );\n\n      //     if (!response.ok) {\n      //       throw new Error(\"Network response was not ok\");\n      //     }\n\n      //     const data = await response.json();\n      //     console.log(\"customer:\", data);\n      //     setData(data);\n      //   } catch (error) {\n      //     console.error(\"Error fetching income:\", error);\n      //   }\n      // };\n      // const get_top_product = async () => {\n      //   try {\n      //     const response = await fetch(\n      //       \"http://localhost:8080/api/home/<USER>\",\n      //       {\n      //         method: \"POST\",\n      //         headers: {\n      //           \"Content-Type\": \"application/json\",\n      //         },\n      //         body: JSON.stringify({\n      //           user: user,\n      //         }),\n      //       }\n      //     );\n\n      //     if (!response.ok) {\n      //       throw new Error(\"Network response was not ok\");\n      //     }\n\n      //     const data = await response.json();\n      //     console.log(\"products:\", data);\n      //     setTopproduct(data);\n      //   } catch (error) {\n      //     console.error(\"Error fetching income:\", error);\n      //   }\n      // };\n      const get_pending = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"pending:\", data);\n          setPending(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_activity = async () => {\n        try {\n          const activity = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          const data = await activity.json();\n          setAct(data.events);\n        } catch (error) {\n          console.error(\"Error fetching activity:\", error);\n        }\n      };\n      await Promise.all([get_revenue(), get_income(), get_customer(),\n      // get_report_customer(),\n      // get_top_product(),\n      get_pending(), get_activity()]);\n    };\n    fetchData();\n  }, [loading]); // Thêm 'user' vào dependencies nếu cần\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-inner\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Trang ch\\u1EE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Made by team 25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Manage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Add Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row row-card-no-pd\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        style: {\n                          whiteSpace: \"nowrap\",\n                          overflow: \"hidden\",\n                          textOverflow: \"ellipsis\"\n                        },\n                        children: \"Todays Income\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted\",\n                      children: \"All Customs Value\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-info fw-bold\",\n                    children: totalincome.profitToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar bg-info\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${totalincome.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: [totalincome.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + totalincome.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Total Revenue\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 376,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted\",\n                      children: \"All Customs Value\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-success fw-bold\",\n                    children: totalrevenue.totalRevenueToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar bg-success\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${totalrevenue.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: [totalrevenue.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + totalrevenue.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Pending order\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted\",\n                      children: \"Fresh Order Amount\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-danger fw-bold\",\n                    children: pending.total\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar bg-danger\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${pending.percent}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: pending.percent\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"New Customer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted\",\n                      children: \"Joined New User\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-secondary fw-bold\",\n                    children: newcustomer.customerToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-bar bg-secondary\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${newcustomer.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: [newcustomer.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + newcustomer.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row row-card-no-pd\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-head-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-title\",\n                    children: \"Th\\u1ED1ng k\\xEA kh\\xE1ch h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-tools\",\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"#\",\n                      className: \"btn btn-label-success btn-round btn-sm me-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-label\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fa fa-pencil\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 471,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 25\n                      }, this), \"Export\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"#\",\n                      className: \"btn btn-label-info btn-round btn-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-label\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fa fa-print\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 480,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 479,\n                        columnNumber: 25\n                      }, this), \"Print\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"chart-container\",\n                  style: {\n                    minHeight: \"375px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 400,\n                    children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                      data: datas,\n                      children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                        dataKey: \"name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                        type: \"number\",\n                        domain: [0, \"dataMax\"]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 495,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n                        strokeDasharray: \"3 3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"kh\\xE1ch h\\xE0ng m\\u1EDBi\",\n                        stroke: \"#ffa726\",\n                        fill: \"#1e88e5\",\n                        fillOpacity: 0.8\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"Kh\\xE1ch h\\xE0ng trung th\\xE0nh\",\n                        stroke: \"#ff6b6b\",\n                        fill: \"red\",\n                        fillOpacity: 0.6\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"Kh\\xE1ch h\\xE0ng quay l\\u1EA1i\",\n                        stroke: \"#2196f3\",\n                        fill: \"#0277bd\",\n                        fillOpacity: 0.4\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"myChartLegend\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card card-primary\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card card-primary\",\n                children: /*#__PURE__*/_jsxDEV(Sales_daily, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: /*#__PURE__*/_jsxDEV(Useronline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: \"L\\u1ECBch l\\xE0m vi\\u1EC7c\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body p-0\",\n                children: /*#__PURE__*/_jsxDEV(CalendarComponent, {\n                  defaultView: \"month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            style: {\n              maxHeight: \"645px\",\n              overflowY: \"auto\",\n              marginBottom: \"15px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: \"Top Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body pb-0\",\n                children: [topproduct.map((a, b) => {\n                  if (b >= 1) {\n                    return /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"separator-dashed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"avatar\",\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: a.image ? a.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n                            alt: \"...\",\n                            className: \"avatar-img rounded-circle\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 646,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 645,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 pt-1 ms-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"fw-bold mb-1\",\n                            children: a.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 657,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 656,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex ms-auto align-items-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: \"text-info fw-bold\",\n                            children: a.rate\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 661,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 660,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex \",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"avatar\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: a.image ? a.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n                        alt: \"...\",\n                        className: \"avatar-img rounded-circle\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 pt-1 ms-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"fw-bold mb-1\",\n                        children: a.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex ms-auto align-items-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-info fw-bold\",\n                        children: a.rate\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 23\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pull-in\",\n                  children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                    id: \"topProductsChart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          style: {\n            marginTop: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-head-row card-tools-still-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-title\",\n                    children: \"Recent Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-tools\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"ol\", {\n                  className: \"activity-feed\",\n                  children: act.map(act => {\n                    return /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"feed-item \" + act.type,\n                      children: [/*#__PURE__*/_jsxDEV(\"time\", {\n                        className: \"date\",\n                        datetime: act.date,\n                        children: act.date\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text\",\n                        dangerouslySetInnerHTML: {\n                          __html: act.detail // Hiển thị HTML (thẻ <br /> sẽ được xử lý)\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 794,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-head-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-title\",\n                    children: \"Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"card-tools\",\n                    children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: \"nav nav-pills nav-secondary nav-pills-no-bd nav-sm\",\n                      id: \"pills-tab\",\n                      role: \"tablist\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        className: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          className: \"nav-link\",\n                          id: \"pills-today\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-today\",\n                          role: \"tab\",\n                          \"aria-selected\": \"true\",\n                          children: \"Today\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 873,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 872,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          className: \"nav-link active\",\n                          id: \"pills-week\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-week\",\n                          role: \"tab\",\n                          \"aria-selected\": \"false\",\n                          children: \"Week\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 885,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 884,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          className: \"nav-link\",\n                          id: \"pills-month\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-month\",\n                          role: \"tab\",\n                          \"aria-selected\": \"false\",\n                          children: \"Month\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 897,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 896,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avatar avatar-online\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"avatar-title rounded-circle border border-white bg-info\",\n                      children: \"J\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Joko Subianto\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-warning ps-3\",\n                        children: \"pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"I am facing some trouble with my viewport. When i start my\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"8:40 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 930,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avatar avatar-offline\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"avatar-title rounded-circle border border-white bg-secondary\",\n                      children: \"P\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 935,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Prabowo Widodo\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-success ps-3\",\n                        children: \"open\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 943,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"I have some query regarding the license issue.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 945,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"1 Day Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 950,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avatar avatar-away\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"avatar-title rounded-circle border border-white bg-danger\",\n                      children: \"L\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 956,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Lee Chong Wei\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-muted ps-3\",\n                        children: \"closed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 961,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Is there any update plan for RTL version near future?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 965,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"2 Days Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avatar avatar-offline\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"avatar-title rounded-circle border border-white bg-secondary\",\n                      children: \"P\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 976,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 975,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Peter Parker\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-success ps-3\",\n                        children: \"open\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 983,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"I have some query regarding the license issue.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"2 Day Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 990,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 989,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"avatar avatar-away\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"avatar-title rounded-circle border border-white bg-danger\",\n                      children: \"L\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Logan Paul\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-muted ps-3\",\n                        children: \"closed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1003,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1001,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Is there any update plan for RTL version near future?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1005,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"2 Days Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"pull-left\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                children: \"TeaM_25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                children: [\" \", \"Help\", \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                children: [\" \", \"Licenses\", \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"copyright\",\n          children: [\"2024, made with \", /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fa fa-heart heart text-danger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1043,\n            columnNumber: 29\n          }, this), \" by team_25\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Distributed by\", /*#__PURE__*/_jsxDEV(\"a\", {\n            target: \"_blank\",\n            href: \"https://themewagon.com/\",\n            children: \"team_25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1019,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Home, \"C/LdXGOHJT+1fiSVoulkKeCWn78=\", false, function () {\n  return [useAuth];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "Sales_daily", "Useronline", "CalendarComponent", "AreaChart", "Area", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "user", "loading", "totalrevenue", "setTotalrevenue", "percentChange", "totalRevenueToday", "state", "totalincome", "setTotalincome", "profitToday", "profitYesterday", "message", "data", "setData", "topproduct", "setTopproduct", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customerToday", "customerYesterday", "pending", "setPending", "total", "percent", "act", "setAct", "datas", "name", "fetchData", "get_revenue", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "console", "log", "error", "get_income", "get_customer", "get_pending", "get_activity", "activity", "events", "Promise", "all", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "style", "whiteSpace", "overflow", "textOverflow", "role", "width", "minHeight", "height", "dataKey", "type", "domain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "fill", "fillOpacity", "id", "defaultView", "maxHeight", "overflowY", "marginBottom", "map", "a", "b", "src", "image", "secure_url", "alt", "rate", "marginTop", "datetime", "date", "dangerouslySetInnerHTML", "__html", "detail", "target", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport Sales_daily from \"./sale_daily\";\r\nimport Useronline from \"./useronlinecard\";\r\n// src/index.js hoặc src/App.js'\r\nimport CalendarComponent from \"../Calendar/index.js\";\r\n// import React from 'react';\r\nimport {\r\n  AreaChart,\r\n  Area,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n} from \"recharts\";\r\n\r\nimport \"./x1.css\";\r\nfunction Home() {\r\n  const { user, loading } = useAuth();\r\n  const [totalrevenue, setTotalrevenue] = useState({\r\n    percentChange: \"0%\",\r\n    totalRevenueToday: \"0\",\r\n    state: \"\",\r\n  });\r\n  const [totalincome, setTotalincome] = useState({\r\n    profitToday: 0,\r\n    profitYesterday: 0,\r\n    percentChange: \"0%\",\r\n    message: \"notchange\",\r\n  });\r\n  const [data, setData] = useState([]);\r\n  const [topproduct, setTopproduct] = useState([]);\r\n  const [newcustomer, setNewcustomer] = useState({\r\n    customerToday: 0,\r\n    customerYesterday: 0,\r\n    percentChange: \"0%\",\r\n    state: \"notchange\",\r\n  });\r\n  const [pending, setPending] = useState({ total: 0, percent: \"0%\" });\r\n  const [act, setAct] = useState([]);\r\n  const datas = [\r\n    {\r\n      name: \"Jan\",\r\n      \"Khách hàng trung thành\": 270,\r\n      \"khách hàng mới\": 150,\r\n      \"Khách hàng quay lại\": 542,\r\n    },\r\n    {\r\n      name: \"Feb\",\r\n      \"Khách hàng trung thành\": 310,\r\n      \"khách hàng mới\": 180,\r\n      \"Khách hàng quay lại\": 520,\r\n    },\r\n    {\r\n      name: \"Mar\",\r\n      \"Khách hàng trung thành\": 350,\r\n      \"khách hàng mới\": 200,\r\n      \"Khách hàng quay lại\": 560,\r\n    },\r\n    {\r\n      name: \"Apr\",\r\n      \"Khách hàng trung thành\": 330,\r\n      \"khách hàng mới\": 220,\r\n      \"Khách hàng quay lại\": 480,\r\n    },\r\n    {\r\n      name: \"May\",\r\n      \"Khách hàng trung thành\": 450,\r\n      \"khách hàng mới\": 260,\r\n      \"Khách hàng quay lại\": 550,\r\n    },\r\n    {\r\n      name: \"Jun\",\r\n      \"Khách hàng trung thành\": 400,\r\n      \"khách hàng mới\": 290,\r\n      \"Khách hàng quay lại\": 580,\r\n    },\r\n    {\r\n      name: \"Jul\",\r\n      \"Khách hàng trung thành\": 460,\r\n      \"khách hàng mới\": 320,\r\n      \"Khách hàng quay lại\": 620,\r\n    },\r\n    {\r\n      name: \"Aug\",\r\n      \"Khách hàng trung thành\": 510,\r\n      \"khách hàng mới\": 340,\r\n      \"Khách hàng quay lại\": 680,\r\n    },\r\n    {\r\n      name: \"Sep\",\r\n      \"Khách hàng trung thành\": 252,\r\n      \"khách hàng mới\": 360,\r\n      \"Khách hàng quay lại\": 740,\r\n    },\r\n    {\r\n      name: \"Oct\",\r\n      \"Khách hàng trung thành\": 680,\r\n      \"khách hàng mới\": 390,\r\n      \"Khách hàng quay lại\": 820,\r\n    },\r\n    {\r\n      name: \"Nov\",\r\n      \"Khách hàng trung thành\": 780,\r\n      \"khách hàng mới\": 420,\r\n      \"Khách hàng quay lại\": 890,\r\n    },\r\n    {\r\n      name: \"Dec\",\r\n      \"Khách hàng trung thành\": 900,\r\n      \"khách hàng mới\": 450,\r\n      \"Khách hàng quay lại\": 980,\r\n    },\r\n  ];\r\n\r\n  // if (!user) {\r\n  //   return <div>Không có người dùng nào đăng nhập.</div>;\r\n  // }\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (loading) return;\r\n      const get_revenue = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"Revenue:\", data);\r\n          setTotalrevenue(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching revenue:\", error);\r\n        }\r\n      };\r\n\r\n      const get_income = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"Income:\", data);\r\n          setTotalincome(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_customer = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"customer:\", data);\r\n          setNewcustomer(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      // const get_report_customer = async () => {\r\n      //   try {\r\n      //     const response = await fetch(\r\n      //       \"http://localhost:8080/api/home/<USER>\",\r\n      //       {\r\n      //         method: \"POST\",\r\n      //         headers: {\r\n      //           \"Content-Type\": \"application/json\",\r\n      //         },\r\n      //         body: JSON.stringify({\r\n      //           user: user,\r\n      //         }),\r\n      //       }\r\n      //     );\r\n\r\n      //     if (!response.ok) {\r\n      //       throw new Error(\"Network response was not ok\");\r\n      //     }\r\n\r\n      //     const data = await response.json();\r\n      //     console.log(\"customer:\", data);\r\n      //     setData(data);\r\n      //   } catch (error) {\r\n      //     console.error(\"Error fetching income:\", error);\r\n      //   }\r\n      // };\r\n      // const get_top_product = async () => {\r\n      //   try {\r\n      //     const response = await fetch(\r\n      //       \"http://localhost:8080/api/home/<USER>\",\r\n      //       {\r\n      //         method: \"POST\",\r\n      //         headers: {\r\n      //           \"Content-Type\": \"application/json\",\r\n      //         },\r\n      //         body: JSON.stringify({\r\n      //           user: user,\r\n      //         }),\r\n      //       }\r\n      //     );\r\n\r\n      //     if (!response.ok) {\r\n      //       throw new Error(\"Network response was not ok\");\r\n      //     }\r\n\r\n      //     const data = await response.json();\r\n      //     console.log(\"products:\", data);\r\n      //     setTopproduct(data);\r\n      //   } catch (error) {\r\n      //     console.error(\"Error fetching income:\", error);\r\n      //   }\r\n      // };\r\n      const get_pending = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n          const data = await response.json();\r\n          console.log(\"pending:\", data);\r\n          setPending(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_activity = async () => {\r\n        try {\r\n          const activity = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n          const data = await activity.json();\r\n\r\n          setAct(data.events);\r\n        } catch (error) {\r\n          console.error(\"Error fetching activity:\", error);\r\n        }\r\n      };\r\n\r\n      await Promise.all([\r\n        get_revenue(),\r\n        get_income(),\r\n        get_customer(),\r\n        // get_report_customer(),\r\n        // get_top_product(),\r\n        get_pending(),\r\n        get_activity(),\r\n      ]);\r\n    };\r\n\r\n    fetchData();\r\n  }, [loading]); // Thêm 'user' vào dependencies nếu cần\r\n\r\n  return (\r\n    <>\r\n      <div className=\"container\">\r\n        <div className=\"page-inner\">\r\n          <div className=\"dashboard-container\">\r\n            <div className=\"dashboard-title\">\r\n              <h3>Trang chủ</h3>\r\n              <h6>Made by team 25</h6>\r\n            </div>\r\n            <div className=\"dashboard-actions\">\r\n              <a href=\"#\">Manage</a>\r\n              <a href=\"#\">Add Customer</a>\r\n            </div>\r\n          </div>\r\n          <div className=\"row row-card-no-pd\">\r\n            <div className=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div className=\"card\">\r\n                <div className=\"card-body\">\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b\r\n                          style={{\r\n                            whiteSpace: \"nowrap\",\r\n                            overflow: \"hidden\",\r\n                            textOverflow: \"ellipsis\",\r\n                          }}\r\n                        >\r\n                          Todays Income\r\n                        </b>\r\n                      </h6>\r\n                      <p className=\"text-muted\">All Customs Value</p>\r\n                    </div>\r\n                    <h4 className=\"text-info fw-bold\">\r\n                      {totalincome.profitToday}\r\n                    </h4>\r\n                  </div>\r\n                  <div className=\"progress progress-sm\">\r\n                    <div\r\n                      className=\"progress-bar bg-info\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${totalincome.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <p className=\"text-muted\">Change</p>\r\n                    <p className=\"text-muted\">\r\n                      {totalincome.percentChange}\r\n                      <small>{\" \" + totalincome.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div className=\"card\">\r\n                <div className=\"card-body\">\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>Total Revenue</b>\r\n                      </h6>\r\n                      <p className=\"text-muted\">All Customs Value</p>\r\n                    </div>\r\n                    <h4 className=\"text-success fw-bold\">\r\n                      {totalrevenue.totalRevenueToday}\r\n                    </h4>\r\n                  </div>\r\n                  <div className=\"progress progress-sm\">\r\n                    <div\r\n                      className=\"progress-bar bg-success\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${totalrevenue.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <p className=\"text-muted\">Change</p>\r\n                    <p className=\"text-muted\">\r\n                      {totalrevenue.percentChange}\r\n                      <small>{\" \" + totalrevenue.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div className=\"card\">\r\n                <div className=\"card-body\">\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>Pending order</b>\r\n                      </h6>\r\n                      <p className=\"text-muted\">Fresh Order Amount</p>\r\n                    </div>\r\n                    <h4 className=\"text-danger fw-bold\">{pending.total}</h4>\r\n                  </div>\r\n                  <div className=\"progress progress-sm\">\r\n                    <div\r\n                      className=\"progress-bar bg-danger\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${pending.percent}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <p className=\"text-muted\">Change</p>\r\n                    <p className=\"text-muted\">{pending.percent}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div className=\"card\">\r\n                <div className=\"card-body\">\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>New Customer</b>\r\n                      </h6>\r\n                      <p className=\"text-muted\">Joined New User</p>\r\n                    </div>\r\n                    <h4 className=\"text-secondary fw-bold\">\r\n                      {newcustomer.customerToday}\r\n                    </h4>\r\n                  </div>\r\n                  <div className=\"progress progress-sm\">\r\n                    <div\r\n                      className=\"progress-bar bg-secondary\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${newcustomer.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <p className=\"text-muted\">Change</p>\r\n                    <p className=\"text-muted\">\r\n                      {newcustomer.percentChange}\r\n                      <small>{\" \" + newcustomer.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"row row-card-no-pd\">\r\n            <div className=\"col-md-8\">\r\n              <div className=\"card\">\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-head-row\">\r\n                    <div className=\"card-title\">Thống kê khách hàng</div>\r\n                    <div className=\"card-tools\">\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"btn btn-label-success btn-round btn-sm me-2\"\r\n                      >\r\n                        <span className=\"btn-label\">\r\n                          <i className=\"fa fa-pencil\"></i>\r\n                        </span>\r\n                        Export\r\n                      </a>\r\n                      <a\r\n                        href=\"#\"\r\n                        className=\"btn btn-label-info btn-round btn-sm\"\r\n                      >\r\n                        <span className=\"btn-label\">\r\n                          <i className=\"fa fa-print\"></i>\r\n                        </span>\r\n                        Print\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"card-body\">\r\n                  <div\r\n                    className=\"chart-container\"\r\n                    style={{ minHeight: \"375px\" }}\r\n                  >\r\n                    <ResponsiveContainer width=\"100%\" height={400}>\r\n                      <AreaChart data={datas}>\r\n                        <XAxis dataKey=\"name\" />\r\n                        <YAxis type=\"number\" domain={[0, \"dataMax\"]} />\r\n                        <CartesianGrid strokeDasharray=\"3 3\" />\r\n                        <Tooltip />\r\n                        <Legend />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"khách hàng mới\"\r\n                          stroke=\"#ffa726\"\r\n                          fill=\"#1e88e5\"\r\n                          fillOpacity={0.8}\r\n                        />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"Khách hàng trung thành\"\r\n                          stroke=\"#ff6b6b\"\r\n                          fill=\"red\"\r\n                          fillOpacity={0.6}\r\n                        />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"Khách hàng quay lại\"\r\n                          stroke=\"#2196f3\"\r\n                          fill=\"#0277bd\"\r\n                          fillOpacity={0.4}\r\n                        />\r\n                      </AreaChart>\r\n                    </ResponsiveContainer>\r\n                  </div>\r\n                  <div id=\"myChartLegend\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-md-4\">\r\n              <div className=\"card card-primary\">\r\n                <div className=\"card card-primary\">\r\n                  <Sales_daily />\r\n                </div>\r\n              </div>\r\n              <div className=\"card\">\r\n                <Useronline />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"row\">\r\n            <div className=\"col-md-8\">\r\n              <div className=\"card\">\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-title\">Lịch làm việc</div>\r\n                </div>\r\n                <div className=\"card-body p-0\">\r\n                  <CalendarComponent defaultView=\"month\" />\r\n                  {/* <div className=\"table-responsive\">\r\n                  <table className=\"table align-items-center\">\r\n                    <thead className=\"thead-light\">\r\n                      <tr>\r\n                        <th scope=\"col\">Page name</th>\r\n                        <th scope=\"col\">Visitors</th>\r\n                        <th scope=\"col\">Unique users</th>\r\n                        <th scope=\"col\">Bounce rate</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/</th>\r\n                        <td>4,569</td>\r\n                        <td>340</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/index.html</th>\r\n                        <td>3,985</td>\r\n                        <td>319</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/charts.html</th>\r\n                        <td>3,513</td>\r\n                        <td>294</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          36,49%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/tables.html</th>\r\n                        <td>2,050</td>\r\n                        <td>147</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          50,87%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/profile.html</th>\r\n                        <td>1,795</td>\r\n                        <td>190</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-down text-danger me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/</th>\r\n                        <td>4,569</td>\r\n                        <td>340</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/index.html</th>\r\n                        <td>3,985</td>\r\n                        <td>319</td>\r\n                        <td>\r\n                          <i className=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div> */}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              className=\"col-md-4\"\r\n              style={{\r\n                maxHeight: \"645px\",\r\n                overflowY: \"auto\",\r\n                marginBottom: \"15px\",\r\n              }}\r\n            >\r\n              <div className=\"card\">\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-title\">Top Products</div>\r\n                </div>\r\n                <div className=\"card-body pb-0\">\r\n                  {topproduct.map((a, b) => {\r\n                    if (b >= 1) {\r\n                      return (\r\n                        <>\r\n                          <div className=\"separator-dashed\"></div>\r\n                          <div className=\"d-flex\">\r\n                            <div className=\"avatar\">\r\n                              <img\r\n                                src={\r\n                                  a.image\r\n                                    ? a.image.secure_url\r\n                                    : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                                }\r\n                                alt=\"...\"\r\n                                className=\"avatar-img rounded-circle\"\r\n                              />\r\n                            </div>\r\n                            <div className=\"flex-1 pt-1 ms-2\">\r\n                              <h6 className=\"fw-bold mb-1\">{a.name}</h6>\r\n                              {/* <small className=\"text-muted\">The Best Donuts</small> */}\r\n                            </div>\r\n                            <div className=\"d-flex ms-auto align-items-center\">\r\n                              <h4 className=\"text-info fw-bold\">{a.rate}</h4>\r\n                            </div>\r\n                          </div>\r\n                        </>\r\n                      );\r\n                    }\r\n                    return (\r\n                      <div className=\"d-flex \">\r\n                        <div className=\"avatar\">\r\n                          <img\r\n                            src={\r\n                              a.image\r\n                                ? a.image.secure_url\r\n                                : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                            }\r\n                            alt=\"...\"\r\n                            className=\"avatar-img rounded-circle\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex-1 pt-1 ms-2\">\r\n                          <h6 className=\"fw-bold mb-1\">{a.name}</h6>\r\n                          {/* <small className=\"text-muted\">Cascading Style Sheets</small> */}\r\n                        </div>\r\n                        <div className=\"d-flex ms-auto align-items-center\">\r\n                          <h4 className=\"text-info fw-bold\">{a.rate}</h4>\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {/* <div className=\"d-flex \">\r\n                  <div className=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct.svg\"\r\n                      alt=\"...\"\r\n                      className=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex-1 pt-1 ms-2\">\r\n                    <h6 className=\"fw-bold mb-1\">CSS</h6>\r\n                    <small className=\"text-muted\">Cascading Style Sheets</small>\r\n                  </div>\r\n                  <div className=\"d-flex ms-auto align-items-center\">\r\n                    <h4 className=\"text-info fw-bold\">+$17</h4>\r\n                  </div>\r\n                </div>\r\n                <div className=\"separator-dashed\"></div>\r\n                <div className=\"d-flex\">\r\n                  <div className=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct.svg\"\r\n                      alt=\"...\"\r\n                      className=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex-1 pt-1 ms-2\">\r\n                    <h6 className=\"fw-bold mb-1\">J.CO Donuts</h6>\r\n                    <small className=\"text-muted\">The Best Donuts</small>\r\n                  </div>\r\n                  <div className=\"d-flex ms-auto align-items-center\">\r\n                    <h4 className=\"text-info fw-bold\">+$300</h4>\r\n                  </div>\r\n                </div>\r\n                <div className=\"separator-dashed\"></div>\r\n                <div className=\"d-flex\">\r\n                  <div className=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct3.svg\"\r\n                      alt=\"...\"\r\n                      className=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex-1 pt-1 ms-2\">\r\n                    <h6 className=\"fw-bold mb-1\">Ready Pro</h6>\r\n                    <small className=\"text-muted\">\r\n                      Bootstrap 5 Admin Dashboard\r\n                    </small>\r\n                  </div>\r\n                  <div className=\"d-flex ms-auto align-items-center\">\r\n                    <h4 className=\"text-info fw-bold\">+$350</h4>\r\n                  </div>\r\n                </div> */}\r\n                  <div className=\"separator-dashed\"></div>\r\n                  <div className=\"pull-in\">\r\n                    <canvas id=\"topProductsChart\"></canvas>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"row\" style={{ marginTop: \"10px\" }}>\r\n            <div className=\"col-md-6\">\r\n              <div className=\"card\">\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-head-row card-tools-still-right\">\r\n                    <div className=\"card-title\">Recent Activity</div>\r\n                    <div className=\"card-tools\">\r\n                      {/* <div className=\"dropdown\">\r\n                      <button\r\n                        className=\"btn btn-icon btn-clean\"\r\n                        type=\"button\"\r\n                        id=\"dropdownMenuButton\"\r\n                        data-bs-toggle=\"dropdown\"\r\n                        aria-haspopup=\"true\"\r\n                        aria-expanded=\"false\"\r\n                      >\r\n                        <i className=\"fas fa-ellipsis-h\"></i>\r\n                      </button>\r\n                      <div\r\n                        className=\"dropdown-menu\"\r\n                        aria-labelledby=\"dropdownMenuButton\"\r\n                      >\r\n                        <a className=\"dropdown-item\" href=\"#\">\r\n                          Action\r\n                        </a>\r\n                        <a className=\"dropdown-item\" href=\"#\">\r\n                          Another action\r\n                        </a>\r\n                        <a className=\"dropdown-item\" href=\"#\">\r\n                          Something else here\r\n                        </a>\r\n                      </div>\r\n                    </div> */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"card-body\">\r\n                  <ol className=\"activity-feed\">\r\n                    {act.map((act) => {\r\n                      return (\r\n                        <li className={\"feed-item \" + act.type}>\r\n                          <time className=\"date\" datetime={act.date}>\r\n                            {act.date}\r\n                          </time>\r\n                          <span\r\n                            className=\"text\"\r\n                            dangerouslySetInnerHTML={{\r\n                              __html: act.detail, // Hiển thị HTML (thẻ <br /> sẽ được xử lý)\r\n                            }}\r\n                          ></span>\r\n                        </li>\r\n                      );\r\n                    })}\r\n                    {/* <li className=\"feed-item feed-item-secondary\">\r\n                    <time className=\"date\" datetime=\"9-25\">\r\n                      Sep 25\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Responded to need\r\n                      <a href=\"#\">\"Volunteer opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li className=\"feed-item feed-item-success\">\r\n                    <time className=\"date\" datetime=\"9-24\">\r\n                      Sep 24\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Added an interest\r\n                      <a href=\"#\">\"Volunteer Activities\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li className=\"feed-item feed-item-info\">\r\n                    <time className=\"date\" datetime=\"9-23\">\r\n                      Sep 23\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Joined the group\r\n                      <a href=\"single-group.php\">\"Boardsmanship Forum\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li className=\"feed-item feed-item-warning\">\r\n                    <time className=\"date\" datetime=\"9-21\">\r\n                      Sep 21\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Responded to need\r\n                      <a href=\"#\">\"In-Kind Opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li className=\"feed-item feed-item-danger\">\r\n                    <time className=\"date\" datetime=\"9-18\">\r\n                      Sep 18\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Created need\r\n                      <a href=\"#\">\"Volunteer Opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li className=\"feed-item\">\r\n                    <time className=\"date\" datetime=\"9-17\">\r\n                      Sep 17\r\n                    </time>\r\n                    <span className=\"text\">\r\n                      Attending the event\r\n                      <a href=\"single-event.php\">\"Some New Event\"</a>\r\n                    </span>\r\n                  </li> */}\r\n                  </ol>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"col-md-6\">\r\n              <div className=\"card\">\r\n                <div className=\"card-header\">\r\n                  <div className=\"card-head-row\">\r\n                    <div className=\"card-title\">Information</div>\r\n                    <div className=\"card-tools\">\r\n                      <ul\r\n                        className=\"nav nav-pills nav-secondary nav-pills-no-bd nav-sm\"\r\n                        id=\"pills-tab\"\r\n                        role=\"tablist\"\r\n                      >\r\n                        <li className=\"nav-item\">\r\n                          <a\r\n                            className=\"nav-link\"\r\n                            id=\"pills-today\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-today\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"true\"\r\n                          >\r\n                            Today\r\n                          </a>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                          <a\r\n                            className=\"nav-link active\"\r\n                            id=\"pills-week\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-week\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"false\"\r\n                          >\r\n                            Week\r\n                          </a>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                          <a\r\n                            className=\"nav-link\"\r\n                            id=\"pills-month\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-month\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"false\"\r\n                          >\r\n                            Month\r\n                          </a>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"card-body\">\r\n                  <div className=\"d-flex\">\r\n                    <div className=\"avatar avatar-online\">\r\n                      <span className=\"avatar-title rounded-circle border border-white bg-info\">\r\n                        J\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex-1 ms-3 pt-1\">\r\n                      <h6 className=\"text-uppercase fw-bold mb-1\">\r\n                        Joko Subianto\r\n                        <span className=\"text-warning ps-3\">pending</span>\r\n                      </h6>\r\n                      <span className=\"text-muted\">\r\n                        I am facing some trouble with my viewport. When i start\r\n                        my\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"float-end pt-1\">\r\n                      <small className=\"text-muted\">8:40 PM</small>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"separator-dashed\"></div>\r\n                  <div className=\"d-flex\">\r\n                    <div className=\"avatar avatar-offline\">\r\n                      <span className=\"avatar-title rounded-circle border border-white bg-secondary\">\r\n                        P\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex-1 ms-3 pt-1\">\r\n                      <h6 className=\"text-uppercase fw-bold mb-1\">\r\n                        Prabowo Widodo\r\n                        <span className=\"text-success ps-3\">open</span>\r\n                      </h6>\r\n                      <span className=\"text-muted\">\r\n                        I have some query regarding the license issue.\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"float-end pt-1\">\r\n                      <small className=\"text-muted\">1 Day Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"separator-dashed\"></div>\r\n                  <div className=\"d-flex\">\r\n                    <div className=\"avatar avatar-away\">\r\n                      <span className=\"avatar-title rounded-circle border border-white bg-danger\">\r\n                        L\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex-1 ms-3 pt-1\">\r\n                      <h6 className=\"text-uppercase fw-bold mb-1\">\r\n                        Lee Chong Wei\r\n                        <span className=\"text-muted ps-3\">closed</span>\r\n                      </h6>\r\n                      <span className=\"text-muted\">\r\n                        Is there any update plan for RTL version near future?\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"float-end pt-1\">\r\n                      <small className=\"text-muted\">2 Days Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"separator-dashed\"></div>\r\n                  <div className=\"d-flex\">\r\n                    <div className=\"avatar avatar-offline\">\r\n                      <span className=\"avatar-title rounded-circle border border-white bg-secondary\">\r\n                        P\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex-1 ms-3 pt-1\">\r\n                      <h6 className=\"text-uppercase fw-bold mb-1\">\r\n                        Peter Parker\r\n                        <span className=\"text-success ps-3\">open</span>\r\n                      </h6>\r\n                      <span className=\"text-muted\">\r\n                        I have some query regarding the license issue.\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"float-end pt-1\">\r\n                      <small className=\"text-muted\">2 Day Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"separator-dashed\"></div>\r\n                  <div className=\"d-flex\">\r\n                    <div className=\"avatar avatar-away\">\r\n                      <span className=\"avatar-title rounded-circle border border-white bg-danger\">\r\n                        L\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex-1 ms-3 pt-1\">\r\n                      <h6 className=\"text-uppercase fw-bold mb-1\">\r\n                        Logan Paul{\" \"}\r\n                        <span className=\"text-muted ps-3\">closed</span>\r\n                      </h6>\r\n                      <span className=\"text-muted\">\r\n                        Is there any update plan for RTL version near future?\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"float-end pt-1\">\r\n                      <small className=\"text-muted\">2 Days Ago</small>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <footer className=\"footer\">\r\n        <div className=\"container-fluid d-flex justify-content-between\">\r\n          <nav className=\"pull-left\">\r\n            <ul className=\"nav\">\r\n              <li className=\"nav-item\">\r\n                <a className=\"nav-link\" href=\"#\">\r\n                  TeaM_25\r\n                </a>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <a className=\"nav-link\" href=\"#\">\r\n                  {\" \"}\r\n                  Help{\" \"}\r\n                </a>\r\n              </li>\r\n              <li className=\"nav-item\">\r\n                <a className=\"nav-link\" href=\"#\">\r\n                  {\" \"}\r\n                  Licenses{\" \"}\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n          <div className=\"copyright\">\r\n            2024, made with <i className=\"fa fa-heart heart text-danger\"></i> by\r\n            team_25\r\n          </div>\r\n          <div>\r\n            Distributed by\r\n            <a target=\"_blank\" href=\"https://themewagon.com/\">\r\n              team_25\r\n            </a>\r\n            .\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,UAAU,MAAM,kBAAkB;AACzC;AACA,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAEjB,OAAO,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAClB,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACnC,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC;IAC/CuB,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,GAAG;IACtBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC;IAC7C4B,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBN,aAAa,EAAE,IAAI;IACnBO,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC;IAC7CqC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBf,aAAa,EAAE,IAAI;IACnBE,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,KAAK,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EACnE,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM6C,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,CACF;;EAED;EACA;EACA;EACA/C,SAAS,CAAC,MAAM;IACd,MAAMgD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI3B,OAAO,EAAE;MACb,MAAM4B,WAAW,GAAG,MAAAA,CAAA,KAAY;QAC9B,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8C,EAC9C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE7B,IAAI,CAAC;UAC7BT,eAAe,CAACS,IAAI,CAAC;QACvB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QACjD;MACF,CAAC;MAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B,IAAI;UACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAC1B,6CAA6C,EAC7C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE7B,IAAI,CAAC;UAC5BJ,cAAc,CAACI,IAAI,CAAC;QACtB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;QAC/B,IAAI;UACF,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAC1B,6CAA6C,EAC7C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7B,IAAI,CAAC;UAC9BK,cAAc,CAACL,IAAI,CAAC;QACtB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;QAC9B,IAAI;UACF,MAAMf,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8C,EAC9C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UACA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE7B,IAAI,CAAC;UAC7BS,UAAU,CAACT,IAAI,CAAC;QAClB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;QAC/B,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAC1B,gDAAgD,EAChD;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UACD,MAAMY,IAAI,GAAG,MAAMmC,QAAQ,CAACR,IAAI,CAAC,CAAC;UAElCd,MAAM,CAACb,IAAI,CAACoC,MAAM,CAAC;QACrB,CAAC,CAAC,OAAON,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC;MAED,MAAMO,OAAO,CAACC,GAAG,CAAC,CAChBrB,WAAW,CAAC,CAAC,EACbc,UAAU,CAAC,CAAC,EACZC,YAAY,CAAC,CAAC;MACd;MACA;MACAC,WAAW,CAAC,CAAC,EACbC,YAAY,CAAC,CAAC,CACf,CAAC;IACJ,CAAC;IAEDlB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,oBACEN,OAAA,CAAAE,SAAA;IAAAsD,QAAA,gBACExD,OAAA;MAAKyD,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxBxD,OAAA;QAAKyD,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACzBxD,OAAA;UAAKyD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAClCxD,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BxD,OAAA;cAAAwD,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB7D,OAAA;cAAAwD,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCxD,OAAA;cAAG8D,IAAI,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtB7D,OAAA;cAAG8D,IAAI,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA;UAAKyD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACjCxD,OAAA;YAAKyD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAChDxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBxD,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,eACExD,OAAA;wBACE+D,KAAK,EAAE;0BACLC,UAAU,EAAE,QAAQ;0BACpBC,QAAQ,EAAE,QAAQ;0BAClBC,YAAY,EAAE;wBAChB,CAAE;wBAAAV,QAAA,EACH;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL7D,OAAA;sBAAGyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACN7D,OAAA;oBAAIyD,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAC9B5C,WAAW,CAACE;kBAAW;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnCxD,OAAA;oBACEyD,SAAS,EAAC,sBAAsB;oBAChCU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAGxD,WAAW,CAACH,aAAa;oBAAG;kBAAE;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACpC7D,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,GACtB5C,WAAW,CAACH,aAAa,eAC1BT,OAAA;sBAAAwD,QAAA,EAAQ,GAAG,GAAG5C,WAAW,CAACD;oBAAK;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAChDxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBxD,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,eACExD,OAAA;wBAAAwD,QAAA,EAAG;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACL7D,OAAA;sBAAGyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACN7D,OAAA;oBAAIyD,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,EACjCjD,YAAY,CAACG;kBAAiB;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnCxD,OAAA;oBACEyD,SAAS,EAAC,yBAAyB;oBACnCU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG7D,YAAY,CAACE,aAAa;oBAAG;kBAAE;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACpC7D,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,GACtBjD,YAAY,CAACE,aAAa,eAC3BT,OAAA;sBAAAwD,QAAA,EAAQ,GAAG,GAAGjD,YAAY,CAACI;oBAAK;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAChDxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBxD,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,eACExD,OAAA;wBAAAwD,QAAA,EAAG;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACL7D,OAAA;sBAAGyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACN7D,OAAA;oBAAIyD,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,EAAE/B,OAAO,CAACE;kBAAK;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnCxD,OAAA;oBACEyD,SAAS,EAAC,wBAAwB;oBAClCU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG3C,OAAO,CAACG,OAAO;oBAAG;kBAAE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACpC7D,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAE/B,OAAO,CAACG;kBAAO;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,eAChDxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBxD,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAAwD,QAAA,gBACExD,OAAA;sBAAAwD,QAAA,eACExD,OAAA;wBAAAwD,QAAA,EAAG;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACL7D,OAAA;sBAAGyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACN7D,OAAA;oBAAIyD,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EACnCnC,WAAW,CAACE;kBAAa;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnCxD,OAAA;oBACEyD,SAAS,EAAC,2BAA2B;oBACrCU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG/C,WAAW,CAACZ,aAAa;oBAAG;kBAAE;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7CxD,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACpC7D,OAAA;oBAAGyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,GACtBnC,WAAW,CAACZ,aAAa,eAC1BT,OAAA;sBAAAwD,QAAA,EAAQ,GAAG,GAAGnC,WAAW,CAACV;oBAAK;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA;UAAKyD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,gBACjCxD,OAAA;YAAKyD,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxD,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BxD,OAAA;kBAAKyD,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5BxD,OAAA;oBAAKyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD7D,OAAA;oBAAKyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBACzBxD,OAAA;sBACE8D,IAAI,EAAC,GAAG;sBACRL,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,gBAEvDxD,OAAA;wBAAMyD,SAAS,EAAC,WAAW;wBAAAD,QAAA,eACzBxD,OAAA;0BAAGyD,SAAS,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,UAET;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ7D,OAAA;sBACE8D,IAAI,EAAC,GAAG;sBACRL,SAAS,EAAC,qCAAqC;sBAAAD,QAAA,gBAE/CxD,OAAA;wBAAMyD,SAAS,EAAC,WAAW;wBAAAD,QAAA,eACzBxD,OAAA;0BAAGyD,SAAS,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC,SAET;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBACEyD,SAAS,EAAC,iBAAiB;kBAC3BM,KAAK,EAAE;oBAAEM,SAAS,EAAE;kBAAQ,CAAE;kBAAAb,QAAA,eAE9BxD,OAAA,CAACF,mBAAmB;oBAACsE,KAAK,EAAC,MAAM;oBAACE,MAAM,EAAE,GAAI;oBAAAd,QAAA,eAC5CxD,OAAA,CAACT,SAAS;sBAAC0B,IAAI,EAAEc,KAAM;sBAAAyB,QAAA,gBACrBxD,OAAA,CAACP,KAAK;wBAAC8E,OAAO,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxB7D,OAAA,CAACN,KAAK;wBAAC8E,IAAI,EAAC,QAAQ;wBAACC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/C7D,OAAA,CAACL,aAAa;wBAAC+E,eAAe,EAAC;sBAAK;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvC7D,OAAA,CAACJ,OAAO;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACX7D,OAAA,CAACH,MAAM;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACV7D,OAAA,CAACR,IAAI;wBACHgF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,2BAAgB;wBACxBI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,SAAS;wBACdC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACF7D,OAAA,CAACR,IAAI;wBACHgF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,iCAAwB;wBAChCI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,KAAK;wBACVC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACF7D,OAAA,CAACR,IAAI;wBACHgF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,gCAAqB;wBAC7BI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,SAAS;wBACdC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACN7D,OAAA;kBAAK8E,EAAE,EAAC;gBAAe;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACvBxD,OAAA;cAAKyD,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAChCxD,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,eAChCxD,OAAA,CAACZ,WAAW;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7D,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBxD,OAAA,CAACX,UAAU;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA;UAAKyD,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBxD,OAAA;YAAKyD,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxD,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BxD,OAAA;kBAAKyD,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN7D,OAAA;gBAAKyD,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5BxD,OAAA,CAACV,iBAAiB;kBAACyF,WAAW,EAAC;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8EtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YACEyD,SAAS,EAAC,UAAU;YACpBM,KAAK,EAAE;cACLiB,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,MAAM;cACjBC,YAAY,EAAE;YAChB,CAAE;YAAA1B,QAAA,eAEFxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxD,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BxD,OAAA;kBAAKyD,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACN7D,OAAA;gBAAKyD,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,GAC5BrC,UAAU,CAACgE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;kBACxB,IAAIA,CAAC,IAAI,CAAC,EAAE;oBACV,oBACErF,OAAA,CAAAE,SAAA;sBAAAsD,QAAA,gBACExD,OAAA;wBAAKyD,SAAS,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxC7D,OAAA;wBAAKyD,SAAS,EAAC,QAAQ;wBAAAD,QAAA,gBACrBxD,OAAA;0BAAKyD,SAAS,EAAC,QAAQ;0BAAAD,QAAA,eACrBxD,OAAA;4BACEsF,GAAG,EACDF,CAAC,CAACG,KAAK,GACHH,CAAC,CAACG,KAAK,CAACC,UAAU,GAClB,+KACL;4BACDC,GAAG,EAAC,KAAK;4BACThC,SAAS,EAAC;0BAA2B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN7D,OAAA;0BAAKyD,SAAS,EAAC,kBAAkB;0BAAAD,QAAA,eAC/BxD,OAAA;4BAAIyD,SAAS,EAAC,cAAc;4BAAAD,QAAA,EAAE4B,CAAC,CAACpD;0BAAI;4BAAA0B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEvC,CAAC,eACN7D,OAAA;0BAAKyD,SAAS,EAAC,mCAAmC;0BAAAD,QAAA,eAChDxD,OAAA;4BAAIyD,SAAS,EAAC,mBAAmB;4BAAAD,QAAA,EAAE4B,CAAC,CAACM;0BAAI;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,eACN,CAAC;kBAEP;kBACA,oBACE7D,OAAA;oBAAKyD,SAAS,EAAC,SAAS;oBAAAD,QAAA,gBACtBxD,OAAA;sBAAKyD,SAAS,EAAC,QAAQ;sBAAAD,QAAA,eACrBxD,OAAA;wBACEsF,GAAG,EACDF,CAAC,CAACG,KAAK,GACHH,CAAC,CAACG,KAAK,CAACC,UAAU,GAClB,+KACL;wBACDC,GAAG,EAAC,KAAK;wBACThC,SAAS,EAAC;sBAA2B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN7D,OAAA;sBAAKyD,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,eAC/BxD,OAAA;wBAAIyD,SAAS,EAAC,cAAc;wBAAAD,QAAA,EAAE4B,CAAC,CAACpD;sBAAI;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEvC,CAAC,eACN7D,OAAA;sBAAKyD,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,eAChDxD,OAAA;wBAAIyD,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,EAAE4B,CAAC,CAACM;sBAAI;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAEV,CAAC,CAAC,eAqDF7D,OAAA;kBAAKyD,SAAS,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7D,OAAA;kBAAKyD,SAAS,EAAC,SAAS;kBAAAD,QAAA,eACtBxD,OAAA;oBAAQ8E,EAAE,EAAC;kBAAkB;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA;UAAKyD,SAAS,EAAC,KAAK;UAACM,KAAK,EAAE;YAAE4B,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,gBAChDxD,OAAA;YAAKyD,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxD,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BxD,OAAA;kBAAKyD,SAAS,EAAC,sCAAsC;kBAAAD,QAAA,gBACnDxD,OAAA;oBAAKyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjD7D,OAAA;oBAAKyD,SAAS,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,eACxBxD,OAAA;kBAAIyD,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAC1B3B,GAAG,CAACsD,GAAG,CAAEtD,GAAG,IAAK;oBAChB,oBACE7B,OAAA;sBAAIyD,SAAS,EAAE,YAAY,GAAG5B,GAAG,CAAC2C,IAAK;sBAAAhB,QAAA,gBACrCxD,OAAA;wBAAMyD,SAAS,EAAC,MAAM;wBAACmC,QAAQ,EAAE/D,GAAG,CAACgE,IAAK;wBAAArC,QAAA,EACvC3B,GAAG,CAACgE;sBAAI;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACP7D,OAAA;wBACEyD,SAAS,EAAC,MAAM;wBAChBqC,uBAAuB,EAAE;0BACvBC,MAAM,EAAElE,GAAG,CAACmE,MAAM,CAAE;wBACtB;sBAAE;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAET,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuDA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKyD,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBxD,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBxD,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAD,QAAA,eAC1BxD,OAAA;kBAAKyD,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5BxD,OAAA;oBAAKyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7C7D,OAAA;oBAAKyD,SAAS,EAAC,YAAY;oBAAAD,QAAA,eACzBxD,OAAA;sBACEyD,SAAS,EAAC,oDAAoD;sBAC9DqB,EAAE,EAAC,WAAW;sBACdX,IAAI,EAAC,SAAS;sBAAAX,QAAA,gBAEdxD,OAAA;wBAAIyD,SAAS,EAAC,UAAU;wBAAAD,QAAA,eACtBxD,OAAA;0BACEyD,SAAS,EAAC,UAAU;0BACpBqB,EAAE,EAAC,aAAa;0BAChB,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,cAAc;0BACnBK,IAAI,EAAC,KAAK;0BACV,iBAAc,MAAM;0BAAAX,QAAA,EACrB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL7D,OAAA;wBAAIyD,SAAS,EAAC,UAAU;wBAAAD,QAAA,eACtBxD,OAAA;0BACEyD,SAAS,EAAC,iBAAiB;0BAC3BqB,EAAE,EAAC,YAAY;0BACf,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,aAAa;0BAClBK,IAAI,EAAC,KAAK;0BACV,iBAAc,OAAO;0BAAAX,QAAA,EACtB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL7D,OAAA;wBAAIyD,SAAS,EAAC,UAAU;wBAAAD,QAAA,eACtBxD,OAAA;0BACEyD,SAAS,EAAC,UAAU;0BACpBqB,EAAE,EAAC,aAAa;0BAChB,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,cAAc;0BACnBK,IAAI,EAAC,KAAK;0BACV,iBAAc,OAAO;0BAAAX,QAAA,EACtB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7D,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBxD,OAAA;kBAAKyD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxD,OAAA;oBAAKyD,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,eACnCxD,OAAA;sBAAMyD,SAAS,EAAC,yDAAyD;sBAAAD,QAAA,EAAC;oBAE1E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BxD,OAAA;sBAAIyD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,eAE1C,eAAAxD,OAAA;wBAAMyD,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACL7D,OAAA;sBAAMyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAG7B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAC7BxD,OAAA;sBAAOyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7D,OAAA;kBAAKyD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxD,OAAA;oBAAKyD,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,eACpCxD,OAAA;sBAAMyD,SAAS,EAAC,8DAA8D;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BxD,OAAA;sBAAIyD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,gBAE1C,eAAAxD,OAAA;wBAAMyD,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACL7D,OAAA;sBAAMyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAE7B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAC7BxD,OAAA;sBAAOyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7D,OAAA;kBAAKyD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxD,OAAA;oBAAKyD,SAAS,EAAC,oBAAoB;oBAAAD,QAAA,eACjCxD,OAAA;sBAAMyD,SAAS,EAAC,2DAA2D;sBAAAD,QAAA,EAAC;oBAE5E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BxD,OAAA;sBAAIyD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,eAE1C,eAAAxD,OAAA;wBAAMyD,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACL7D,OAAA;sBAAMyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAE7B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAC7BxD,OAAA;sBAAOyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7D,OAAA;kBAAKyD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxD,OAAA;oBAAKyD,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,eACpCxD,OAAA;sBAAMyD,SAAS,EAAC,8DAA8D;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BxD,OAAA;sBAAIyD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,cAE1C,eAAAxD,OAAA;wBAAMyD,SAAS,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACL7D,OAAA;sBAAMyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAE7B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAC7BxD,OAAA;sBAAOyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKyD,SAAS,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC7D,OAAA;kBAAKyD,SAAS,EAAC,QAAQ;kBAAAD,QAAA,gBACrBxD,OAAA;oBAAKyD,SAAS,EAAC,oBAAoB;oBAAAD,QAAA,eACjCxD,OAAA;sBAAMyD,SAAS,EAAC,2DAA2D;sBAAAD,QAAA,EAAC;oBAE5E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BxD,OAAA;sBAAIyD,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,YAChC,EAAC,GAAG,eACdxD,OAAA;wBAAMyD,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACL7D,OAAA;sBAAMyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAE7B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7D,OAAA;oBAAKyD,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAC7BxD,OAAA;sBAAOyD,SAAS,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN7D,OAAA;MAAQyD,SAAS,EAAC,QAAQ;MAAAD,QAAA,eACxBxD,OAAA;QAAKyD,SAAS,EAAC,gDAAgD;QAAAD,QAAA,gBAC7DxD,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAD,QAAA,eACxBxD,OAAA;YAAIyD,SAAS,EAAC,KAAK;YAAAD,QAAA,gBACjBxD,OAAA;cAAIyD,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBxD,OAAA;gBAAGyD,SAAS,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,EAAC;cAEjC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACL7D,OAAA;cAAIyD,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBxD,OAAA;gBAAGyD,SAAS,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,GAC7B,GAAG,EAAC,MACD,EAAC,GAAG;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACL7D,OAAA;cAAIyD,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBxD,OAAA;gBAAGyD,SAAS,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,GAC7B,GAAG,EAAC,UACG,EAAC,GAAG;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAD,QAAA,GAAC,kBACT,eAAAxD,OAAA;YAAGyD,SAAS,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEnE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7D,OAAA;UAAAwD,QAAA,GAAK,gBAEH,eAAAxD,OAAA;YAAGiG,MAAM,EAAC,QAAQ;YAACnC,IAAI,EAAC,yBAAyB;YAAAN,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,KAEN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACT,CAAC;AAEP;AAACzD,EAAA,CA7gCQD,IAAI;EAAA,QACehB,OAAO;AAAA;AAAA+G,EAAA,GAD1B/F,IAAI;AA+gCb,eAAeA,IAAI;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}