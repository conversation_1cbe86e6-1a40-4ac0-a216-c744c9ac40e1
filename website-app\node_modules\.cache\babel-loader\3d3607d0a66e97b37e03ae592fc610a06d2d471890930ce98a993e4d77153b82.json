{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\introduce\\\\intro.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./intro.css\";\nimport Cookies from \"js-cookie\";\nimport { GoogleOAuthProvider, GoogleLogin } from \"@react-oauth/google\";\nimport { FacebookProvider, LoginButton } from \"react-facebook\";\nimport facebook from \"../introduce/facebook.png\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport Forgot_password from \"./forgot_password\";\nimport Change_password from \"./resetpassword\";\nimport { useLoading } from \"./Loading\";\nimport top from \"./img/top.png\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction LoginModal({\n  off,\n  isSignup\n}) {\n  _s();\n  // Sử dụng state để đi<PERSON>u khiển hiển thị modal và form\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [error, setError] = useState(\"\");\n  const [confirm, setConfirm] = useState(false);\n  const [isforgot, setIsforgot] = useState(false);\n  const [isreset, setIsreset] = useState(false);\n  const navigate = useNavigate();\n  const {\n    login\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\",\n    ...(isSignup && {\n      username: \"\",\n      confirmPassword: \"\",\n      code: \"\"\n    }) // Thêm confirmPassword nếu là đăng ký\n  });\n  // const isFormValid = formData.email && formData.password;\n  const handleChange = e => {\n    setError(\"\");\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const submit_log = e => {\n    e.preventDefault();\n    if (formData.password !== formData.confirmPassword && isSignup) {\n      setError(\"Mật khẩu khác với xác nhận mật khẩu\");\n    } else if (confirm) {\n      const body = {\n        email: formData.email,\n        newPassword: formData.password,\n        code: formData.code\n      };\n      console.log(body);\n      startLoading();\n      fetch(`http://localhost:8080/api/auth/reset_password`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      }).then(response => response.json()).then(data => {\n        console.log(data);\n        stopLoading();\n        setConfirm(true);\n        if (true) {\n          // Lưu dữ liệu user vào Cookies\n          // localStorage.setItem(\"token\", data.token);\n          Cookies.set(\"user\", JSON.stringify(data.user), {\n            expires: 7,\n            secure: true,\n            sameSite: \"Strict\"\n          });\n          login(data.user);\n          navigate(\"/home\");\n        }\n      }).catch(error => {\n        console.error(\"Lỗi:\", error);\n      });\n    } else if (isSignup) {\n      const body = {\n        email: formData.email,\n        name: formData.username\n      };\n      console.log(body);\n      startLoading();\n      fetch(`http://localhost:8080/api/auth/signup`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      }).then(response => console.log(response)).then(data => {\n        stopLoading();\n        setConfirm(true);\n      }).catch(error => {\n        console.error(\"Lỗi:\", error);\n      });\n    } else {\n      const body = {\n        email: formData.email,\n        password: formData.password\n      };\n      console.log(formData);\n      startLoading();\n      fetch(`http://localhost:8080/api/auth/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      }).then(response => response.json()).then(data => {\n        stopLoading();\n        console.log(data.user);\n        // console.log(data.token);\n        console.log(data.message);\n        if (true) {\n          // Lưu dữ liệu user vào Cookies\n          // localStorage.setItem(\"token\", data.token);\n          Cookies.set(\"user\", JSON.stringify(data.user), {\n            expires: 7,\n            secure: true,\n            sameSite: \"Strict\"\n          });\n          login(data.user);\n          navigate(\"/home\");\n        }\n        // else {\n        //   setError(\"Email hoặc mật khẩu của bạn không hợp lệ\");\n        // }\n      }).catch(error => {\n        console.error(\"Lỗi:\", error);\n      });\n    }\n  };\n\n  //google\n  const responseMessage = response => {\n    const credential = response.credential;\n    const decoded = jwtDecode(credential);\n    console.log(decoded);\n    const body = {\n      family_name: decoded.family_name,\n      given_name: decoded.given_name,\n      GoogleID: decoded.sub,\n      email: decoded.email\n    };\n    console.log(JSON.stringify(body));\n    startLoading();\n    fetch(\"http://localhost:8080/api/auth/google\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(body)\n    }).then(response => response.json()).then(data => {\n      stopLoading();\n      console.log(data);\n      if (data.message === \"Login successful\" || data.message === \"User created successfully\") {\n        // Lưu dữ liệu user vào Cookies\n        localStorage.setItem(\"token\", data.token);\n        Cookies.set(\"user\", JSON.stringify(data.user), {\n          expires: 7,\n          secure: true,\n          sameSite: \"Strict\"\n        });\n        login(data.user);\n        navigate(\"/home\");\n      } else {\n        setError(\"Email hoặc mật khẩu của bạn không hợp lệ\");\n      }\n    }).catch(error => {\n      console.log(\"Lỗi:\", error);\n    });\n  };\n  const errorMessage = error => {\n    console.log(error);\n  };\n  //facebook\n  const handleResponse = data => {\n    console.log(data);\n  };\n  const handleError = error => {\n    console.error(error);\n  };\n  const forgot = () => {\n    setIsforgot(true);\n  };\n  const sentagain = () => {\n    const body = {\n      email: formData.email,\n      name: formData.username\n    };\n    console.log(body);\n    startLoading();\n    fetch(`http://localhost:8080/api/auth/signup`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(body)\n    }).then(response => console.log(response)).then(data => {\n      stopLoading();\n      setConfirm(true);\n    }).catch(error => {\n      console.error(\"Lỗi:\", error);\n    });\n    // setConfirm(false);\n    // const body = {\n    //   email: formData.email,\n    //   password: formData.password,\n    //   name: formData.username,\n    //   confirm: false,\n    //   code: formData.code,\n    // };\n    // console.log(body);\n    // startLoading();\n    // fetch(\"http://localhost:8080/api/login/sign_up\", {\n    //   method: \"POST\",\n    //   headers: {\n    //     \"Content-Type\": \"application/json\",\n    //   },\n    //   body: JSON.stringify(body),\n    // })\n    //   .then((response) => response.json())\n    //   .then((data) => {\n    //     stopLoading();\n    //     if (data.message === \"User created successfully\") {\n    //       // Lưu dữ liệu user vào Cookies\n    //       setIsforgot(true);\n    //     } else {\n    //       setError(data.message);\n    //     }\n    //   })\n    //   .catch((error) => {\n    //     console.error(\"Lỗi:\", error);\n    //   });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isreset && /*#__PURE__*/_jsxDEV(Change_password, {\n      off: () => {\n        setIsreset(false);\n      },\n      email: isreset\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }, this), isforgot && /*#__PURE__*/_jsxDEV(Forgot_password, {\n      off: () => {\n        setIsforgot(false);\n      },\n      turnon: email => {\n        setIsreset(email);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n      clientId: \"1039484967279-b0uv9c8m0t6v453c7im8f0jiopq82v3j.apps.googleusercontent.com\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-modal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"image-top\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: top,\n              alt: \"Background\",\n              class: \"top-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"login-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: isSignup ? \"Sign up\" : \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"close-btn\",\n              onClick: () => {\n                off(0);\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"By continuing, you agree to our \", /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"User Agreement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 47\n            }, this), \" and acknowledge that you understand the \", /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 51\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"login-option\",\n            children: \"Continue with phone number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"forgoogle\",\n            children: /*#__PURE__*/_jsxDEV(GoogleLogin, {\n              onSuccess: responseMessage,\n              onError: errorMessage,\n              theme: \"filled_blue\",\n              size: \"large\",\n              shape: \"circle\",\n              color: \"blue\",\n              className: \"custom-google-login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FacebookProvider, {\n            appId: \"1509733739672960\",\n            children: /*#__PURE__*/_jsxDEV(LoginButton, {\n              scope: \"email\",\n              onCompleted: handleResponse,\n              onError: handleError,\n              className: \"facebook-login-button\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"facebook-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: facebook,\n                  style: {\n                    height: \"40px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110\\u0103ng nh\\u1EADp b\\u1EB1ng Facebook\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"divider\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"OR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"login-form\",\n            onSubmit: submit_log,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                name: \"email\",\n                type: \"text\",\n                placeholder: \"Email\",\n                value: formData.email,\n                onChange: handleChange // Cập nhật giá trị email\n                ,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                name: \"password\",\n                type: \"password\",\n                placeholder: \"Password\",\n                value: formData.password,\n                onChange: handleChange // Cập nhật giá trị password\n                ,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), isSignup && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"confirmPassword\",\n                placeholder: \"Confirm Password\",\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), isSignup && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                placeholder: \"Name\",\n                value: formData.username,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), confirm && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"code\",\n                  placeholder: \"\\u0110i\\u1EC1n m\\xE3 x\\xE1c nh\\u1EADn \",\n                  value: formData.code,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"sentagain\",\n                onClick: sentagain,\n                children: \"G\\u1EEDi l\\u1EA1i m\\xE3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), !isSignup && /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"forgot-password\",\n              onClick: forgot,\n              style: {\n                cursor: \"pointer\"\n              },\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              id: \"login-btn\",\n              type: \"submit\",\n              children: isSignup ? \"Sign up\" : \"login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: \"red\"\n              },\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), !isSignup && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"signup-text\",\n            children: [\"New to Myapp?\", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                cursor: \"pointer\",\n                color: \"cornflowerblue\"\n              },\n              onClick: () => {\n                off(2);\n              },\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), \" \"]\n  }, void 0, true);\n}\n_s(LoginModal, \"IU4IGIzfzUpX53yU4RNgLFLfYII=\", false, function () {\n  return [useLoading, useNavigate, useAuth];\n});\n_c = LoginModal;\nexport default LoginModal;\nvar _c;\n$RefreshReg$(_c, \"LoginModal\");", "map": {"version": 3, "names": ["React", "useState", "Cookies", "GoogleOAuthProvider", "GoogleLogin", "FacebookProvider", "LoginButton", "facebook", "jwtDecode", "useNavigate", "useAuth", "Forgot_password", "Change_password", "useLoading", "top", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LoginModal", "off", "isSignup", "_s", "startLoading", "stopLoading", "error", "setError", "confirm", "setConfirm", "isforgot", "setIsforgot", "isreset", "setIsreset", "navigate", "login", "formData", "setFormData", "email", "password", "username", "confirmPassword", "code", "handleChange", "e", "target", "name", "value", "submit_log", "preventDefault", "body", "newPassword", "console", "log", "fetch", "method", "headers", "JSON", "stringify", "then", "response", "json", "data", "set", "user", "expires", "secure", "sameSite", "catch", "message", "responseMessage", "credential", "decoded", "family_name", "given_name", "GoogleID", "sub", "localStorage", "setItem", "token", "errorMessage", "handleResponse", "handleError", "forgot", "sentagain", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "turnon", "clientId", "className", "class", "src", "alt", "onClick", "href", "onSuccess", "onError", "theme", "size", "shape", "color", "appId", "scope", "onCompleted", "style", "height", "onSubmit", "type", "placeholder", "onChange", "required", "cursor", "id", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/introduce/intro.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./intro.css\";\r\nimport Cookies from \"js-cookie\";\r\nimport { GoogleOAuthProvider, GoogleLogin } from \"@react-oauth/google\";\r\nimport { FacebookProvider, LoginButton } from \"react-facebook\";\r\nimport facebook from \"../introduce/facebook.png\";\r\nimport { jwtDecode } from \"jwt-decode\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport Forgot_password from \"./forgot_password\";\r\nimport Change_password from \"./resetpassword\";\r\nimport { useLoading } from \"./Loading\";\r\nimport top from \"./img/top.png\";\r\nfunction LoginModal({ off, isSignup }) {\r\n  // Sử dụng state để điều khiển hiển thị modal và form\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [error, setError] = useState(\"\");\r\n  const [confirm, setConfirm] = useState(false);\r\n  const [isforgot, setIsforgot] = useState(false);\r\n  const [isreset, setIsreset] = useState(false);\r\n  const navigate = useNavigate();\r\n  const { login } = useAuth();\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    password: \"\",\r\n    ...(isSignup && { username: \"\", confirmPassword: \"\", code: \"\" }), // Thêm confirmPassword nếu là đăng ký\r\n  });\r\n  // const isFormValid = formData.email && formData.password;\r\n  const handleChange = (e) => {\r\n    setError(\"\");\r\n    setFormData({ ...formData, [e.target.name]: e.target.value });\r\n  };\r\n  const submit_log = (e) => {\r\n    e.preventDefault();\r\n    if (formData.password !== formData.confirmPassword && isSignup) {\r\n      setError(\"Mật khẩu khác với xác nhận mật khẩu\");\r\n    } else if (confirm) {\r\n      const body = {\r\n        email: formData.email,\r\n        newPassword: formData.password,\r\n        code: formData.code,\r\n      };\r\n      console.log(body);\r\n      startLoading();\r\n      fetch(`http://localhost:8080/api/auth/reset_password`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      })\r\n        .then((response) => response.json())\r\n        .then((data) => {\r\n          console.log(data);\r\n          stopLoading();\r\n          setConfirm(true);\r\n          if (true) {\r\n            // Lưu dữ liệu user vào Cookies\r\n            // localStorage.setItem(\"token\", data.token);\r\n            Cookies.set(\"user\", JSON.stringify(data.user), {\r\n              expires: 7,\r\n              secure: true,\r\n              sameSite: \"Strict\",\r\n            });\r\n            login(data.user);\r\n            navigate(\"/home\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Lỗi:\", error);\r\n        });\r\n    } else if (isSignup) {\r\n      const body = {\r\n        email: formData.email,\r\n        name: formData.username,\r\n      };\r\n      console.log(body);\r\n      startLoading();\r\n      fetch(`http://localhost:8080/api/auth/signup`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      })\r\n        .then((response) => console.log(response))\r\n        .then((data) => {\r\n          stopLoading();\r\n          setConfirm(true);\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Lỗi:\", error);\r\n        });\r\n    } else {\r\n      const body = {\r\n        email: formData.email,\r\n        password: formData.password,\r\n      };\r\n      console.log(formData);\r\n      startLoading();\r\n      fetch(`http://localhost:8080/api/auth/login`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      })\r\n        .then((response) => response.json())\r\n        .then((data) => {\r\n          stopLoading();\r\n          console.log(data.user);\r\n          // console.log(data.token);\r\n          console.log(data.message);\r\n          if (true) {\r\n            // Lưu dữ liệu user vào Cookies\r\n            // localStorage.setItem(\"token\", data.token);\r\n            Cookies.set(\"user\", JSON.stringify(data.user), {\r\n              expires: 7,\r\n              secure: true,\r\n              sameSite: \"Strict\",\r\n            });\r\n            login(data.user);\r\n            navigate(\"/home\");\r\n          }\r\n          // else {\r\n          //   setError(\"Email hoặc mật khẩu của bạn không hợp lệ\");\r\n          // }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Lỗi:\", error);\r\n        });\r\n    }\r\n  };\r\n\r\n  //google\r\n  const responseMessage = (response) => {\r\n    const credential = response.credential;\r\n    const decoded = jwtDecode(credential);\r\n    console.log(decoded);\r\n    const body = {\r\n      family_name: decoded.family_name,\r\n      given_name: decoded.given_name,\r\n      GoogleID: decoded.sub,\r\n      email: decoded.email,\r\n    };\r\n    console.log(JSON.stringify(body));\r\n    startLoading();\r\n    fetch(\"http://localhost:8080/api/auth/google\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(body),\r\n    })\r\n      .then((response) => response.json())\r\n      .then((data) => {\r\n        stopLoading();\r\n        console.log(data);\r\n        if (\r\n          data.message === \"Login successful\" ||\r\n          data.message === \"User created successfully\"\r\n        ) {\r\n          // Lưu dữ liệu user vào Cookies\r\n          localStorage.setItem(\"token\", data.token);\r\n          Cookies.set(\"user\", JSON.stringify(data.user), {\r\n            expires: 7,\r\n            secure: true,\r\n            sameSite: \"Strict\",\r\n          });\r\n          login(data.user);\r\n          navigate(\"/home\");\r\n        } else {\r\n          setError(\"Email hoặc mật khẩu của bạn không hợp lệ\");\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.log(\"Lỗi:\", error);\r\n      });\r\n  };\r\n\r\n  const errorMessage = (error) => {\r\n    console.log(error);\r\n  };\r\n  //facebook\r\n  const handleResponse = (data) => {\r\n    console.log(data);\r\n  };\r\n\r\n  const handleError = (error) => {\r\n    console.error(error);\r\n  };\r\n  const forgot = () => {\r\n    setIsforgot(true);\r\n  };\r\n  const sentagain = () => {\r\n    const body = {\r\n      email: formData.email,\r\n      name: formData.username,\r\n    };\r\n    console.log(body);\r\n    startLoading();\r\n    fetch(`http://localhost:8080/api/auth/signup`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(body),\r\n    })\r\n      .then((response) => console.log(response))\r\n      .then((data) => {\r\n        stopLoading();\r\n        setConfirm(true);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Lỗi:\", error);\r\n      });\r\n    // setConfirm(false);\r\n    // const body = {\r\n    //   email: formData.email,\r\n    //   password: formData.password,\r\n    //   name: formData.username,\r\n    //   confirm: false,\r\n    //   code: formData.code,\r\n    // };\r\n    // console.log(body);\r\n    // startLoading();\r\n    // fetch(\"http://localhost:8080/api/login/sign_up\", {\r\n    //   method: \"POST\",\r\n    //   headers: {\r\n    //     \"Content-Type\": \"application/json\",\r\n    //   },\r\n    //   body: JSON.stringify(body),\r\n    // })\r\n    //   .then((response) => response.json())\r\n    //   .then((data) => {\r\n    //     stopLoading();\r\n    //     if (data.message === \"User created successfully\") {\r\n    //       // Lưu dữ liệu user vào Cookies\r\n    //       setIsforgot(true);\r\n    //     } else {\r\n    //       setError(data.message);\r\n    //     }\r\n    //   })\r\n    //   .catch((error) => {\r\n    //     console.error(\"Lỗi:\", error);\r\n    //   });\r\n  };\r\n  return (\r\n    <>\r\n      {isreset && (\r\n        <Change_password\r\n          off={() => {\r\n            setIsreset(false);\r\n          }}\r\n          email={isreset}\r\n        />\r\n      )}\r\n      {isforgot && (\r\n        <Forgot_password\r\n          off={() => {\r\n            setIsforgot(false);\r\n          }}\r\n          turnon={(email) => {\r\n            setIsreset(email);\r\n          }}\r\n        />\r\n      )}\r\n      <GoogleOAuthProvider clientId=\"1039484967279-b0uv9c8m0t6v453c7im8f0jiopq82v3j.apps.googleusercontent.com\">\r\n        <div className=\"login\">\r\n          <div className=\"login-modal\">\r\n            <div class=\"image-top\">\r\n              <img src={top} alt=\"Background\" class=\"top-image\" />\r\n            </div>\r\n            <div className=\"login-header\">\r\n              <h2>{isSignup ? \"Sign up\" : \"Login\"}</h2>\r\n              <span\r\n                className=\"close-btn\"\r\n                onClick={() => {\r\n                  off(0);\r\n                }}\r\n              >\r\n                &times;\r\n              </span>\r\n            </div>\r\n\r\n            <p>\r\n              By continuing, you agree to our <a href=\"#\">User Agreement</a> and\r\n              acknowledge that you understand the <a href=\"#\">Privacy Policy</a>\r\n              .\r\n            </p>\r\n\r\n            <button className=\"login-option\">Continue with phone number</button>\r\n\r\n            <div className=\"forgoogle\">\r\n              <GoogleLogin\r\n                onSuccess={responseMessage}\r\n                onError={errorMessage}\r\n                theme=\"filled_blue\"\r\n                size=\"large\"\r\n                shape=\"circle\"\r\n                color=\"blue\"\r\n                className=\"custom-google-login\"\r\n              />\r\n            </div>\r\n            <FacebookProvider appId=\"1509733739672960\">\r\n              <LoginButton\r\n                scope=\"email\"\r\n                onCompleted={handleResponse}\r\n                onError={handleError}\r\n                className=\"facebook-login-button\"\r\n              >\r\n                <span className=\"facebook-icon\">\r\n                  <img src={facebook} style={{ height: \"40px\" }}></img>\r\n                </span>\r\n                <span>Đăng nhập bằng Facebook</span>\r\n              </LoginButton>\r\n            </FacebookProvider>\r\n\r\n            <div className=\"divider\">\r\n              <span>OR</span>\r\n            </div>\r\n\r\n            <form className=\"login-form\" onSubmit={submit_log}>\r\n              <div className=\"form-group\">\r\n                <input\r\n                  name=\"email\"\r\n                  type=\"text\"\r\n                  placeholder=\"Email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange} // Cập nhật giá trị email\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <input\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  placeholder=\"Password\"\r\n                  value={formData.password}\r\n                  onChange={handleChange} // Cập nhật giá trị password\r\n                  required\r\n                />\r\n              </div>\r\n              {isSignup && (\r\n                <div className=\"form-group\">\r\n                  <input\r\n                    type=\"password\"\r\n                    name=\"confirmPassword\"\r\n                    placeholder=\"Confirm Password\"\r\n                    value={formData.confirmPassword}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n              )}\r\n              {isSignup && (\r\n                <div className=\"form-group\">\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"username\"\r\n                    placeholder=\"Name\"\r\n                    value={formData.username}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n              )}\r\n              {confirm && (\r\n                <>\r\n                  <div className=\"form-group\">\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"code\"\r\n                      placeholder=\"Điền mã xác nhận \"\r\n                      value={formData.code}\r\n                      onChange={handleChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <p className=\"sentagain\" onClick={sentagain}>\r\n                    Gửi lại mã\r\n                  </p>\r\n                </>\r\n              )}\r\n              {!isSignup && (\r\n                <a\r\n                  className=\"forgot-password\"\r\n                  onClick={forgot}\r\n                  style={{ cursor: \"pointer\" }}\r\n                >\r\n                  Forgot password?\r\n                </a>\r\n              )}\r\n              <button id=\"login-btn\" type=\"submit\">\r\n                {isSignup ? \"Sign up\" : \"login\"}\r\n              </button>\r\n              <p style={{ color: \"red\" }}>{error}</p>\r\n            </form>\r\n            {!isSignup && (\r\n              <p className=\"signup-text\">\r\n                New to Myapp?{\" \"}\r\n                <a\r\n                  style={{ cursor: \"pointer\", color: \"cornflowerblue\" }}\r\n                  onClick={() => {\r\n                    off(2);\r\n                  }}\r\n                >\r\n                  Sign Up\r\n                </a>\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </GoogleOAuthProvider>{\" \"}\r\n    </>\r\n  );\r\n}\r\nexport default LoginModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,qBAAqB;AACtE,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,SAASC,UAAU,QAAQ,WAAW;AACtC,OAAOC,GAAG,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChC,SAASC,UAAUA,CAAC;EAAEC,GAAG;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACrC;EACA,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGX,UAAU,CAAC,CAAC;EAClD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC;IACvCoC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZ,IAAIjB,QAAQ,IAAI;MAAEkB,QAAQ,EAAE,EAAE;MAAEC,eAAe,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAE;EACpE,CAAC,CAAC;EACF;EACA,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BjB,QAAQ,CAAC,EAAE,CAAC;IACZU,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACQ,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC/D,CAAC;EACD,MAAMC,UAAU,GAAIJ,CAAC,IAAK;IACxBA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAIb,QAAQ,CAACG,QAAQ,KAAKH,QAAQ,CAACK,eAAe,IAAInB,QAAQ,EAAE;MAC9DK,QAAQ,CAAC,qCAAqC,CAAC;IACjD,CAAC,MAAM,IAAIC,OAAO,EAAE;MAClB,MAAMsB,IAAI,GAAG;QACXZ,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBa,WAAW,EAAEf,QAAQ,CAACG,QAAQ;QAC9BG,IAAI,EAAEN,QAAQ,CAACM;MACjB,CAAC;MACDU,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;MACjB1B,YAAY,CAAC,CAAC;MACd8B,KAAK,CAAC,+CAA+C,EAAE;QACrDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI;MAC3B,CAAC,CAAC,CACCS,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAK;QACdV,OAAO,CAACC,GAAG,CAACS,IAAI,CAAC;QACjBrC,WAAW,CAAC,CAAC;QACbI,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI,IAAI,EAAE;UACR;UACA;UACA1B,OAAO,CAAC4D,GAAG,CAAC,MAAM,EAAEN,IAAI,CAACC,SAAS,CAACI,IAAI,CAACE,IAAI,CAAC,EAAE;YAC7CC,OAAO,EAAE,CAAC;YACVC,MAAM,EAAE,IAAI;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACFhC,KAAK,CAAC2B,IAAI,CAACE,IAAI,CAAC;UAChB9B,QAAQ,CAAC,OAAO,CAAC;QACnB;MACF,CAAC,CAAC,CACDkC,KAAK,CAAE1C,KAAK,IAAK;QAChB0B,OAAO,CAAC1B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,MAAM,IAAIJ,QAAQ,EAAE;MACnB,MAAM4B,IAAI,GAAG;QACXZ,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBQ,IAAI,EAAEV,QAAQ,CAACI;MACjB,CAAC;MACDY,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;MACjB1B,YAAY,CAAC,CAAC;MACd8B,KAAK,CAAC,uCAAuC,EAAE;QAC7CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI;MAC3B,CAAC,CAAC,CACCS,IAAI,CAAEC,QAAQ,IAAKR,OAAO,CAACC,GAAG,CAACO,QAAQ,CAAC,CAAC,CACzCD,IAAI,CAAEG,IAAI,IAAK;QACdrC,WAAW,CAAC,CAAC;QACbI,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC,CACDuC,KAAK,CAAE1C,KAAK,IAAK;QAChB0B,OAAO,CAAC1B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,MAAM;MACL,MAAMwB,IAAI,GAAG;QACXZ,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG;MACrB,CAAC;MACDa,OAAO,CAACC,GAAG,CAACjB,QAAQ,CAAC;MACrBZ,YAAY,CAAC,CAAC;MACd8B,KAAK,CAAC,sCAAsC,EAAE;QAC5CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI;MAC3B,CAAC,CAAC,CACCS,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAK;QACdrC,WAAW,CAAC,CAAC;QACb2B,OAAO,CAACC,GAAG,CAACS,IAAI,CAACE,IAAI,CAAC;QACtB;QACAZ,OAAO,CAACC,GAAG,CAACS,IAAI,CAACO,OAAO,CAAC;QACzB,IAAI,IAAI,EAAE;UACR;UACA;UACAlE,OAAO,CAAC4D,GAAG,CAAC,MAAM,EAAEN,IAAI,CAACC,SAAS,CAACI,IAAI,CAACE,IAAI,CAAC,EAAE;YAC7CC,OAAO,EAAE,CAAC;YACVC,MAAM,EAAE,IAAI;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACFhC,KAAK,CAAC2B,IAAI,CAACE,IAAI,CAAC;UAChB9B,QAAQ,CAAC,OAAO,CAAC;QACnB;QACA;QACA;QACA;MACF,CAAC,CAAC,CACDkC,KAAK,CAAE1C,KAAK,IAAK;QAChB0B,OAAO,CAAC1B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC;MAC9B,CAAC,CAAC;IACN;EACF,CAAC;;EAED;EACA,MAAM4C,eAAe,GAAIV,QAAQ,IAAK;IACpC,MAAMW,UAAU,GAAGX,QAAQ,CAACW,UAAU;IACtC,MAAMC,OAAO,GAAG/D,SAAS,CAAC8D,UAAU,CAAC;IACrCnB,OAAO,CAACC,GAAG,CAACmB,OAAO,CAAC;IACpB,MAAMtB,IAAI,GAAG;MACXuB,WAAW,EAAED,OAAO,CAACC,WAAW;MAChCC,UAAU,EAAEF,OAAO,CAACE,UAAU;MAC9BC,QAAQ,EAAEH,OAAO,CAACI,GAAG;MACrBtC,KAAK,EAAEkC,OAAO,CAAClC;IACjB,CAAC;IACDc,OAAO,CAACC,GAAG,CAACI,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC,CAAC;IACjC1B,YAAY,CAAC,CAAC;IACd8B,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC,CACCS,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAK;MACdrC,WAAW,CAAC,CAAC;MACb2B,OAAO,CAACC,GAAG,CAACS,IAAI,CAAC;MACjB,IACEA,IAAI,CAACO,OAAO,KAAK,kBAAkB,IACnCP,IAAI,CAACO,OAAO,KAAK,2BAA2B,EAC5C;QACA;QACAQ,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEhB,IAAI,CAACiB,KAAK,CAAC;QACzC5E,OAAO,CAAC4D,GAAG,CAAC,MAAM,EAAEN,IAAI,CAACC,SAAS,CAACI,IAAI,CAACE,IAAI,CAAC,EAAE;UAC7CC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE,IAAI;UACZC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFhC,KAAK,CAAC2B,IAAI,CAACE,IAAI,CAAC;QAChB9B,QAAQ,CAAC,OAAO,CAAC;MACnB,CAAC,MAAM;QACLP,QAAQ,CAAC,0CAA0C,CAAC;MACtD;IACF,CAAC,CAAC,CACDyC,KAAK,CAAE1C,KAAK,IAAK;MAChB0B,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE3B,KAAK,CAAC;IAC5B,CAAC,CAAC;EACN,CAAC;EAED,MAAMsD,YAAY,GAAItD,KAAK,IAAK;IAC9B0B,OAAO,CAACC,GAAG,CAAC3B,KAAK,CAAC;EACpB,CAAC;EACD;EACA,MAAMuD,cAAc,GAAInB,IAAI,IAAK;IAC/BV,OAAO,CAACC,GAAG,CAACS,IAAI,CAAC;EACnB,CAAC;EAED,MAAMoB,WAAW,GAAIxD,KAAK,IAAK;IAC7B0B,OAAO,CAAC1B,KAAK,CAACA,KAAK,CAAC;EACtB,CAAC;EACD,MAAMyD,MAAM,GAAGA,CAAA,KAAM;IACnBpD,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,MAAMqD,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMlC,IAAI,GAAG;MACXZ,KAAK,EAAEF,QAAQ,CAACE,KAAK;MACrBQ,IAAI,EAAEV,QAAQ,CAACI;IACjB,CAAC;IACDY,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;IACjB1B,YAAY,CAAC,CAAC;IACd8B,KAAK,CAAC,uCAAuC,EAAE;MAC7CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC,CACCS,IAAI,CAAEC,QAAQ,IAAKR,OAAO,CAACC,GAAG,CAACO,QAAQ,CAAC,CAAC,CACzCD,IAAI,CAAEG,IAAI,IAAK;MACdrC,WAAW,CAAC,CAAC;MACbI,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,CACDuC,KAAK,CAAE1C,KAAK,IAAK;MAChB0B,OAAO,CAAC1B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC;IAC9B,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC;EACD,oBACET,OAAA,CAAAE,SAAA;IAAAkE,QAAA,GACGrD,OAAO,iBACNf,OAAA,CAACJ,eAAe;MACdQ,GAAG,EAAEA,CAAA,KAAM;QACTY,UAAU,CAAC,KAAK,CAAC;MACnB,CAAE;MACFK,KAAK,EAAEN;IAAQ;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACF,EACA3D,QAAQ,iBACPb,OAAA,CAACL,eAAe;MACdS,GAAG,EAAEA,CAAA,KAAM;QACTU,WAAW,CAAC,KAAK,CAAC;MACpB,CAAE;MACF2D,MAAM,EAAGpD,KAAK,IAAK;QACjBL,UAAU,CAACK,KAAK,CAAC;MACnB;IAAE;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eACDxE,OAAA,CAACb,mBAAmB;MAACuF,QAAQ,EAAC,2EAA2E;MAAAN,QAAA,eACvGpE,OAAA;QAAK2E,SAAS,EAAC,OAAO;QAAAP,QAAA,eACpBpE,OAAA;UAAK2E,SAAS,EAAC,aAAa;UAAAP,QAAA,gBAC1BpE,OAAA;YAAK4E,KAAK,EAAC,WAAW;YAAAR,QAAA,eACpBpE,OAAA;cAAK6E,GAAG,EAAE/E,GAAI;cAACgF,GAAG,EAAC,YAAY;cAACF,KAAK,EAAC;YAAW;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNxE,OAAA;YAAK2E,SAAS,EAAC,cAAc;YAAAP,QAAA,gBAC3BpE,OAAA;cAAAoE,QAAA,EAAK/D,QAAQ,GAAG,SAAS,GAAG;YAAO;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzCxE,OAAA;cACE2E,SAAS,EAAC,WAAW;cACrBI,OAAO,EAAEA,CAAA,KAAM;gBACb3E,GAAG,CAAC,CAAC,CAAC;cACR,CAAE;cAAAgE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENxE,OAAA;YAAAoE,QAAA,GAAG,kCAC+B,eAAApE,OAAA;cAAGgF,IAAI,EAAC,GAAG;cAAAZ,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,6CAC1B,eAAAxE,OAAA;cAAGgF,IAAI,EAAC,GAAG;cAAAZ,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,KAEpE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJxE,OAAA;YAAQ2E,SAAS,EAAC,cAAc;YAAAP,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAEpExE,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAP,QAAA,eACxBpE,OAAA,CAACZ,WAAW;cACV6F,SAAS,EAAE5B,eAAgB;cAC3B6B,OAAO,EAAEnB,YAAa;cACtBoB,KAAK,EAAC,aAAa;cACnBC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,QAAQ;cACdC,KAAK,EAAC,MAAM;cACZX,SAAS,EAAC;YAAqB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxE,OAAA,CAACX,gBAAgB;YAACkG,KAAK,EAAC,kBAAkB;YAAAnB,QAAA,eACxCpE,OAAA,CAACV,WAAW;cACVkG,KAAK,EAAC,OAAO;cACbC,WAAW,EAAEzB,cAAe;cAC5BkB,OAAO,EAAEjB,WAAY;cACrBU,SAAS,EAAC,uBAAuB;cAAAP,QAAA,gBAEjCpE,OAAA;gBAAM2E,SAAS,EAAC,eAAe;gBAAAP,QAAA,eAC7BpE,OAAA;kBAAK6E,GAAG,EAAEtF,QAAS;kBAACmG,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAAO;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACPxE,OAAA;gBAAAoE,QAAA,EAAM;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEnBxE,OAAA;YAAK2E,SAAS,EAAC,SAAS;YAAAP,QAAA,eACtBpE,OAAA;cAAAoE,QAAA,EAAM;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAENxE,OAAA;YAAM2E,SAAS,EAAC,YAAY;YAACiB,QAAQ,EAAE7D,UAAW;YAAAqC,QAAA,gBAChDpE,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAP,QAAA,eACzBpE,OAAA;gBACE6B,IAAI,EAAC,OAAO;gBACZgE,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,OAAO;gBACnBhE,KAAK,EAAEX,QAAQ,CAACE,KAAM;gBACtB0E,QAAQ,EAAErE,YAAa,CAAC;gBAAA;gBACxBsE,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxE,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAP,QAAA,eACzBpE,OAAA;gBACE6B,IAAI,EAAC,UAAU;gBACfgE,IAAI,EAAC,UAAU;gBACfC,WAAW,EAAC,UAAU;gBACtBhE,KAAK,EAAEX,QAAQ,CAACG,QAAS;gBACzByE,QAAQ,EAAErE,YAAa,CAAC;gBAAA;gBACxBsE,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLnE,QAAQ,iBACPL,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAP,QAAA,eACzBpE,OAAA;gBACE6F,IAAI,EAAC,UAAU;gBACfhE,IAAI,EAAC,iBAAiB;gBACtBiE,WAAW,EAAC,kBAAkB;gBAC9BhE,KAAK,EAAEX,QAAQ,CAACK,eAAgB;gBAChCuE,QAAQ,EAAErE,YAAa;gBACvBsE,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EACAnE,QAAQ,iBACPL,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAP,QAAA,eACzBpE,OAAA;gBACE6F,IAAI,EAAC,MAAM;gBACXhE,IAAI,EAAC,UAAU;gBACfiE,WAAW,EAAC,MAAM;gBAClBhE,KAAK,EAAEX,QAAQ,CAACI,QAAS;gBACzBwE,QAAQ,EAAErE,YAAa;gBACvBsE,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EACA7D,OAAO,iBACNX,OAAA,CAAAE,SAAA;cAAAkE,QAAA,gBACEpE,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAP,QAAA,eACzBpE,OAAA;kBACE6F,IAAI,EAAC,MAAM;kBACXhE,IAAI,EAAC,MAAM;kBACXiE,WAAW,EAAC,wCAAmB;kBAC/BhE,KAAK,EAAEX,QAAQ,CAACM,IAAK;kBACrBsE,QAAQ,EAAErE,YAAa;kBACvBsE,QAAQ;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxE,OAAA;gBAAG2E,SAAS,EAAC,WAAW;gBAACI,OAAO,EAAEZ,SAAU;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eACJ,CACH,EACA,CAACnE,QAAQ,iBACRL,OAAA;cACE2E,SAAS,EAAC,iBAAiB;cAC3BI,OAAO,EAAEb,MAAO;cAChBwB,KAAK,EAAE;gBAAEO,MAAM,EAAE;cAAU,CAAE;cAAA7B,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,eACDxE,OAAA;cAAQkG,EAAE,EAAC,WAAW;cAACL,IAAI,EAAC,QAAQ;cAAAzB,QAAA,EACjC/D,QAAQ,GAAG,SAAS,GAAG;YAAO;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACTxE,OAAA;cAAG0F,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAM,CAAE;cAAAlB,QAAA,EAAE3D;YAAK;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACN,CAACnE,QAAQ,iBACRL,OAAA;YAAG2E,SAAS,EAAC,aAAa;YAAAP,QAAA,GAAC,eACZ,EAAC,GAAG,eACjBpE,OAAA;cACE0F,KAAK,EAAE;gBAAEO,MAAM,EAAE,SAAS;gBAAEX,KAAK,EAAE;cAAiB,CAAE;cACtDP,OAAO,EAAEA,CAAA,KAAM;gBACb3E,GAAG,CAAC,CAAC,CAAC;cACR,CAAE;cAAAgE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC,EAAC,GAAG;EAAA,eAC1B,CAAC;AAEP;AAAClE,EAAA,CAnZQH,UAAU;EAAA,QAEqBN,UAAU,EAK/BJ,WAAW,EACVC,OAAO;AAAA;AAAAyG,EAAA,GARlBhG,UAAU;AAoZnB,eAAeA,UAAU;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}