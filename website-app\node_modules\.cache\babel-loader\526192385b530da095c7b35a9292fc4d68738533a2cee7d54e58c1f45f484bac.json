{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\Import\\\\index.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// // import ImageUpload from \"../../components/Manage_product/image\"\n// // import Change_password from\"../../components/introduce/resetpassword.js\"\n// import OrderManagement from \"../../components/test/index\";\n// import ModalHistory from \"./ModalHistory\";\n// import React, {\n//   useState,\n//   useRef,\n//   useEffect,\n//   useCallback,\n//   useContext,\n// } from \"react\";\n// import axios from \"axios\";\n// import debounce from \"lodash.debounce\";\n// import Modal from \"./../../components/ComponentExport/Modal\";\n// import \"./import.css\";\n// import ModalDetail from \"./ModalDetail\";\n// import { useAuth} from \"../../components/introduce/useAuth\";\n// import { notify } from \"../../components/Notification/notification\";\n// import { useLoading } from \"../../components/introduce/Loading\";\n// function Import() {\n//   const [isOpen, setIsOpen] = useState(false);\n//   const [searchTerm, setSearchTerm] = useState(\"\");\n//   const [results, setResults] = useState([]);\n//   const [showDropdown, setShowDropdown] = useState(false);\n//   const [openHistory, setOpenHistory] = useState(false);\n//   const [openDetail, setOpenDetail] = useState(false);\n//   const [suggestions, setSuggestions] = useState([]);\n//   const [suppOrPro, setSuppOrPro] = useState(false);\n//   const [idProductAdded, setIdProductAdded] = useState([]);\n//   const [idOrder, setIdOrder] = useState(null);\n//   const [dataTop, setDataTop] = useState([]);\n//   const { user, loading } = useAuth();\n//   const apiGetOrder = useRef()\n//   const apiGetHistory = useRef()\n//   const [view,setView] = useState(true);\n//   const [loadLog,setLoadLog] = useState(false)\n//   const [loadOrder,setLoadOrder] = useState(false)\n//   // const id_owner = user.id_owner;\n//   const openModal = () => setIsOpen(true);\n//   const closeModal = () => setIsOpen(false);\n//   const openModalHistory = () => setOpenHistory(true);\n//   const closeModalHistory = () => setOpenHistory(false);\n//   const closeModalDetail = () => setOpenDetail(false);\n//   const openModalDetail = () => setOpenDetail(true);\n//   const {startLoading,stopLoading}=useLoading();\n//   useEffect(() => {\n//     const fetchData = async () => {\n//       try {\n//         if(loading)return;\n//         const res = await fetch(\n//           `http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`\n//         );\n//         const dataRes = await res.json();\n//         setDataTop(dataRes);\n\n//       } catch (err) {\n//         console.error(err);\n//       }\n//     };\n//     fetchData();\n//   }, [loading]);\n//   const handleSearch = (event) => {\n//     const term = event.target.value;\n//     let keyword = term.trim();\n//     setSearchTerm(term);\n//     if (keyword.startsWith(\"@All\")) {\n//       keyword = keyword.substr(4).trim();\n//       setSuppOrPro(false);\n//       if (keyword.length > 0) {\n//         debouncedFetchSuggestions(\n//           keyword,\n//           `http://localhost:8080/api/import/supplier/search`\n//         );\n//       } else {\n//         setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\n//       }\n//     } else {\n//       setSuppOrPro(true);\n//       if (keyword.length > 0) {\n//         const topData = dataTop\n//           .filter((item) =>\n//             item.name.toLowerCase().includes(keyword.toLowerCase())\n//           )\n//           .slice(0, 5);\n//         if (topData.length) {\n//           setResults(topData.map((item) => item.name));\n//           setSuggestions(topData);\n//         } else {\n//           console.log(\"jellooo\");\n//           debouncedFetchSuggestions(\n//             keyword,\n//             `http://localhost:8080/api/import/products/exhibitProN`\n//           );\n//         }\n//       } else {\n//         setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\n//       }\n//     }\n//   };\n//   // database\n//   const fetchProductSuggestions = async (keyword, hrefLink) => {\n//     try {\n//       const response = await axios.get(hrefLink, {\n//         params: {\n//           query: keyword,\n//           ownerId: user.id_owner,\n//         },\n//       });\n//       const sugg = response.data.map((s) => s.name);\n//       setResults(sugg);\n//       setDataTop((prev) => {\n//         const newData = [...prev, ...response.data];\n//         return newData;\n//       });\n//       setSuggestions(response.data);\n//     } catch (error) {\n//       console.error(\"Error fetching suggestions:\", error);\n//     }\n//   };\n//   const debouncedFetchSuggestions = useCallback(\n//     debounce(\n//       (keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink),\n//       500\n//     ),\n//     [user] // Chỉ tạo ra một lần\n//   );\n\n//   const handleAddToOrder = async () => {\n//     const idPro = suggestions.filter((sugg) => sugg.name == searchTerm);\n//     setSuggestions([]);\n//     const suppliersId = idPro ? idPro[0] : null;\n//     try {\n//       // Gửi request GET với query string chứa productId\n//       if (suppliersId) {\n//         let response;\n//         if (!suppOrPro) {\n//           response = await fetch(\n//             `http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`,\n//             {\n//               method: \"GET\",\n//               headers: {\n//                 \"Content-Type\": \"application/json\",\n//               },\n//             }\n//           );\n//         }\n\n//         // Kiểm tra nếu request thành công\n//         if (!suppOrPro && response.ok) {\n//           const data = await response.json(); // Dữ liệu trả về từ server (có thể là chi tiết sản phẩm)\n//           setIdProductAdded(data);\n//           // Xử lý dữ liệu từ server (Hiển thị thông tin đơn hàng, ví dụ...)\n//           setSearchTerm(\"\");\n//           setResults([]);\n//         } else if (suppOrPro) {\n//           setIdProductAdded(idPro);\n//           setSearchTerm(\"\");\n//           setResults([]);\n//         } else {\n//           console.error(\"Error adding to order\");\n//         }\n//       }\n//     } catch (error) {\n//       console.error(\"Request failed\", error);\n//     }\n//   };\n//   const handleBlur = () => {\n//     setTimeout(() => {\n//       setShowDropdown(false);\n//     }, 700);\n//   };\n//   const handleSelectLiResult = (result) => {\n//     setSearchTerm(result); // Cập nhật giá trị input với kết quả đã chọn\n//     setShowDropdown(false); // Ẩn dropdown sau khi chọn\n//   };\n//   //  console.log(apiGetHistory.current)\n//   return (\n//     <>\n//       <OrderManagement\n//         onCreateOrder={openModal}\n//         onHistory={openModalHistory}\n//         openModalDetail={openModalDetail}\n//         setIdOrder={setIdOrder}\n//         refOrder ={apiGetOrder}\n//         setView = {setView}\n//         loadOrder = {loadOrder}\n//         setLoadLog =  {setLoadLog}\n//         setLoadOrder = {setLoadOrder}\n//       />\n\n//       <Modal isOpen={isOpen} onClose={closeModal}>\n//         <div className=\"Modal-title\">Create your order opening</div>\n//         <div className=\"divide\"></div>\n//         <div className=\"header-order\">\n//           <div className=\"search-container\">\n//             <div style={{ display: \"flex\", flex: 1, marginLeft: 10 }}>\n//               <span style={{ display: \"block\", paddingTop: \"10px\" }}>\n//                 Tìm kiếm:{\" \"}\n//               </span>\n//               <div className=\"search-result-container\">\n//                 <input\n//                   type=\"text\"\n//                   style={{ flex: 1 }}\n//                   className=\"order-mgmt-search\"\n//                   placeholder=\"Search by code or product name\"\n//                   value={searchTerm}\n//                   onChange={handleSearch}\n//                   onBlur={handleBlur} // Thêm onBlur để ẩn dropdown\n//                   onFocus={() => setShowDropdown(true)} // Hiển thị dropdown khi focus\n//                 />\n//                 {showDropdown && results.length > 0 && (\n//                   <ul className=\"dropdown\">\n//                     {results.map((result, index) => (\n//                       <li\n//                         key={index}\n//                         className=\"search-item\"\n//                         onClick={() => handleSelectLiResult(result)}\n//                       >\n//                         <div className=\"search-container-item\">\n//                           {result}\n//                           {suppOrPro && suggestions.length > 0 && (\n//                             <div\n//                               className=\"search-container-img\"\n//                               style={{\n//                                 backgroundImage: `url(${suggestions[index].image.secure_url})`,\n//                               }}\n//                             ></div>\n//                           )}\n//                         </div>\n//                         <div\n//                           className=\"divide\"\n//                           style={{ margin: \"8px 2px 0\", background: \"white\" }}\n//                         ></div>\n//                       </li>\n//                     ))}\n//                   </ul>\n//                 )}\n//               </div>\n//             </div>\n//           </div>\n//           <button className=\"btn-add-order\" onClick={handleAddToOrder}>\n//             Add to order\n//           </button>\n//         </div>\n//         <div className=\"body-modal\">\n//           <ContentOrder\n//             dataHis={idProductAdded}\n//             setIdProductAdded={setIdProductAdded}\n//             apiFetchOrderHistory = {apiGetOrder}\n//             apiGetHistory = {apiGetHistory}\n\n//           />\n//         </div>\n//       </Modal>\n//       <ModalHistory\n//         isOpen={openHistory}\n//         onClose={closeModalHistory}\n//         openModalDetail={openModalDetail}\n//         setIdOrder={setIdOrder}\n//         apiGetHistory= {apiGetHistory}\n//         setView = {setView}\n//         loadLog = {loadLog}\n//       />\n//       <ModalDetail\n//         isOpen={openDetail}\n//         onClose={closeModalDetail}\n//         idOrder={idOrder}\n//         view = {view}\n//         setLoadLog = {setLoadLog}\n//         setLoadOrder = {setLoadOrder}\n//       >\n//         {\" \"}\n//       </ModalDetail>\n//     </>\n//     // <div style={{ textAlign: 'center', margin: '20px' }}>\n//     //   <input\n//     //     type=\"file\"\n//     //     accept=\"image/*\"\n//     //     onChange={handleImageChange}\n//     //   />\n//     //   {selectedImage && (\n//     //     <div style={{ marginTop: '20px' }}>\n//     //       <h3>Ảnh đã tải lên:</h3>\n//     //       <img\n//     //         src={selectedImage}\n//     //         alt=\"Uploaded\"\n//     //         style={{ maxWidth: '300px', maxHeight: '300px' }}\n//     //       />\n//     //     </div>\n//     //   )}\n//     // </div>\n//   );\n// }\n\n// const ContentOrder = ({ dataHis, setIdProductAdded,apiFetchOrderHistory,apiGetHistory }) => {\n//   const initItem = (item) => {\n//     return {\n//       name: item.name,\n//       description: item.description,\n//       supplier: item.supplierDetails.name,\n//       price: item.purchasePrice.replace(/\\./g, \"\"),\n//       imageUrl: item.image.secure_url,\n//       supplierId: item.supplierDetails._id,\n//       quantity: 1,\n//       status: \"pending\",\n//       email: true,\n//       isChecked: true,\n//       emailName: item.supplierDetails.email,\n//       productId: item._id,\n//     };\n//   };\n//   const {startLoading,stopLoading}=useLoading();\n//   const { user, loading } = useAuth();\n//   const [listProductWereAdded, setListProductWereAdded] = useState([]);\n//   const listItem = dataHis.map((item) => initItem(item));\n//   const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\n//   const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(\n//     Array(listProductWereAdded.length).fill(false)\n//   );\n//   const [selectedSupplier, setSelectedSupplier] = useState(\n//     Array(listProductWereAdded.length).fill(\"\")\n//   );\n//   const [quantities, setQuantities] = useState(\n//     listProductWereAdded.map((product) => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\n//   );\n\n//   const [isOpen, setIsOpen] = useState(\n//     new Array(listProductWereAdded.length).fill(false)\n//   ); // Khởi tạo mảng isOpen\n//   const [myTax,setMyTax]= useState(10);\n//   useEffect(() => {\n//     if (dataHis && dataHis.length > 0) {\n//       const newItems = dataHis.map(initItem);\n//       console.log(dataHis, listProductWereAdded);\n//       if (\n//         !listProductWereAdded.some((item) =>\n//           dataHis.some((it) => it._id === item.productId)\n//         )\n//       ) {\n//         setListProductWereAdded((prevList) => [...newItems, ...prevList]);\n//       }\n//       setIdProductAdded([]);\n//     }\n//   }, [dataHis]);\n//   const handleSupplierChange = (supplier, index) => {\n//     setListProductWereAdded((prev) => {\n//       const newList = [...prev];\n//       newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\n//       return newList;\n//     });\n\n//     // Cập nhật selectedSupplier\n//     setSelectedSupplier((prev) => {\n//       const newSelectedSuppliers = [...prev];\n//       newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\n//       return newSelectedSuppliers;\n//     });\n\n//     // Ẩn dropdown sau khi chọn\n//     setIsDropdownOpenSupplier((prev) => {\n//       const newDropdownState = [...prev];\n//       newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\n//       return newDropdownState;\n//     });\n//   };\n//   const handleSupplierClick = (index) => {\n//     setIsDropdownOpenSupplier((prev) => {\n//       const newDropdownState = [...prev];\n//       newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\n//       return newDropdownState;\n//     });\n//   };\n//   const amountBill = () => {\n//     let sum = 0;\n//     listProductWereAdded.forEach((product) => {\n//       sum += product.price.replace(/\\./g, \"\") * product.quantity;\n//     });\n//     return sum;\n//   };\n//   const toggleDropdown = (index) => {\n//     setIsOpen((prev) => {\n//       const newOpen = [...prev];\n//       newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\n//       return newOpen;\n//     });\n//   };\n\n//   const dropdownRef = useRef(null);\n//   const dropdownRefSupplier = useRef(null);\n//   const handleStatusClick = (index) => {\n//     setDropdownOpenIndex((prev) => (prev === index ? null : index));\n//   };\n\n//   const handleStatusChange = (index, newStatus) => {\n//     setListProductWereAdded((prev) => {\n//       const updatedProducts = [...prev];\n//       updatedProducts[index].status = newStatus;\n//       setDropdownOpenIndex(null);\n//       return updatedProducts;\n//     });\n//     // Ẩn dropdown sau khi chọn\n//   };\n//   const handleClickOutside = (event) => {\n//     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n//       setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\n//     }\n//   };\n\n//   useEffect(() => {\n//     document.addEventListener(\"mousedown\", handleClickOutside);\n//     return () => {\n//       document.removeEventListener(\"mousedown\", handleClickOutside);\n//     };\n//   }, []);\n\n//   const increase = (index) => {\n//     setListProductWereAdded((prev) => {\n//       const newQuantities = [...prev];\n//       newQuantities[index].quantity += 1; // Tăng giá trị\n//       return newQuantities;\n//     });\n//   };\n\n//   const decrease = (index) => {\n//     setListProductWereAdded((prev) => {\n//       const newQuantities = [...prev];\n//       if (newQuantities[index].quantity > 0) {\n//         newQuantities[index].quantity -= 1; // Tăng giá trị\n//       }\n//       return newQuantities;\n//     });\n//   };\n\n//   const handleRemove = (index) => {\n//     setListProductWereAdded((prev) => {\n//       const newList = [...prev];\n//       newList.splice(index, 1); // Xoá phần tử\n//       return newList;\n//     });\n\n//     setIsOpen((prev) => {\n//       const newOpen = [...prev];\n//       newOpen.splice(index, 1); // Cập nhật mảng isOpen\n//       return newOpen;\n//     });\n//   };\n//   const handleInputQuantitty = (index, e) => {\n//     const newQuantity = e.target.value; // Lấy giá trị mới từ input\n//     setListProductWereAdded((prev) => {\n//       // Tạo bản sao của danh sách hiện tại\n//       const updatedList = [...prev];\n//       // Cập nhật số lượng sản phẩm tại chỉ số index\n//       updatedList[index] = {\n//         ...updatedList[index],\n//         quantity: newQuantity,\n//       };\n//       return updatedList; // Trả về danh sách đã cập nhật\n//     });\n//   };\n//   const handleCheckboxChange = (index) => {\n//     setListProductWereAdded((prev) => {\n//       const updatedProducts = [...listProductWereAdded];\n//       updatedProducts[index].email = !updatedProducts[index].email;\n//       return updatedProducts;\n//     });\n//   };\n\n//   const handleSubmit = async () => {\n//     const groupBySupplier = listProductWereAdded.reduce(\n//       (acc, item) => {\n//         // Kiểm tra xem đã có supplier này trong nhóm chưa\n//         if (!acc.dataForm[item.supplier]) {\n//           acc.dataForm[item.supplier] = [];\n//         }\n//         acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\n//         return acc;\n//       },\n//       { user: {}, dataForm: {} }\n//     );\n//     groupBySupplier.user = {\n//       id: user._id,\n//       name: user.name,\n//       email: user.email,\n//       ownerId: user.id_owner,\n//       id_owner:user.id_owner,\n//       role:user.role\n//     };\n//     groupBySupplier.tax = myTax\n//     const url = \"http://localhost:8080/api/import/orderHistory/save\";\n\n//     try {\n\n//       const response = await fetch(url, {\n//         method: \"POST\", // Phương thức POST\n//         headers: {\n//           \"Content-Type\": \"application/json\", // Xác định kiểu dữ liệu là JSON\n//         },\n//         body: JSON.stringify(groupBySupplier), // Chuyển đổi dữ liệu thành chuỗi JSON\n//       });\n\n//       if (response.ok) {\n//         // Nếu thành công, xử lý kết quả\n//         notify(1,\"you've completed importing goods\",\"Successfully!\")\n//         const responseData = await response.json();\n//         console.log(\"Dữ liệu đã được gửi thành công\", responseData);\n//         await apiFetchOrderHistory.current.fetchOrder(\"\")\n//         await apiGetHistory.current.debouncedFetchSuggestions(\" \", \"http://localhost:8080/api/import/loggingOrder/listOrder\", 1, 10);\n\n//         setIdProductAdded([]);\n//         setListProductWereAdded([]);\n//       } else {\n//         notify(2,\"you don't have the role to do this\",\"Fail!\")\n//         // Nếu có lỗi từ server\n//         console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\n//       }\n//     } catch (error) {\n//       console.error(\"Lỗi kết nối:\", error);\n//     }\n//   };\n\n//   return (\n//     <>\n//       <div className=\"list-product-title\">List product </div>\n//       <div className=\"list-product-content\">\n//         <div className=\"list-product-detail\">\n//           <table>\n//             <thead>\n//               <tr>\n//                 <th>STT</th>\n//                 <th>Ảnh Mô Tả</th>\n//                 <th>Sản Phẩm</th>\n//                 <th>Nhà Cung Cấp</th>\n//                 <th>Số Lượng</th>\n//                 <th>Thành Tiền</th>\n//                 <th>Status</th>\n//                 <th>Delete</th>\n//                 <th>Mail</th>\n//               </tr>\n//             </thead>\n//             <tbody>\n//               {listProductWereAdded.map((product, index) => (\n//                 <tr key={index}>\n//                   <td>{index + 1}</td>\n//                   <td>\n//                     <div\n//                       style={{\n//                         display: \"flex\",\n//                         justifyContent: \"center\",\n//                         alignItems: \"center\",\n//                       }}\n//                     >\n//                       <div\n//                         className=\"body-container-img-description\"\n//                         style={{ backgroundImage: `url(${product.imageUrl})` }}\n//                       ></div>\n//                     </div>\n//                   </td>\n//                   <td>\n//                     <div className=\"modal-body-product-name\">\n//                       {product.name}\n//                     </div>\n//                     <div className=\"modal-body-product-description\">\n//                       {product.description}\n//                     </div>\n//                   </td>\n//                   <td>\n//                     <div style={{ position: \"relative\" }}>\n//                       {product.supplier}\n//                     </div>\n//                   </td>\n//                   <td>\n//                     <div className=\"Quantity\">\n//                       <button\n//                         className=\"Quantity-button\"\n//                         onClick={() => decrease(index)}\n//                       >\n//                         -\n//                       </button>\n//                       <input\n//                         value={listProductWereAdded[index].quantity}\n//                         className=\"Quantity-input\"\n//                         onChange={(e) => handleInputQuantitty(index, e)}\n//                       />\n//                       <button\n//                         className=\"Quantity-button\"\n//                         onClick={() => increase(index)}\n//                       >\n//                         +\n//                       </button>\n//                     </div>\n//                   </td>\n//                   <td>\n//                     {(\n//                       product.price.replace(/\\./g, \"\") *\n//                       listProductWereAdded[index].quantity\n//                     ).toLocaleString()}{\" \"}\n//                     VND\n//                   </td>\n//                   <td>\n//                     <div\n//                       className={`product-status ${listProductWereAdded[index].status}`}\n//                       onClick={() => handleStatusClick(index)}\n//                       style={{ position: \"relative\", cursor: \"pointer\" }}\n//                     >\n//                       {product.status}\n//                       {dropdownOpenIndex === index && (\n//                         <div ref={dropdownRef} className=\"dropdown\">\n//                           <div\n//                             className=\"dropdown-item\"\n//                             onClick={() => handleStatusChange(index, \"pending\")}\n//                           >\n//                             Pending\n//                           </div>\n//                           <div\n//                             className=\"dropdown-item \"\n//                             onClick={() =>\n//                               handleStatusChange(index, \"deliveried\")\n//                             }\n//                           >\n//                             Delivered\n//                           </div>\n//                           <div\n//                             className=\"dropdown-item \"\n//                             onClick={() =>\n//                               handleStatusChange(index, \"canceled\")\n//                             }\n//                           >\n//                             Canceled\n//                           </div>\n//                         </div>\n//                       )}\n//                     </div>\n//                   </td>\n//                   <td>\n//                     <input\n//                       type=\"checkbox\"\n//                       checked={product.isChecked}\n//                       onChange={() => handleRemove(index)} // Call handler on change\n//                       id={`checkbox-${index}`}\n//                     />\n//                   </td>\n//                   <td>\n//                     <input\n//                       type=\"checkbox\"\n//                       checked={listProductWereAdded[index].email}\n//                       onChange={() => handleCheckboxChange(index)}\n//                     />\n//                   </td>\n//                 </tr>\n//               ))}\n//             </tbody>\n//           </table>\n//         </div>\n//         <div className=\"order-tax\">\n//           TAX :{\" \"}\n//           <input\n//           type = \"text\"\n//           style={{borderRadius:\"8px\",maxWidth:\"60px\", border:\"1px solid #333\",    fontSize:\"16px\",\n//             color:\"#333\",\n//             textAlign:\"right\",lineHeight:\"24px\",\n//             paddingRight:\"8px\",\n//           }}\n//           value={myTax}\n//           name= \"tax\"\n//           onChange={(e)=>{if (/^\\d*$/.test(e.target.value)){setMyTax(e.target.value)}}}\n//           />\n//           <span style={{ fontSize: 16, fontWeight: 300 }}>\n//                 {\"   \"}%\n//           </span>{\" \"}\n//         </div>\n//         <div className=\"order-tax\">\n//           Tổng tiền:{\" \"}\n//           <span style={{ fontSize: 16, fontWeight: 300 }}>\n//             {(amountBill() *( myTax+100)/100)\n//               .toFixed(0)\n//               .toString()\n//               .replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\")}{\" \"}\n//             VND\n//           </span>\n//         </div>\n//         <div className=\"complete-order\">\n//           <button onClick={() => handleSubmit()}>Complete</button>\n//         </div>\n//       </div>\n//     </>\n//   );\n// };\n\n// export default Import;\nimport OrderManagement from \"../../components/test/index\";\nimport ModalHistory from \"./ModalHistory\";\nimport React, { useState, useRef, useEffect, useCallback } from \"react\";\nimport axios from \"axios\";\nimport debounce from \"lodash.debounce\";\nimport Modal from \"./../../components/ComponentExport/Modal\";\nimport \"./import.css\";\nimport ModalDetail from \"./ModalDetail\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Import() {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [openHistory, setOpenHistory] = useState(false);\n  const [openDetail, setOpenDetail] = useState(false);\n  const [suggestions, setSuggestions] = useState([]);\n  const [suppOrPro, setSuppOrPro] = useState(false);\n  const [idProductAdded, setIdProductAdded] = useState([]);\n  const [idOrder, setIdOrder] = useState(null);\n  const [dataTop, setDataTop] = useState([]);\n  const {\n    user,\n    loading\n  } = useAuth();\n  const apiGetOrder = useRef();\n  const apiGetHistory = useRef();\n  const [view, setView] = useState(true);\n  const [loadLog, setLoadLog] = useState(false);\n  const [loadOrder, setLoadOrder] = useState(false);\n  // const id_owner = user.id_owner;\n  const openModal = () => setIsOpen(true);\n  const closeModal = () => setIsOpen(false);\n  const openModalHistory = () => setOpenHistory(true);\n  const closeModalHistory = () => setOpenHistory(false);\n  const closeModalDetail = () => setOpenDetail(false);\n  const openModalDetail = () => setOpenDetail(true);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        if (loading) return;\n        const res = await fetch(`http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`);\n        startLoading();\n        const dataRes = await res.json();\n        stopLoading();\n        setDataTop(prev => {\n          let newData;\n          if (prev) newData = [...prev, ...dataRes];else newData = [...dataRes];\n          return newData;\n        });\n      } catch (err) {\n        console.error(err);\n      }\n    };\n    fetchData();\n  }, [loading, user]);\n  const handleSearch = event => {\n    const term = event.target.value;\n    let keyword = term.trim();\n    setSearchTerm(term);\n    if (keyword.startsWith(\"@All\")) {\n      keyword = keyword.substr(4).trim();\n      setSuppOrPro(false);\n      if (keyword.length > 0) {\n        debouncedFetchSuggestions(keyword, `http://localhost:8080/api/import/supplier/search`);\n      } else {\n        setSuggestions([]);\n        setResults([]);\n      }\n    } else {\n      setSuppOrPro(true);\n      if (keyword) {\n        if (!dataTop.some(d => d.name.toLowerCase().includes(keyword.toLowerCase))) {\n          debouncedFetchSuggestions(keyword, `http://localhost:8080/api/import/products/exhibitProN`);\n        }\n        setResults([]);\n        setSuggestions([]);\n        const data_match = dataTop.filter(item => item.name.toLowerCase().includes(keyword.toLowerCase())).slice(0, 5);\n        setResults(data_match.map(d => d.name));\n        setSuggestions(data_match);\n      } else {\n        setSuggestions([]);\n        setResults([]);\n      }\n    }\n  };\n  const fetchProductSuggestions = async (keyword, hrefLink) => {\n    try {\n      const response = await axios.get(hrefLink, {\n        params: {\n          query: keyword,\n          ownerId: user.id_owner\n        }\n      });\n      const sugg = response.data.map(s => s.name);\n      setDataTop(prev => {\n        const existingIds = new Set(prev.map(item => item._id));\n        const newData = response.data.filter(item => !existingIds.has(item._id));\n        return [...prev, ...newData];\n      });\n    } catch (error) {\n      console.error(\"Error fetching suggestions:\", error);\n    }\n  };\n  const debouncedFetchSuggestions = useCallback(debounce((keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink), 500), [user, loading] // Chỉ tạo ra một lần\n  );\n  const handleAddToOrder = async () => {\n    const idPro = suggestions.filter(sugg => sugg.name == searchTerm);\n    const suppliersId = idPro ? idPro[0] : null;\n    try {\n      if (suppliersId) {\n        let response;\n        if (!suppOrPro) {\n          response = await fetch(`http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            }\n          });\n        }\n        if (!suppOrPro && response.ok) {\n          const data = await response.json();\n          setIdProductAdded(data);\n          setSearchTerm(\"\");\n          setResults([]);\n        } else if (suppOrPro) {\n          setIdProductAdded(idPro);\n          setSearchTerm(\"\");\n          setResults([]);\n        } else {\n          console.error(\"Error adding to order\");\n        }\n        setSuggestions([]);\n      }\n    } catch (error) {\n      console.error(\"Request failed\", error);\n    }\n  };\n  const handleBlur = () => {\n    setTimeout(() => {\n      setShowDropdown(false);\n    }, 700);\n  };\n  const handleSelectLiResult = result => {\n    setSearchTerm(result);\n    setShowDropdown(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(OrderManagement, {\n      onCreateOrder: openModal,\n      onHistory: openModalHistory,\n      openModalDetail: openModalDetail,\n      setIdOrder: setIdOrder,\n      refOrder: apiGetOrder,\n      setView: setView,\n      loadOrder: loadOrder,\n      setLoadLog: setLoadLog,\n      setLoadOrder: setLoadOrder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: isOpen,\n      onClose: closeModal,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"Modal-title\",\n        children: \"Create your order opening\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-order\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              flex: 1,\n              marginLeft: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"block\",\n                paddingTop: \"10px\"\n              },\n              children: [\"T\\xECm ki\\u1EBFm:\", \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-result-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                style: {\n                  flex: 1\n                },\n                className: \"order-mgmt-search\",\n                placeholder: \"Search by code or product name\",\n                value: searchTerm,\n                onChange: handleSearch,\n                onBlur: handleBlur // Thêm onBlur để ẩn dropdown\n                ,\n                onFocus: () => setShowDropdown(true) // Hiển thị dropdown khi focus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 17\n              }, this), showDropdown && results.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"dropdown\",\n                children: results.map((result, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"search-item\",\n                  onClick: () => handleSelectLiResult(result),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"search-container-item\",\n                    children: [result, suppOrPro && suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"search-container-img\",\n                      style: {\n                        backgroundImage: `url(${suggestions[index].image ? suggestions[index].image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"})`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 913,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"divide\",\n                    style: {\n                      margin: \"8px 2px 0\",\n                      background: \"white\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-add-order\",\n          onClick: handleAddToOrder,\n          children: \"Add to order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 885,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"body-modal\",\n        children: /*#__PURE__*/_jsxDEV(ContentOrder, {\n          dataHis: idProductAdded,\n          setIdProductAdded: setIdProductAdded,\n          apiFetchOrderHistory: apiGetOrder,\n          apiGetHistory: apiGetHistory,\n          setLoadOrder: setLoadOrder,\n          setLoadLog: setLoadLog\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 882,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalHistory, {\n      isOpen: openHistory,\n      onClose: closeModalHistory,\n      openModalDetail: openModalDetail,\n      setIdOrder: setIdOrder,\n      apiGetHistory: apiGetHistory,\n      setView: setView,\n      loadLog: loadLog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalDetail, {\n      isOpen: openDetail,\n      onClose: closeModalDetail,\n      idOrder: idOrder,\n      view: view,\n      setLoadLog: setLoadLog,\n      setLoadOrder: setLoadOrder,\n      children: \" \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 960,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Import, \"jBU3V33i7uSmPfPjeLqmtTs5UZk=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = Import;\nconst ContentOrder = ({\n  dataHis,\n  setIdProductAdded,\n  apiFetchOrderHistory,\n  apiGetHistory,\n  setLoadLog,\n  setLoadOrder\n}) => {\n  _s2();\n  const initItem = item => {\n    return {\n      name: item.name,\n      description: item.description,\n      supplier: item.supplierDetails.name,\n      price: item.purchasePrice.replace(/\\./g, \"\"),\n      imageUrl: item.image ? item.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n      supplierId: item.supplierDetails._id,\n      quantity: 1,\n      status: \"pending\",\n      email: true,\n      isChecked: true,\n      emailName: item.supplierDetails.email,\n      productId: item._id\n    };\n  };\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [listProductWereAdded, setListProductWereAdded] = useState([]);\n  const listItem = dataHis.map(item => initItem(item));\n  const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\n  const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(Array(listProductWereAdded.length).fill(false));\n  const [selectedSupplier, setSelectedSupplier] = useState(Array(listProductWereAdded.length).fill(\"\"));\n  const [quantities, setQuantities] = useState(listProductWereAdded.map(product => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\n  );\n  const [isOpen, setIsOpen] = useState(new Array(listProductWereAdded.length).fill(false)); // Khởi tạo mảng isOpen\n  const [myTax, setMyTax] = useState(10);\n  useEffect(() => {\n    if (dataHis && dataHis.length > 0) {\n      const newItems = dataHis.map(initItem);\n      console.log(dataHis, listProductWereAdded);\n      if (!listProductWereAdded.some(item => dataHis.some(it => it._id === item.productId))) {\n        setListProductWereAdded(prevList => [...newItems, ...prevList]);\n      }\n      setIdProductAdded([]);\n    }\n  }, [dataHis]);\n  const handleSupplierChange = (supplier, index) => {\n    setListProductWereAdded(prev => {\n      const newList = [...prev];\n      newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\n      return newList;\n    });\n\n    // Cập nhật selectedSupplier\n    setSelectedSupplier(prev => {\n      const newSelectedSuppliers = [...prev];\n      newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\n      return newSelectedSuppliers;\n    });\n\n    // Ẩn dropdown sau khi chọn\n    setIsDropdownOpenSupplier(prev => {\n      const newDropdownState = [...prev];\n      newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\n      return newDropdownState;\n    });\n  };\n  const handleSupplierClick = index => {\n    setIsDropdownOpenSupplier(prev => {\n      const newDropdownState = [...prev];\n      newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\n      return newDropdownState;\n    });\n  };\n  const amountBill = () => {\n    let sum = 0;\n    listProductWereAdded.forEach(product => {\n      sum += product.price.replace(/\\./g, \"\") * product.quantity;\n    });\n    return sum;\n  };\n  const toggleDropdown = index => {\n    setIsOpen(prev => {\n      const newOpen = [...prev];\n      newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\n      return newOpen;\n    });\n  };\n  const dropdownRef = useRef(null);\n  const dropdownRefSupplier = useRef(null);\n  const handleStatusClick = index => {\n    setDropdownOpenIndex(prev => prev === index ? null : index);\n  };\n  const handleStatusChange = (index, newStatus) => {\n    setListProductWereAdded(prev => {\n      const updatedProducts = [...prev];\n      updatedProducts[index].status = newStatus;\n      setDropdownOpenIndex(null);\n      return updatedProducts;\n    });\n    // Ẩn dropdown sau khi chọn\n  };\n  const handleClickOutside = event => {\n    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n      setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\n    }\n  };\n  useEffect(() => {\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  const increase = index => {\n    setListProductWereAdded(prev => {\n      const newQuantities = [...prev];\n      newQuantities[index].quantity += 1; // Tăng giá trị\n      return newQuantities;\n    });\n  };\n  const decrease = index => {\n    setListProductWereAdded(prev => {\n      const newQuantities = [...prev];\n      if (newQuantities[index].quantity > 0) {\n        newQuantities[index].quantity -= 1; // Tăng giá trị\n      }\n      return newQuantities;\n    });\n  };\n  const handleRemove = index => {\n    setListProductWereAdded(prev => {\n      const newList = [...prev];\n      newList.splice(index, 1); // Xoá phần tử\n      return newList;\n    });\n    setIsOpen(prev => {\n      const newOpen = [...prev];\n      newOpen.splice(index, 1); // Cập nhật mảng isOpen\n      return newOpen;\n    });\n  };\n  const handleInputQuantitty = (index, e) => {\n    const newQuantity = e.target.value; // Lấy giá trị mới từ input\n    setListProductWereAdded(prev => {\n      // Tạo bản sao của danh sách hiện tại\n      const updatedList = [...prev];\n      // Cập nhật số lượng sản phẩm tại chỉ số index\n      updatedList[index] = {\n        ...updatedList[index],\n        quantity: newQuantity\n      };\n      return updatedList; // Trả về danh sách đã cập nhật\n    });\n  };\n  const handleCheckboxChange = index => {\n    setListProductWereAdded(prev => {\n      const updatedProducts = [...listProductWereAdded];\n      updatedProducts[index].email = !updatedProducts[index].email;\n      return updatedProducts;\n    });\n  };\n  const handleSubmit = async () => {\n    console.log(\"baby take my hand\");\n    const groupBySupplier = listProductWereAdded.reduce((acc, item) => {\n      // Kiểm tra xem đã có supplier này trong nhóm chưa\n      if (!acc.dataForm[item.supplier]) {\n        acc.dataForm[item.supplier] = [];\n      }\n      acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\n      return acc;\n    }, {\n      user: {},\n      dataForm: {}\n    });\n    groupBySupplier.user = {\n      id: user._id,\n      name: user.name,\n      email: user.email,\n      ownerId: user.id_owner,\n      id_owner: user.id_owner,\n      role: user.role\n    };\n    groupBySupplier.tax = myTax;\n    const url = \"http://localhost:8080/api/import/orderHistory/save\";\n    try {\n      startLoading();\n      const response = await fetch(url, {\n        method: \"POST\",\n        // Phương thức POST\n        headers: {\n          \"Content-Type\": \"application/json\" // Xác định kiểu dữ liệu là JSON\n        },\n        body: JSON.stringify(groupBySupplier) // Chuyển đổi dữ liệu thành chuỗi JSON\n      });\n      stopLoading();\n      if (response.ok) {\n        notify(1, \"you've completed importing goods\", \"Successfully!\");\n        const responseData = await response.json();\n        console.log(\"Dữ liệu đã được gửi thành công\", responseData);\n        //await apiFetchOrderHistory.current.fetchOrder(\" \")\n        //await apiGetHistory.current.debouncedFetchSuggestions(\" \", \"http://localhost:8080/api/import/loggingOrder/listOrder\", 1, 10);\n        setLoadOrder(prev => !prev);\n        setLoadLog(prev => !prev);\n        setIdProductAdded([]);\n        setListProductWereAdded([]);\n      } else {\n        notify(2, \"you don't have the role to do this\", \"Fail!\");\n        // Nếu có lỗi từ server\n        console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\n      }\n    } catch (error) {\n      console.error(\"Lỗi kết nối:\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"list-product-title\",\n      children: \"List product \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"list-product-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"list-product-detail\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"STT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u1EA2nh M\\xF4 T\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1EA3n Ph\\u1EA9m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Nh\\xE0 Cung C\\u1EA5p\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1ED1 L\\u01B0\\u1EE3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Th\\xE0nh Ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Mail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: listProductWereAdded.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"body-container-img-description\",\n                    style: {\n                      backgroundImage: `url(${product.imageUrl})`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1241,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"modal-body-product-name\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"modal-body-product-description\",\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1251,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: \"relative\"\n                  },\n                  children: product.supplier\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"Quantity\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"Quantity-button\",\n                    onClick: () => decrease(index),\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    value: listProductWereAdded[index].quantity,\n                    className: \"Quantity-input\",\n                    onChange: e => handleInputQuantitty(index, e)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"Quantity-button\",\n                    onClick: () => increase(index),\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1273,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1261,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [(product.price.replace(/\\./g, \"\") * listProductWereAdded[index].quantity).toLocaleString(), \" \", \"VND\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `product-status ${listProductWereAdded[index].status}`,\n                  onClick: () => handleStatusClick(index),\n                  style: {\n                    position: \"relative\",\n                    cursor: \"pointer\"\n                  },\n                  children: [product.status, dropdownOpenIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                    ref: dropdownRef,\n                    className: \"dropdown\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item\",\n                      onClick: () => handleStatusChange(index, \"pending\"),\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1297,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item \",\n                      onClick: () => handleStatusChange(index, \"deliveried\"),\n                      children: \"Delivered\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1303,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item \",\n                      onClick: () => handleStatusChange(index, \"canceled\"),\n                      children: \"Canceled\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1311,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1296,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: product.isChecked,\n                  onChange: () => handleRemove(index) // Call handler on change\n                  ,\n                  id: `checkbox-${index}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: listProductWereAdded[index].email,\n                  onChange: () => handleCheckboxChange(index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1332,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-tax\",\n        children: [\"TAX :\", \" \", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          style: {\n            borderRadius: \"8px\",\n            maxWidth: \"60px\",\n            border: \"1px solid #333\",\n            fontSize: \"16px\",\n            color: \"#333\",\n            textAlign: \"right\",\n            lineHeight: \"24px\",\n            paddingRight: \"8px\"\n          },\n          value: myTax,\n          name: \"tax\",\n          onChange: e => {\n            if (/^\\d*$/.test(e.target.value)) {\n              setMyTax(e.target.value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16,\n            fontWeight: 300\n          },\n          children: [\"   \", \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 11\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-tax\",\n        children: [\"T\\u1ED5ng ti\\u1EC1n:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16,\n            fontWeight: 300\n          },\n          children: [(amountBill() * (myTax + 100) / 100).toFixed(0).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\"), \" \", \"VND\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"complete-order\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleSubmit(),\n          children: \"Complete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1378,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1213,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s2(ContentOrder, \"QfhfeXTDA1ENpiUi3DRshx2eVm4=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c2 = ContentOrder;\nexport default Import;\nvar _c, _c2;\n$RefreshReg$(_c, \"Import\");\n$RefreshReg$(_c2, \"ContentOrder\");", "map": {"version": 3, "names": ["OrderManagement", "ModalHistory", "React", "useState", "useRef", "useEffect", "useCallback", "axios", "debounce", "Modal", "ModalDetail", "useAuth", "notify", "useLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Import", "_s", "startLoading", "stopLoading", "isOpen", "setIsOpen", "searchTerm", "setSearchTerm", "results", "setResults", "showDropdown", "setShowDropdown", "openHistory", "setOpenHistory", "openDetail", "setOpenDetail", "suggestions", "setSuggestions", "suppOrPro", "setSuppOrPro", "idProductAdded", "setIdProductAdded", "idOrder", "setIdOrder", "dataTop", "setDataTop", "user", "loading", "apiGetOrder", "apiGetHistory", "view", "<PERSON><PERSON><PERSON><PERSON>", "loadLog", "setLoadLog", "loadOrder", "setLoadOrder", "openModal", "closeModal", "openModalHistory", "closeModalHistory", "closeModalDetail", "openModalDetail", "fetchData", "res", "fetch", "id_owner", "dataRes", "json", "prev", "newData", "err", "console", "error", "handleSearch", "event", "term", "target", "value", "keyword", "trim", "startsWith", "substr", "length", "debouncedFetchSuggestions", "some", "d", "name", "toLowerCase", "includes", "data_match", "filter", "item", "slice", "map", "fetchProductSuggestions", "hrefLink", "response", "get", "params", "query", "ownerId", "sugg", "data", "s", "existingIds", "Set", "_id", "has", "handleAddToOrder", "idPro", "suppliersId", "method", "headers", "ok", "handleBlur", "setTimeout", "handleSelectLiResult", "result", "children", "onCreateOrder", "onHistory", "refOrder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClose", "className", "style", "display", "flex", "marginLeft", "paddingTop", "type", "placeholder", "onChange", "onBlur", "onFocus", "index", "onClick", "backgroundImage", "image", "secure_url", "margin", "background", "ContentOrder", "dataHis", "apiFetchOrderHistory", "_c", "_s2", "initItem", "description", "supplier", "supplierDetails", "price", "purchasePrice", "replace", "imageUrl", "supplierId", "quantity", "status", "email", "isChecked", "emailName", "productId", "listProductWereAdded", "setListProductWereAdded", "listItem", "dropdownOpenIndex", "setDropdownOpenIndex", "isDropdownOpenSupplier", "setIsDropdownOpenSupplier", "Array", "fill", "selectedSupplier", "setSelectedSupplier", "quantities", "setQuantities", "product", "myTax", "setMyTax", "newItems", "log", "it", "prevList", "handleSupplierChange", "newList", "newSelectedSuppliers", "newDropdownState", "handleSupplierClick", "amountBill", "sum", "for<PERSON>ach", "toggleDropdown", "newOpen", "dropdownRef", "dropdownRefSupplier", "handleStatusClick", "handleStatusChange", "newStatus", "updatedProducts", "handleClickOutside", "current", "contains", "document", "addEventListener", "removeEventListener", "increase", "newQuantities", "decrease", "handleRemove", "splice", "handleInputQuantitty", "e", "newQuantity", "updatedList", "handleCheckboxChange", "handleSubmit", "groupBySupplier", "reduce", "acc", "dataForm", "push", "id", "role", "tax", "url", "body", "JSON", "stringify", "responseData", "statusText", "justifyContent", "alignItems", "position", "toLocaleString", "cursor", "ref", "checked", "borderRadius", "max<PERSON><PERSON><PERSON>", "border", "fontSize", "color", "textAlign", "lineHeight", "paddingRight", "test", "fontWeight", "toFixed", "toString", "_c2", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/Import/index.js"], "sourcesContent": ["// // import ImageUpload from \"../../components/Manage_product/image\"\r\n// // import Change_password from\"../../components/introduce/resetpassword.js\"\r\n// import OrderManagement from \"../../components/test/index\";\r\n// import ModalHistory from \"./ModalHistory\";\r\n// import React, {\r\n//   useState,\r\n//   useRef,\r\n//   useEffect,\r\n//   useCallback,\r\n//   useContext,\r\n// } from \"react\";\r\n// import axios from \"axios\";\r\n// import debounce from \"lodash.debounce\";\r\n// import Modal from \"./../../components/ComponentExport/Modal\";\r\n// import \"./import.css\";\r\n// import ModalDetail from \"./ModalDetail\";\r\n// import { useAuth} from \"../../components/introduce/useAuth\";\r\n// import { notify } from \"../../components/Notification/notification\";\r\n// import { useLoading } from \"../../components/introduce/Loading\";\r\n// function Import() {\r\n//   const [isOpen, setIsOpen] = useState(false);\r\n//   const [searchTerm, setSearchTerm] = useState(\"\");\r\n//   const [results, setResults] = useState([]);\r\n//   const [showDropdown, setShowDropdown] = useState(false);\r\n//   const [openHistory, setOpenHistory] = useState(false);\r\n//   const [openDetail, setOpenDetail] = useState(false);\r\n//   const [suggestions, setSuggestions] = useState([]);\r\n//   const [suppOrPro, setSuppOrPro] = useState(false);\r\n//   const [idProductAdded, setIdProductAdded] = useState([]);\r\n//   const [idOrder, setIdOrder] = useState(null);\r\n//   const [dataTop, setDataTop] = useState([]);\r\n//   const { user, loading } = useAuth();\r\n//   const apiGetOrder = useRef()\r\n//   const apiGetHistory = useRef()\r\n//   const [view,setView] = useState(true);\r\n//   const [loadLog,setLoadLog] = useState(false)\r\n//   const [loadOrder,setLoadOrder] = useState(false)\r\n//   // const id_owner = user.id_owner;\r\n//   const openModal = () => setIsOpen(true);\r\n//   const closeModal = () => setIsOpen(false);\r\n//   const openModalHistory = () => setOpenHistory(true);\r\n//   const closeModalHistory = () => setOpenHistory(false);\r\n//   const closeModalDetail = () => setOpenDetail(false);\r\n//   const openModalDetail = () => setOpenDetail(true);\r\n//   const {startLoading,stopLoading}=useLoading();\r\n//   useEffect(() => {\r\n//     const fetchData = async () => {\r\n//       try {\r\n//         if(loading)return;\r\n//         const res = await fetch(\r\n//           `http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`\r\n//         );\r\n//         const dataRes = await res.json();\r\n//         setDataTop(dataRes);\r\n\r\n//       } catch (err) {\r\n//         console.error(err);\r\n//       }\r\n//     };\r\n//     fetchData();\r\n//   }, [loading]);\r\n//   const handleSearch = (event) => {\r\n//     const term = event.target.value;\r\n//     let keyword = term.trim();\r\n//     setSearchTerm(term);\r\n//     if (keyword.startsWith(\"@All\")) {\r\n//       keyword = keyword.substr(4).trim();\r\n//       setSuppOrPro(false);\r\n//       if (keyword.length > 0) {\r\n//         debouncedFetchSuggestions(\r\n//           keyword,\r\n//           `http://localhost:8080/api/import/supplier/search`\r\n//         );\r\n//       } else {\r\n//         setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\r\n//       }\r\n//     } else {\r\n//       setSuppOrPro(true);\r\n//       if (keyword.length > 0) {\r\n//         const topData = dataTop\r\n//           .filter((item) =>\r\n//             item.name.toLowerCase().includes(keyword.toLowerCase())\r\n//           )\r\n//           .slice(0, 5);\r\n//         if (topData.length) {\r\n//           setResults(topData.map((item) => item.name));\r\n//           setSuggestions(topData);\r\n//         } else {\r\n//           console.log(\"jellooo\");\r\n//           debouncedFetchSuggestions(\r\n//             keyword,\r\n//             `http://localhost:8080/api/import/products/exhibitProN`\r\n//           );\r\n//         }\r\n//       } else {\r\n//         setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\r\n//       }\r\n//     }\r\n//   };\r\n//   // database\r\n//   const fetchProductSuggestions = async (keyword, hrefLink) => {\r\n//     try {\r\n//       const response = await axios.get(hrefLink, {\r\n//         params: {\r\n//           query: keyword,\r\n//           ownerId: user.id_owner,\r\n//         },\r\n//       });\r\n//       const sugg = response.data.map((s) => s.name);\r\n//       setResults(sugg);\r\n//       setDataTop((prev) => {\r\n//         const newData = [...prev, ...response.data];\r\n//         return newData;\r\n//       });\r\n//       setSuggestions(response.data);\r\n//     } catch (error) {\r\n//       console.error(\"Error fetching suggestions:\", error);\r\n//     }\r\n//   };\r\n//   const debouncedFetchSuggestions = useCallback(\r\n//     debounce(\r\n//       (keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink),\r\n//       500\r\n//     ),\r\n//     [user] // Chỉ tạo ra một lần\r\n//   );\r\n\r\n//   const handleAddToOrder = async () => {\r\n//     const idPro = suggestions.filter((sugg) => sugg.name == searchTerm);\r\n//     setSuggestions([]);\r\n//     const suppliersId = idPro ? idPro[0] : null;\r\n//     try {\r\n//       // Gửi request GET với query string chứa productId\r\n//       if (suppliersId) {\r\n//         let response;\r\n//         if (!suppOrPro) {\r\n//           response = await fetch(\r\n//             `http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`,\r\n//             {\r\n//               method: \"GET\",\r\n//               headers: {\r\n//                 \"Content-Type\": \"application/json\",\r\n//               },\r\n//             }\r\n//           );\r\n//         }\r\n\r\n//         // Kiểm tra nếu request thành công\r\n//         if (!suppOrPro && response.ok) {\r\n//           const data = await response.json(); // Dữ liệu trả về từ server (có thể là chi tiết sản phẩm)\r\n//           setIdProductAdded(data);\r\n//           // Xử lý dữ liệu từ server (Hiển thị thông tin đơn hàng, ví dụ...)\r\n//           setSearchTerm(\"\");\r\n//           setResults([]);\r\n//         } else if (suppOrPro) {\r\n//           setIdProductAdded(idPro);\r\n//           setSearchTerm(\"\");\r\n//           setResults([]);\r\n//         } else {\r\n//           console.error(\"Error adding to order\");\r\n//         }\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Request failed\", error);\r\n//     }\r\n//   };\r\n//   const handleBlur = () => {\r\n//     setTimeout(() => {\r\n//       setShowDropdown(false);\r\n//     }, 700);\r\n//   };\r\n//   const handleSelectLiResult = (result) => {\r\n//     setSearchTerm(result); // Cập nhật giá trị input với kết quả đã chọn\r\n//     setShowDropdown(false); // Ẩn dropdown sau khi chọn\r\n//   };\r\n//   //  console.log(apiGetHistory.current)\r\n//   return (\r\n//     <>\r\n//       <OrderManagement\r\n//         onCreateOrder={openModal}\r\n//         onHistory={openModalHistory}\r\n//         openModalDetail={openModalDetail}\r\n//         setIdOrder={setIdOrder}\r\n//         refOrder ={apiGetOrder}\r\n//         setView = {setView}\r\n//         loadOrder = {loadOrder}\r\n//         setLoadLog =  {setLoadLog}\r\n//         setLoadOrder = {setLoadOrder}\r\n//       />\r\n\r\n//       <Modal isOpen={isOpen} onClose={closeModal}>\r\n//         <div className=\"Modal-title\">Create your order opening</div>\r\n//         <div className=\"divide\"></div>\r\n//         <div className=\"header-order\">\r\n//           <div className=\"search-container\">\r\n//             <div style={{ display: \"flex\", flex: 1, marginLeft: 10 }}>\r\n//               <span style={{ display: \"block\", paddingTop: \"10px\" }}>\r\n//                 Tìm kiếm:{\" \"}\r\n//               </span>\r\n//               <div className=\"search-result-container\">\r\n//                 <input\r\n//                   type=\"text\"\r\n//                   style={{ flex: 1 }}\r\n//                   className=\"order-mgmt-search\"\r\n//                   placeholder=\"Search by code or product name\"\r\n//                   value={searchTerm}\r\n//                   onChange={handleSearch}\r\n//                   onBlur={handleBlur} // Thêm onBlur để ẩn dropdown\r\n//                   onFocus={() => setShowDropdown(true)} // Hiển thị dropdown khi focus\r\n//                 />\r\n//                 {showDropdown && results.length > 0 && (\r\n//                   <ul className=\"dropdown\">\r\n//                     {results.map((result, index) => (\r\n//                       <li\r\n//                         key={index}\r\n//                         className=\"search-item\"\r\n//                         onClick={() => handleSelectLiResult(result)}\r\n//                       >\r\n//                         <div className=\"search-container-item\">\r\n//                           {result}\r\n//                           {suppOrPro && suggestions.length > 0 && (\r\n//                             <div\r\n//                               className=\"search-container-img\"\r\n//                               style={{\r\n//                                 backgroundImage: `url(${suggestions[index].image.secure_url})`,\r\n//                               }}\r\n//                             ></div>\r\n//                           )}\r\n//                         </div>\r\n//                         <div\r\n//                           className=\"divide\"\r\n//                           style={{ margin: \"8px 2px 0\", background: \"white\" }}\r\n//                         ></div>\r\n//                       </li>\r\n//                     ))}\r\n//                   </ul>\r\n//                 )}\r\n//               </div>\r\n//             </div>\r\n//           </div>\r\n//           <button className=\"btn-add-order\" onClick={handleAddToOrder}>\r\n//             Add to order\r\n//           </button>\r\n//         </div>\r\n//         <div className=\"body-modal\">\r\n//           <ContentOrder\r\n//             dataHis={idProductAdded}\r\n//             setIdProductAdded={setIdProductAdded}\r\n//             apiFetchOrderHistory = {apiGetOrder}\r\n//             apiGetHistory = {apiGetHistory}\r\n\r\n//           />\r\n//         </div>\r\n//       </Modal>\r\n//       <ModalHistory\r\n//         isOpen={openHistory}\r\n//         onClose={closeModalHistory}\r\n//         openModalDetail={openModalDetail}\r\n//         setIdOrder={setIdOrder}\r\n//         apiGetHistory= {apiGetHistory}\r\n//         setView = {setView}\r\n//         loadLog = {loadLog}\r\n//       />\r\n//       <ModalDetail\r\n//         isOpen={openDetail}\r\n//         onClose={closeModalDetail}\r\n//         idOrder={idOrder}\r\n//         view = {view}\r\n//         setLoadLog = {setLoadLog}\r\n//         setLoadOrder = {setLoadOrder}\r\n//       >\r\n//         {\" \"}\r\n//       </ModalDetail>\r\n//     </>\r\n//     // <div style={{ textAlign: 'center', margin: '20px' }}>\r\n//     //   <input\r\n//     //     type=\"file\"\r\n//     //     accept=\"image/*\"\r\n//     //     onChange={handleImageChange}\r\n//     //   />\r\n//     //   {selectedImage && (\r\n//     //     <div style={{ marginTop: '20px' }}>\r\n//     //       <h3>Ảnh đã tải lên:</h3>\r\n//     //       <img\r\n//     //         src={selectedImage}\r\n//     //         alt=\"Uploaded\"\r\n//     //         style={{ maxWidth: '300px', maxHeight: '300px' }}\r\n//     //       />\r\n//     //     </div>\r\n//     //   )}\r\n//     // </div>\r\n//   );\r\n// }\r\n\r\n// const ContentOrder = ({ dataHis, setIdProductAdded,apiFetchOrderHistory,apiGetHistory }) => {\r\n//   const initItem = (item) => {\r\n//     return {\r\n//       name: item.name,\r\n//       description: item.description,\r\n//       supplier: item.supplierDetails.name,\r\n//       price: item.purchasePrice.replace(/\\./g, \"\"),\r\n//       imageUrl: item.image.secure_url,\r\n//       supplierId: item.supplierDetails._id,\r\n//       quantity: 1,\r\n//       status: \"pending\",\r\n//       email: true,\r\n//       isChecked: true,\r\n//       emailName: item.supplierDetails.email,\r\n//       productId: item._id,\r\n//     };\r\n//   };\r\n//   const {startLoading,stopLoading}=useLoading();\r\n//   const { user, loading } = useAuth();\r\n//   const [listProductWereAdded, setListProductWereAdded] = useState([]);\r\n//   const listItem = dataHis.map((item) => initItem(item));\r\n//   const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\r\n//   const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(\r\n//     Array(listProductWereAdded.length).fill(false)\r\n//   );\r\n//   const [selectedSupplier, setSelectedSupplier] = useState(\r\n//     Array(listProductWereAdded.length).fill(\"\")\r\n//   );\r\n//   const [quantities, setQuantities] = useState(\r\n//     listProductWereAdded.map((product) => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\r\n//   );\r\n\r\n//   const [isOpen, setIsOpen] = useState(\r\n//     new Array(listProductWereAdded.length).fill(false)\r\n//   ); // Khởi tạo mảng isOpen\r\n//   const [myTax,setMyTax]= useState(10);\r\n//   useEffect(() => {\r\n//     if (dataHis && dataHis.length > 0) {\r\n//       const newItems = dataHis.map(initItem);\r\n//       console.log(dataHis, listProductWereAdded);\r\n//       if (\r\n//         !listProductWereAdded.some((item) =>\r\n//           dataHis.some((it) => it._id === item.productId)\r\n//         )\r\n//       ) {\r\n//         setListProductWereAdded((prevList) => [...newItems, ...prevList]);\r\n//       }\r\n//       setIdProductAdded([]);\r\n//     }\r\n//   }, [dataHis]);\r\n//   const handleSupplierChange = (supplier, index) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const newList = [...prev];\r\n//       newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\r\n//       return newList;\r\n//     });\r\n\r\n//     // Cập nhật selectedSupplier\r\n//     setSelectedSupplier((prev) => {\r\n//       const newSelectedSuppliers = [...prev];\r\n//       newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\r\n//       return newSelectedSuppliers;\r\n//     });\r\n\r\n//     // Ẩn dropdown sau khi chọn\r\n//     setIsDropdownOpenSupplier((prev) => {\r\n//       const newDropdownState = [...prev];\r\n//       newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\r\n//       return newDropdownState;\r\n//     });\r\n//   };\r\n//   const handleSupplierClick = (index) => {\r\n//     setIsDropdownOpenSupplier((prev) => {\r\n//       const newDropdownState = [...prev];\r\n//       newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\r\n//       return newDropdownState;\r\n//     });\r\n//   };\r\n//   const amountBill = () => {\r\n//     let sum = 0;\r\n//     listProductWereAdded.forEach((product) => {\r\n//       sum += product.price.replace(/\\./g, \"\") * product.quantity;\r\n//     });\r\n//     return sum;\r\n//   };\r\n//   const toggleDropdown = (index) => {\r\n//     setIsOpen((prev) => {\r\n//       const newOpen = [...prev];\r\n//       newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\r\n//       return newOpen;\r\n//     });\r\n//   };\r\n\r\n//   const dropdownRef = useRef(null);\r\n//   const dropdownRefSupplier = useRef(null);\r\n//   const handleStatusClick = (index) => {\r\n//     setDropdownOpenIndex((prev) => (prev === index ? null : index));\r\n//   };\r\n\r\n//   const handleStatusChange = (index, newStatus) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const updatedProducts = [...prev];\r\n//       updatedProducts[index].status = newStatus;\r\n//       setDropdownOpenIndex(null);\r\n//       return updatedProducts;\r\n//     });\r\n//     // Ẩn dropdown sau khi chọn\r\n//   };\r\n//   const handleClickOutside = (event) => {\r\n//     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n//       setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\r\n//     }\r\n//   };\r\n\r\n//   useEffect(() => {\r\n//     document.addEventListener(\"mousedown\", handleClickOutside);\r\n//     return () => {\r\n//       document.removeEventListener(\"mousedown\", handleClickOutside);\r\n//     };\r\n//   }, []);\r\n\r\n//   const increase = (index) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const newQuantities = [...prev];\r\n//       newQuantities[index].quantity += 1; // Tăng giá trị\r\n//       return newQuantities;\r\n//     });\r\n//   };\r\n\r\n//   const decrease = (index) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const newQuantities = [...prev];\r\n//       if (newQuantities[index].quantity > 0) {\r\n//         newQuantities[index].quantity -= 1; // Tăng giá trị\r\n//       }\r\n//       return newQuantities;\r\n//     });\r\n//   };\r\n\r\n//   const handleRemove = (index) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const newList = [...prev];\r\n//       newList.splice(index, 1); // Xoá phần tử\r\n//       return newList;\r\n//     });\r\n\r\n//     setIsOpen((prev) => {\r\n//       const newOpen = [...prev];\r\n//       newOpen.splice(index, 1); // Cập nhật mảng isOpen\r\n//       return newOpen;\r\n//     });\r\n//   };\r\n//   const handleInputQuantitty = (index, e) => {\r\n//     const newQuantity = e.target.value; // Lấy giá trị mới từ input\r\n//     setListProductWereAdded((prev) => {\r\n//       // Tạo bản sao của danh sách hiện tại\r\n//       const updatedList = [...prev];\r\n//       // Cập nhật số lượng sản phẩm tại chỉ số index\r\n//       updatedList[index] = {\r\n//         ...updatedList[index],\r\n//         quantity: newQuantity,\r\n//       };\r\n//       return updatedList; // Trả về danh sách đã cập nhật\r\n//     });\r\n//   };\r\n//   const handleCheckboxChange = (index) => {\r\n//     setListProductWereAdded((prev) => {\r\n//       const updatedProducts = [...listProductWereAdded];\r\n//       updatedProducts[index].email = !updatedProducts[index].email;\r\n//       return updatedProducts;\r\n//     });\r\n//   };\r\n\r\n//   const handleSubmit = async () => {\r\n//     const groupBySupplier = listProductWereAdded.reduce(\r\n//       (acc, item) => {\r\n//         // Kiểm tra xem đã có supplier này trong nhóm chưa\r\n//         if (!acc.dataForm[item.supplier]) {\r\n//           acc.dataForm[item.supplier] = [];\r\n//         }\r\n//         acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\r\n//         return acc;\r\n//       },\r\n//       { user: {}, dataForm: {} }\r\n//     );\r\n//     groupBySupplier.user = {\r\n//       id: user._id,\r\n//       name: user.name,\r\n//       email: user.email,\r\n//       ownerId: user.id_owner,\r\n//       id_owner:user.id_owner,\r\n//       role:user.role\r\n//     };\r\n//     groupBySupplier.tax = myTax\r\n//     const url = \"http://localhost:8080/api/import/orderHistory/save\";\r\n\r\n//     try {\r\n\r\n//       const response = await fetch(url, {\r\n//         method: \"POST\", // Phương thức POST\r\n//         headers: {\r\n//           \"Content-Type\": \"application/json\", // Xác định kiểu dữ liệu là JSON\r\n//         },\r\n//         body: JSON.stringify(groupBySupplier), // Chuyển đổi dữ liệu thành chuỗi JSON\r\n//       });\r\n\r\n//       if (response.ok) {\r\n//         // Nếu thành công, xử lý kết quả\r\n//         notify(1,\"you've completed importing goods\",\"Successfully!\")\r\n//         const responseData = await response.json();\r\n//         console.log(\"Dữ liệu đã được gửi thành công\", responseData);\r\n//         await apiFetchOrderHistory.current.fetchOrder(\"\")\r\n//         await apiGetHistory.current.debouncedFetchSuggestions(\" \", \"http://localhost:8080/api/import/loggingOrder/listOrder\", 1, 10);\r\n\r\n//         setIdProductAdded([]);\r\n//         setListProductWereAdded([]);\r\n//       } else {\r\n//         notify(2,\"you don't have the role to do this\",\"Fail!\")\r\n//         // Nếu có lỗi từ server\r\n//         console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Lỗi kết nối:\", error);\r\n//     }\r\n//   };\r\n\r\n//   return (\r\n//     <>\r\n//       <div className=\"list-product-title\">List product </div>\r\n//       <div className=\"list-product-content\">\r\n//         <div className=\"list-product-detail\">\r\n//           <table>\r\n//             <thead>\r\n//               <tr>\r\n//                 <th>STT</th>\r\n//                 <th>Ảnh Mô Tả</th>\r\n//                 <th>Sản Phẩm</th>\r\n//                 <th>Nhà Cung Cấp</th>\r\n//                 <th>Số Lượng</th>\r\n//                 <th>Thành Tiền</th>\r\n//                 <th>Status</th>\r\n//                 <th>Delete</th>\r\n//                 <th>Mail</th>\r\n//               </tr>\r\n//             </thead>\r\n//             <tbody>\r\n//               {listProductWereAdded.map((product, index) => (\r\n//                 <tr key={index}>\r\n//                   <td>{index + 1}</td>\r\n//                   <td>\r\n//                     <div\r\n//                       style={{\r\n//                         display: \"flex\",\r\n//                         justifyContent: \"center\",\r\n//                         alignItems: \"center\",\r\n//                       }}\r\n//                     >\r\n//                       <div\r\n//                         className=\"body-container-img-description\"\r\n//                         style={{ backgroundImage: `url(${product.imageUrl})` }}\r\n//                       ></div>\r\n//                     </div>\r\n//                   </td>\r\n//                   <td>\r\n//                     <div className=\"modal-body-product-name\">\r\n//                       {product.name}\r\n//                     </div>\r\n//                     <div className=\"modal-body-product-description\">\r\n//                       {product.description}\r\n//                     </div>\r\n//                   </td>\r\n//                   <td>\r\n//                     <div style={{ position: \"relative\" }}>\r\n//                       {product.supplier}\r\n//                     </div>\r\n//                   </td>\r\n//                   <td>\r\n//                     <div className=\"Quantity\">\r\n//                       <button\r\n//                         className=\"Quantity-button\"\r\n//                         onClick={() => decrease(index)}\r\n//                       >\r\n//                         -\r\n//                       </button>\r\n//                       <input\r\n//                         value={listProductWereAdded[index].quantity}\r\n//                         className=\"Quantity-input\"\r\n//                         onChange={(e) => handleInputQuantitty(index, e)}\r\n//                       />\r\n//                       <button\r\n//                         className=\"Quantity-button\"\r\n//                         onClick={() => increase(index)}\r\n//                       >\r\n//                         +\r\n//                       </button>\r\n//                     </div>\r\n//                   </td>\r\n//                   <td>\r\n//                     {(\r\n//                       product.price.replace(/\\./g, \"\") *\r\n//                       listProductWereAdded[index].quantity\r\n//                     ).toLocaleString()}{\" \"}\r\n//                     VND\r\n//                   </td>\r\n//                   <td>\r\n//                     <div\r\n//                       className={`product-status ${listProductWereAdded[index].status}`}\r\n//                       onClick={() => handleStatusClick(index)}\r\n//                       style={{ position: \"relative\", cursor: \"pointer\" }}\r\n//                     >\r\n//                       {product.status}\r\n//                       {dropdownOpenIndex === index && (\r\n//                         <div ref={dropdownRef} className=\"dropdown\">\r\n//                           <div\r\n//                             className=\"dropdown-item\"\r\n//                             onClick={() => handleStatusChange(index, \"pending\")}\r\n//                           >\r\n//                             Pending\r\n//                           </div>\r\n//                           <div\r\n//                             className=\"dropdown-item \"\r\n//                             onClick={() =>\r\n//                               handleStatusChange(index, \"deliveried\")\r\n//                             }\r\n//                           >\r\n//                             Delivered\r\n//                           </div>\r\n//                           <div\r\n//                             className=\"dropdown-item \"\r\n//                             onClick={() =>\r\n//                               handleStatusChange(index, \"canceled\")\r\n//                             }\r\n//                           >\r\n//                             Canceled\r\n//                           </div>\r\n//                         </div>\r\n//                       )}\r\n//                     </div>\r\n//                   </td>\r\n//                   <td>\r\n//                     <input\r\n//                       type=\"checkbox\"\r\n//                       checked={product.isChecked}\r\n//                       onChange={() => handleRemove(index)} // Call handler on change\r\n//                       id={`checkbox-${index}`}\r\n//                     />\r\n//                   </td>\r\n//                   <td>\r\n//                     <input\r\n//                       type=\"checkbox\"\r\n//                       checked={listProductWereAdded[index].email}\r\n//                       onChange={() => handleCheckboxChange(index)}\r\n//                     />\r\n//                   </td>\r\n//                 </tr>\r\n//               ))}\r\n//             </tbody>\r\n//           </table>\r\n//         </div>\r\n//         <div className=\"order-tax\">\r\n//           TAX :{\" \"}\r\n//           <input\r\n//           type = \"text\"\r\n//           style={{borderRadius:\"8px\",maxWidth:\"60px\", border:\"1px solid #333\",    fontSize:\"16px\",\r\n//             color:\"#333\",\r\n//             textAlign:\"right\",lineHeight:\"24px\",\r\n//             paddingRight:\"8px\",\r\n//           }}\r\n//           value={myTax}\r\n//           name= \"tax\"\r\n//           onChange={(e)=>{if (/^\\d*$/.test(e.target.value)){setMyTax(e.target.value)}}}\r\n//           />\r\n//           <span style={{ fontSize: 16, fontWeight: 300 }}>\r\n//                 {\"   \"}%\r\n//           </span>{\" \"}\r\n//         </div>\r\n//         <div className=\"order-tax\">\r\n//           Tổng tiền:{\" \"}\r\n//           <span style={{ fontSize: 16, fontWeight: 300 }}>\r\n//             {(amountBill() *( myTax+100)/100)\r\n//               .toFixed(0)\r\n//               .toString()\r\n//               .replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\")}{\" \"}\r\n//             VND\r\n//           </span>\r\n//         </div>\r\n//         <div className=\"complete-order\">\r\n//           <button onClick={() => handleSubmit()}>Complete</button>\r\n//         </div>\r\n//       </div>\r\n//     </>\r\n//   );\r\n// };\r\n\r\n// export default Import;\r\nimport OrderManagement from \"../../components/test/index\";\r\nimport ModalHistory from \"./ModalHistory\";\r\nimport React, { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport axios from \"axios\";\r\nimport debounce from \"lodash.debounce\";\r\nimport Modal from \"./../../components/ComponentExport/Modal\";\r\nimport \"./import.css\";\r\nimport ModalDetail from \"./ModalDetail\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nfunction Import() {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [results, setResults] = useState([]);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [openHistory, setOpenHistory] = useState(false);\r\n  const [openDetail, setOpenDetail] = useState(false);\r\n  const [suggestions, setSuggestions] = useState([]);\r\n  const [suppOrPro, setSuppOrPro] = useState(false);\r\n  const [idProductAdded, setIdProductAdded] = useState([]);\r\n  const [idOrder, setIdOrder] = useState(null);\r\n  const [dataTop, setDataTop] = useState([]);\r\n  const { user, loading } = useAuth();\r\n  const apiGetOrder = useRef();\r\n  const apiGetHistory = useRef();\r\n  const [view, setView] = useState(true);\r\n  const [loadLog, setLoadLog] = useState(false);\r\n  const [loadOrder, setLoadOrder] = useState(false);\r\n  // const id_owner = user.id_owner;\r\n  const openModal = () => setIsOpen(true);\r\n  const closeModal = () => setIsOpen(false);\r\n  const openModalHistory = () => setOpenHistory(true);\r\n  const closeModalHistory = () => setOpenHistory(false);\r\n  const closeModalDetail = () => setOpenDetail(false);\r\n  const openModalDetail = () => setOpenDetail(true);\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        if (loading) return;\r\n        const res = await fetch(\r\n          `http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`\r\n        );\r\n        startLoading();\r\n        const dataRes = await res.json();\r\n        stopLoading();\r\n        setDataTop((prev) => {\r\n          let newData;\r\n          if (prev) newData = [...prev, ...dataRes];\r\n          else newData = [...dataRes];\r\n          return newData;\r\n        });\r\n      } catch (err) {\r\n        console.error(err);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [loading, user]);\r\n\r\n  const handleSearch = (event) => {\r\n    const term = event.target.value;\r\n    let keyword = term.trim();\r\n    setSearchTerm(term);\r\n    if (keyword.startsWith(\"@All\")) {\r\n      keyword = keyword.substr(4).trim();\r\n      setSuppOrPro(false);\r\n      if (keyword.length > 0) {\r\n        debouncedFetchSuggestions(\r\n          keyword,\r\n          `http://localhost:8080/api/import/supplier/search`\r\n        );\r\n      } else {\r\n        setSuggestions([]);\r\n        setResults([]);\r\n      }\r\n    } else {\r\n      setSuppOrPro(true);\r\n      if (keyword) {\r\n        if (\r\n          !dataTop.some((d) =>\r\n            d.name.toLowerCase().includes(keyword.toLowerCase)\r\n          )\r\n        ) {\r\n          debouncedFetchSuggestions(\r\n            keyword,\r\n            `http://localhost:8080/api/import/products/exhibitProN`\r\n          );\r\n        }\r\n        setResults([]);\r\n        setSuggestions([]);\r\n        const data_match = dataTop\r\n          .filter((item) =>\r\n            item.name.toLowerCase().includes(keyword.toLowerCase())\r\n          )\r\n          .slice(0, 5);\r\n        setResults(data_match.map((d) => d.name));\r\n        setSuggestions(data_match);\r\n      } else {\r\n        setSuggestions([]);\r\n        setResults([]);\r\n      }\r\n    }\r\n  };\r\n  const fetchProductSuggestions = async (keyword, hrefLink) => {\r\n    try {\r\n      const response = await axios.get(hrefLink, {\r\n        params: {\r\n          query: keyword,\r\n          ownerId: user.id_owner,\r\n        },\r\n      });\r\n      const sugg = response.data.map((s) => s.name);\r\n      setDataTop((prev) => {\r\n        const existingIds = new Set(prev.map((item) => item._id));\r\n        const newData = response.data.filter(\r\n          (item) => !existingIds.has(item._id)\r\n        );\r\n        return [...prev, ...newData];\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error fetching suggestions:\", error);\r\n    }\r\n  };\r\n  const debouncedFetchSuggestions = useCallback(\r\n    debounce(\r\n      (keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink),\r\n      500\r\n    ),\r\n    [user, loading] // Chỉ tạo ra một lần\r\n  );\r\n\r\n  const handleAddToOrder = async () => {\r\n    const idPro = suggestions.filter((sugg) => sugg.name == searchTerm);\r\n    const suppliersId = idPro ? idPro[0] : null;\r\n    try {\r\n      if (suppliersId) {\r\n        let response;\r\n        if (!suppOrPro) {\r\n          response = await fetch(\r\n            `http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`,\r\n            {\r\n              method: \"GET\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n            }\r\n          );\r\n        }\r\n\r\n        if (!suppOrPro && response.ok) {\r\n          const data = await response.json();\r\n          setIdProductAdded(data);\r\n          setSearchTerm(\"\");\r\n          setResults([]);\r\n        } else if (suppOrPro) {\r\n          setIdProductAdded(idPro);\r\n          setSearchTerm(\"\");\r\n          setResults([]);\r\n        } else {\r\n          console.error(\"Error adding to order\");\r\n        }\r\n        setSuggestions([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Request failed\", error);\r\n    }\r\n  };\r\n  const handleBlur = () => {\r\n    setTimeout(() => {\r\n      setShowDropdown(false);\r\n    }, 700);\r\n  };\r\n  const handleSelectLiResult = (result) => {\r\n    setSearchTerm(result);\r\n    setShowDropdown(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <OrderManagement\r\n        onCreateOrder={openModal}\r\n        onHistory={openModalHistory}\r\n        openModalDetail={openModalDetail}\r\n        setIdOrder={setIdOrder}\r\n        refOrder={apiGetOrder}\r\n        setView={setView}\r\n        loadOrder={loadOrder}\r\n        setLoadLog={setLoadLog}\r\n        setLoadOrder={setLoadOrder}\r\n      />\r\n\r\n      <Modal isOpen={isOpen} onClose={closeModal}>\r\n        <div className=\"Modal-title\">Create your order opening</div>\r\n        <div className=\"divide\"></div>\r\n        <div className=\"header-order\">\r\n          <div className=\"search-container\">\r\n            <div style={{ display: \"flex\", flex: 1, marginLeft: 10 }}>\r\n              <span style={{ display: \"block\", paddingTop: \"10px\" }}>\r\n                Tìm kiếm:{\" \"}\r\n              </span>\r\n              <div className=\"search-result-container\">\r\n                <input\r\n                  type=\"text\"\r\n                  style={{ flex: 1 }}\r\n                  className=\"order-mgmt-search\"\r\n                  placeholder=\"Search by code or product name\"\r\n                  value={searchTerm}\r\n                  onChange={handleSearch}\r\n                  onBlur={handleBlur} // Thêm onBlur để ẩn dropdown\r\n                  onFocus={() => setShowDropdown(true)} // Hiển thị dropdown khi focus\r\n                />\r\n                {showDropdown && results.length > 0 && (\r\n                  <ul className=\"dropdown\">\r\n                    {results.map((result, index) => (\r\n                      <li\r\n                        key={index}\r\n                        className=\"search-item\"\r\n                        onClick={() => handleSelectLiResult(result)}\r\n                      >\r\n                        <div className=\"search-container-item\">\r\n                          {result}\r\n                          {suppOrPro && suggestions.length > 0 && (\r\n                            <div\r\n                              className=\"search-container-img\"\r\n                              style={{\r\n                                backgroundImage: `url(${\r\n                                  suggestions[index].image\r\n                                    ? suggestions[index].image.secure_url\r\n                                    : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                                })`,\r\n                              }}\r\n                            ></div>\r\n                          )}\r\n                        </div>\r\n                        <div\r\n                          className=\"divide\"\r\n                          style={{ margin: \"8px 2px 0\", background: \"white\" }}\r\n                        ></div>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button className=\"btn-add-order\" onClick={handleAddToOrder}>\r\n            Add to order\r\n          </button>\r\n        </div>\r\n        <div className=\"body-modal\">\r\n          <ContentOrder\r\n            dataHis={idProductAdded}\r\n            setIdProductAdded={setIdProductAdded}\r\n            apiFetchOrderHistory={apiGetOrder}\r\n            apiGetHistory={apiGetHistory}\r\n            setLoadOrder={setLoadOrder}\r\n            setLoadLog={setLoadLog}\r\n          />\r\n        </div>\r\n      </Modal>\r\n      <ModalHistory\r\n        isOpen={openHistory}\r\n        onClose={closeModalHistory}\r\n        openModalDetail={openModalDetail}\r\n        setIdOrder={setIdOrder}\r\n        apiGetHistory={apiGetHistory}\r\n        setView={setView}\r\n        loadLog={loadLog}\r\n      />\r\n      <ModalDetail\r\n        isOpen={openDetail}\r\n        onClose={closeModalDetail}\r\n        idOrder={idOrder}\r\n        view={view}\r\n        setLoadLog={setLoadLog}\r\n        setLoadOrder={setLoadOrder}\r\n      >\r\n        {\" \"}\r\n      </ModalDetail>\r\n    </>\r\n  );\r\n}\r\n\r\nconst ContentOrder = ({\r\n  dataHis,\r\n  setIdProductAdded,\r\n  apiFetchOrderHistory,\r\n  apiGetHistory,\r\n  setLoadLog,\r\n  setLoadOrder,\r\n}) => {\r\n  const initItem = (item) => {\r\n    return {\r\n      name: item.name,\r\n      description: item.description,\r\n      supplier: item.supplierDetails.name,\r\n      price: item.purchasePrice.replace(/\\./g, \"\"),\r\n      imageUrl: item.image\r\n        ? item.image.secure_url\r\n        : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\r\n      supplierId: item.supplierDetails._id,\r\n      quantity: 1,\r\n      status: \"pending\",\r\n      email: true,\r\n      isChecked: true,\r\n      emailName: item.supplierDetails.email,\r\n      productId: item._id,\r\n    };\r\n  };\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, loading } = useAuth();\r\n  const [listProductWereAdded, setListProductWereAdded] = useState([]);\r\n  const listItem = dataHis.map((item) => initItem(item));\r\n  const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\r\n  const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(\r\n    Array(listProductWereAdded.length).fill(false)\r\n  );\r\n  const [selectedSupplier, setSelectedSupplier] = useState(\r\n    Array(listProductWereAdded.length).fill(\"\")\r\n  );\r\n  const [quantities, setQuantities] = useState(\r\n    listProductWereAdded.map((product) => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\r\n  );\r\n\r\n  const [isOpen, setIsOpen] = useState(\r\n    new Array(listProductWereAdded.length).fill(false)\r\n  ); // Khởi tạo mảng isOpen\r\n  const [myTax, setMyTax] = useState(10);\r\n  useEffect(() => {\r\n    if (dataHis && dataHis.length > 0) {\r\n      const newItems = dataHis.map(initItem);\r\n      console.log(dataHis, listProductWereAdded);\r\n      if (\r\n        !listProductWereAdded.some((item) =>\r\n          dataHis.some((it) => it._id === item.productId)\r\n        )\r\n      ) {\r\n        setListProductWereAdded((prevList) => [...newItems, ...prevList]);\r\n      }\r\n      setIdProductAdded([]);\r\n    }\r\n  }, [dataHis]);\r\n  const handleSupplierChange = (supplier, index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newList = [...prev];\r\n      newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\r\n      return newList;\r\n    });\r\n\r\n    // Cập nhật selectedSupplier\r\n    setSelectedSupplier((prev) => {\r\n      const newSelectedSuppliers = [...prev];\r\n      newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\r\n      return newSelectedSuppliers;\r\n    });\r\n\r\n    // Ẩn dropdown sau khi chọn\r\n    setIsDropdownOpenSupplier((prev) => {\r\n      const newDropdownState = [...prev];\r\n      newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\r\n      return newDropdownState;\r\n    });\r\n  };\r\n  const handleSupplierClick = (index) => {\r\n    setIsDropdownOpenSupplier((prev) => {\r\n      const newDropdownState = [...prev];\r\n      newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\r\n      return newDropdownState;\r\n    });\r\n  };\r\n  const amountBill = () => {\r\n    let sum = 0;\r\n    listProductWereAdded.forEach((product) => {\r\n      sum += product.price.replace(/\\./g, \"\") * product.quantity;\r\n    });\r\n    return sum;\r\n  };\r\n  const toggleDropdown = (index) => {\r\n    setIsOpen((prev) => {\r\n      const newOpen = [...prev];\r\n      newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\r\n      return newOpen;\r\n    });\r\n  };\r\n\r\n  const dropdownRef = useRef(null);\r\n  const dropdownRefSupplier = useRef(null);\r\n  const handleStatusClick = (index) => {\r\n    setDropdownOpenIndex((prev) => (prev === index ? null : index));\r\n  };\r\n\r\n  const handleStatusChange = (index, newStatus) => {\r\n    setListProductWereAdded((prev) => {\r\n      const updatedProducts = [...prev];\r\n      updatedProducts[index].status = newStatus;\r\n      setDropdownOpenIndex(null);\r\n      return updatedProducts;\r\n    });\r\n    // Ẩn dropdown sau khi chọn\r\n  };\r\n  const handleClickOutside = (event) => {\r\n    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n      setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const increase = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newQuantities = [...prev];\r\n      newQuantities[index].quantity += 1; // Tăng giá trị\r\n      return newQuantities;\r\n    });\r\n  };\r\n\r\n  const decrease = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newQuantities = [...prev];\r\n      if (newQuantities[index].quantity > 0) {\r\n        newQuantities[index].quantity -= 1; // Tăng giá trị\r\n      }\r\n      return newQuantities;\r\n    });\r\n  };\r\n\r\n  const handleRemove = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newList = [...prev];\r\n      newList.splice(index, 1); // Xoá phần tử\r\n      return newList;\r\n    });\r\n\r\n    setIsOpen((prev) => {\r\n      const newOpen = [...prev];\r\n      newOpen.splice(index, 1); // Cập nhật mảng isOpen\r\n      return newOpen;\r\n    });\r\n  };\r\n  const handleInputQuantitty = (index, e) => {\r\n    const newQuantity = e.target.value; // Lấy giá trị mới từ input\r\n    setListProductWereAdded((prev) => {\r\n      // Tạo bản sao của danh sách hiện tại\r\n      const updatedList = [...prev];\r\n      // Cập nhật số lượng sản phẩm tại chỉ số index\r\n      updatedList[index] = {\r\n        ...updatedList[index],\r\n        quantity: newQuantity,\r\n      };\r\n      return updatedList; // Trả về danh sách đã cập nhật\r\n    });\r\n  };\r\n  const handleCheckboxChange = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const updatedProducts = [...listProductWereAdded];\r\n      updatedProducts[index].email = !updatedProducts[index].email;\r\n      return updatedProducts;\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    console.log(\"baby take my hand\");\r\n    const groupBySupplier = listProductWereAdded.reduce(\r\n      (acc, item) => {\r\n        // Kiểm tra xem đã có supplier này trong nhóm chưa\r\n        if (!acc.dataForm[item.supplier]) {\r\n          acc.dataForm[item.supplier] = [];\r\n        }\r\n        acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\r\n        return acc;\r\n      },\r\n      { user: {}, dataForm: {} }\r\n    );\r\n    groupBySupplier.user = {\r\n      id: user._id,\r\n      name: user.name,\r\n      email: user.email,\r\n      ownerId: user.id_owner,\r\n      id_owner: user.id_owner,\r\n      role: user.role,\r\n    };\r\n    groupBySupplier.tax = myTax;\r\n    const url = \"http://localhost:8080/api/import/orderHistory/save\";\r\n\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(url, {\r\n        method: \"POST\", // Phương thức POST\r\n        headers: {\r\n          \"Content-Type\": \"application/json\", // Xác định kiểu dữ liệu là JSON\r\n        },\r\n        body: JSON.stringify(groupBySupplier), // Chuyển đổi dữ liệu thành chuỗi JSON\r\n      });\r\n      stopLoading();\r\n      if (response.ok) {\r\n        notify(1, \"you've completed importing goods\", \"Successfully!\");\r\n        const responseData = await response.json();\r\n        console.log(\"Dữ liệu đã được gửi thành công\", responseData);\r\n        //await apiFetchOrderHistory.current.fetchOrder(\" \")\r\n        //await apiGetHistory.current.debouncedFetchSuggestions(\" \", \"http://localhost:8080/api/import/loggingOrder/listOrder\", 1, 10);\r\n        setLoadOrder((prev) => !prev);\r\n        setLoadLog((prev) => !prev);\r\n        setIdProductAdded([]);\r\n        setListProductWereAdded([]);\r\n      } else {\r\n        notify(2, \"you don't have the role to do this\", \"Fail!\");\r\n        // Nếu có lỗi từ server\r\n        console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Lỗi kết nối:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"list-product-title\">List product </div>\r\n      <div className=\"list-product-content\">\r\n        <div className=\"list-product-detail\">\r\n          <table>\r\n            <thead>\r\n              <tr>\r\n                <th>STT</th>\r\n                <th>Ảnh Mô Tả</th>\r\n                <th>Sản Phẩm</th>\r\n                <th>Nhà Cung Cấp</th>\r\n                <th>Số Lượng</th>\r\n                <th>Thành Tiền</th>\r\n                <th>Status</th>\r\n                <th>Delete</th>\r\n                <th>Mail</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {listProductWereAdded.map((product, index) => (\r\n                <tr key={index}>\r\n                  <td>{index + 1}</td>\r\n                  <td>\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                      }}\r\n                    >\r\n                      <div\r\n                        className=\"body-container-img-description\"\r\n                        style={{ backgroundImage: `url(${product.imageUrl})` }}\r\n                      ></div>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"modal-body-product-name\">\r\n                      {product.name}\r\n                    </div>\r\n                    <div className=\"modal-body-product-description\">\r\n                      {product.description}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div style={{ position: \"relative\" }}>\r\n                      {product.supplier}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"Quantity\">\r\n                      <button\r\n                        className=\"Quantity-button\"\r\n                        onClick={() => decrease(index)}\r\n                      >\r\n                        -\r\n                      </button>\r\n                      <input\r\n                        value={listProductWereAdded[index].quantity}\r\n                        className=\"Quantity-input\"\r\n                        onChange={(e) => handleInputQuantitty(index, e)}\r\n                      />\r\n                      <button\r\n                        className=\"Quantity-button\"\r\n                        onClick={() => increase(index)}\r\n                      >\r\n                        +\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    {(\r\n                      product.price.replace(/\\./g, \"\") *\r\n                      listProductWereAdded[index].quantity\r\n                    ).toLocaleString()}{\" \"}\r\n                    VND\r\n                  </td>\r\n                  <td>\r\n                    <div\r\n                      className={`product-status ${listProductWereAdded[index].status}`}\r\n                      onClick={() => handleStatusClick(index)}\r\n                      style={{ position: \"relative\", cursor: \"pointer\" }}\r\n                    >\r\n                      {product.status}\r\n                      {dropdownOpenIndex === index && (\r\n                        <div ref={dropdownRef} className=\"dropdown\">\r\n                          <div\r\n                            className=\"dropdown-item\"\r\n                            onClick={() => handleStatusChange(index, \"pending\")}\r\n                          >\r\n                            Pending\r\n                          </div>\r\n                          <div\r\n                            className=\"dropdown-item \"\r\n                            onClick={() =>\r\n                              handleStatusChange(index, \"deliveried\")\r\n                            }\r\n                          >\r\n                            Delivered\r\n                          </div>\r\n                          <div\r\n                            className=\"dropdown-item \"\r\n                            onClick={() =>\r\n                              handleStatusChange(index, \"canceled\")\r\n                            }\r\n                          >\r\n                            Canceled\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={product.isChecked}\r\n                      onChange={() => handleRemove(index)} // Call handler on change\r\n                      id={`checkbox-${index}`}\r\n                    />\r\n                  </td>\r\n                  <td>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={listProductWereAdded[index].email}\r\n                      onChange={() => handleCheckboxChange(index)}\r\n                    />\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className=\"order-tax\">\r\n          TAX :{\" \"}\r\n          <input\r\n            type=\"text\"\r\n            style={{\r\n              borderRadius: \"8px\",\r\n              maxWidth: \"60px\",\r\n              border: \"1px solid #333\",\r\n              fontSize: \"16px\",\r\n              color: \"#333\",\r\n              textAlign: \"right\",\r\n              lineHeight: \"24px\",\r\n              paddingRight: \"8px\",\r\n            }}\r\n            value={myTax}\r\n            name=\"tax\"\r\n            onChange={(e) => {\r\n              if (/^\\d*$/.test(e.target.value)) {\r\n                setMyTax(e.target.value);\r\n              }\r\n            }}\r\n          />\r\n          <span style={{ fontSize: 16, fontWeight: 300 }}>{\"   \"}%</span>{\" \"}\r\n        </div>\r\n        <div className=\"order-tax\">\r\n          Tổng tiền:{\" \"}\r\n          <span style={{ fontSize: 16, fontWeight: 300 }}>\r\n            {((amountBill() * (myTax + 100)) / 100)\r\n              .toFixed(0)\r\n              .toString()\r\n              .replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\")}{\" \"}\r\n            VND\r\n          </span>\r\n        </div>\r\n        <div className=\"complete-order\">\r\n          <button onClick={() => handleSubmit()}>Complete</button>\r\n        </div>\r\n      </div>\r\n      {/* <Import2 /> */}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Import;\r\n"], "mappings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eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,KAAK,MAAM,0CAA0C;AAC5D,OAAO,cAAc;AACrB,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,UAAU,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChE,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGR,UAAU,CAAC,CAAC;EAClD,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IAAEyC,IAAI;IAAEC;EAAQ,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACnC,MAAMmC,WAAW,GAAG1C,MAAM,CAAC,CAAC;EAC5B,MAAM2C,aAAa,GAAG3C,MAAM,CAAC,CAAC;EAC9B,MAAM,CAAC4C,IAAI,EAAEC,OAAO,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA,MAAMmD,SAAS,GAAGA,CAAA,KAAM/B,SAAS,CAAC,IAAI,CAAC;EACvC,MAAMgC,UAAU,GAAGA,CAAA,KAAMhC,SAAS,CAAC,KAAK,CAAC;EACzC,MAAMiC,gBAAgB,GAAGA,CAAA,KAAMzB,cAAc,CAAC,IAAI,CAAC;EACnD,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM1B,cAAc,CAAC,KAAK,CAAC;EACrD,MAAM2B,gBAAgB,GAAGA,CAAA,KAAMzB,aAAa,CAAC,KAAK,CAAC;EACnD,MAAM0B,eAAe,GAAGA,CAAA,KAAM1B,aAAa,CAAC,IAAI,CAAC;EACjD5B,SAAS,CAAC,MAAM;IACd,MAAMuD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,IAAIf,OAAO,EAAE;QACb,MAAMgB,GAAG,GAAG,MAAMC,KAAK,CACrB,2EAA2ElB,IAAI,CAACmB,QAAQ,EAC1F,CAAC;QACD3C,YAAY,CAAC,CAAC;QACd,MAAM4C,OAAO,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,CAAC;QAChC5C,WAAW,CAAC,CAAC;QACbsB,UAAU,CAAEuB,IAAI,IAAK;UACnB,IAAIC,OAAO;UACX,IAAID,IAAI,EAAEC,OAAO,GAAG,CAAC,GAAGD,IAAI,EAAE,GAAGF,OAAO,CAAC,CAAC,KACrCG,OAAO,GAAG,CAAC,GAAGH,OAAO,CAAC;UAC3B,OAAOG,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;MACpB;IACF,CAAC;IACDR,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACf,OAAO,EAAED,IAAI,CAAC,CAAC;EAEnB,MAAM2B,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC/B,IAAIC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,CAAC;IACzBpD,aAAa,CAACgD,IAAI,CAAC;IACnB,IAAIG,OAAO,CAACE,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9BF,OAAO,GAAGA,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;MAClCxC,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIuC,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACtBC,yBAAyB,CACvBL,OAAO,EACP,kDACF,CAAC;MACH,CAAC,MAAM;QACLzC,cAAc,CAAC,EAAE,CAAC;QAClBR,UAAU,CAAC,EAAE,CAAC;MAChB;IACF,CAAC,MAAM;MACLU,YAAY,CAAC,IAAI,CAAC;MAClB,IAAIuC,OAAO,EAAE;QACX,IACE,CAAClC,OAAO,CAACwC,IAAI,CAAEC,CAAC,IACdA,CAAC,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,OAAO,CAACS,WAAW,CACnD,CAAC,EACD;UACAJ,yBAAyB,CACvBL,OAAO,EACP,uDACF,CAAC;QACH;QACAjD,UAAU,CAAC,EAAE,CAAC;QACdQ,cAAc,CAAC,EAAE,CAAC;QAClB,MAAMoD,UAAU,GAAG7C,OAAO,CACvB8C,MAAM,CAAEC,IAAI,IACXA,IAAI,CAACL,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,OAAO,CAACS,WAAW,CAAC,CAAC,CACxD,CAAC,CACAK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACd/D,UAAU,CAAC4D,UAAU,CAACI,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAAC;QACzCjD,cAAc,CAACoD,UAAU,CAAC;MAC5B,CAAC,MAAM;QACLpD,cAAc,CAAC,EAAE,CAAC;QAClBR,UAAU,CAAC,EAAE,CAAC;MAChB;IACF;EACF,CAAC;EACD,MAAMiE,uBAAuB,GAAG,MAAAA,CAAOhB,OAAO,EAAEiB,QAAQ,KAAK;IAC3D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvF,KAAK,CAACwF,GAAG,CAACF,QAAQ,EAAE;QACzCG,MAAM,EAAE;UACNC,KAAK,EAAErB,OAAO;UACdsB,OAAO,EAAEtD,IAAI,CAACmB;QAChB;MACF,CAAC,CAAC;MACF,MAAMoC,IAAI,GAAGL,QAAQ,CAACM,IAAI,CAACT,GAAG,CAAEU,CAAC,IAAKA,CAAC,CAACjB,IAAI,CAAC;MAC7CzC,UAAU,CAAEuB,IAAI,IAAK;QACnB,MAAMoC,WAAW,GAAG,IAAIC,GAAG,CAACrC,IAAI,CAACyB,GAAG,CAAEF,IAAI,IAAKA,IAAI,CAACe,GAAG,CAAC,CAAC;QACzD,MAAMrC,OAAO,GAAG2B,QAAQ,CAACM,IAAI,CAACZ,MAAM,CACjCC,IAAI,IAAK,CAACa,WAAW,CAACG,GAAG,CAAChB,IAAI,CAACe,GAAG,CACrC,CAAC;QACD,OAAO,CAAC,GAAGtC,IAAI,EAAE,GAAGC,OAAO,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EACD,MAAMW,yBAAyB,GAAG3E,WAAW,CAC3CE,QAAQ,CACN,CAACoE,OAAO,EAAEiB,QAAQ,KAAKD,uBAAuB,CAAChB,OAAO,EAAEiB,QAAQ,CAAC,EACjE,GACF,CAAC,EACD,CAACjD,IAAI,EAAEC,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAM6D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMC,KAAK,GAAGzE,WAAW,CAACsD,MAAM,CAAEW,IAAI,IAAKA,IAAI,CAACf,IAAI,IAAI5D,UAAU,CAAC;IACnE,MAAMoF,WAAW,GAAGD,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IAC3C,IAAI;MACF,IAAIC,WAAW,EAAE;QACf,IAAId,QAAQ;QACZ,IAAI,CAAC1D,SAAS,EAAE;UACd0D,QAAQ,GAAG,MAAMhC,KAAK,CACpB,kEAAkE8C,WAAW,CAACJ,GAAG,YAAY5D,IAAI,CAACmB,QAAQ,EAAE,EAC5G;YACE8C,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CACF,CAAC;QACH;QAEA,IAAI,CAAC1E,SAAS,IAAI0D,QAAQ,CAACiB,EAAE,EAAE;UAC7B,MAAMX,IAAI,GAAG,MAAMN,QAAQ,CAAC7B,IAAI,CAAC,CAAC;UAClC1B,iBAAiB,CAAC6D,IAAI,CAAC;UACvB3E,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,MAAM,IAAIS,SAAS,EAAE;UACpBG,iBAAiB,CAACoE,KAAK,CAAC;UACxBlF,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,MAAM;UACL0C,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC;QACxC;QACAnC,cAAc,CAAC,EAAE,CAAC;MACpB;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EACD,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvBC,UAAU,CAAC,MAAM;MACfpF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACD,MAAMqF,oBAAoB,GAAIC,MAAM,IAAK;IACvC1F,aAAa,CAAC0F,MAAM,CAAC;IACrBtF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAAmG,QAAA,gBACErG,OAAA,CAACf,eAAe;MACdqH,aAAa,EAAE/D,SAAU;MACzBgE,SAAS,EAAE9D,gBAAiB;MAC5BG,eAAe,EAAEA,eAAgB;MACjClB,UAAU,EAAEA,UAAW;MACvB8E,QAAQ,EAAEzE,WAAY;MACtBG,OAAO,EAAEA,OAAQ;MACjBG,SAAS,EAAEA,SAAU;MACrBD,UAAU,EAAEA,UAAW;MACvBE,YAAY,EAAEA;IAAa;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEF5G,OAAA,CAACN,KAAK;MAACa,MAAM,EAAEA,MAAO;MAACsG,OAAO,EAAErE,UAAW;MAAA6D,QAAA,gBACzCrG,OAAA;QAAK8G,SAAS,EAAC,aAAa;QAAAT,QAAA,EAAC;MAAyB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D5G,OAAA;QAAK8G,SAAS,EAAC;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9B5G,OAAA;QAAK8G,SAAS,EAAC,cAAc;QAAAT,QAAA,gBAC3BrG,OAAA;UAAK8G,SAAS,EAAC,kBAAkB;UAAAT,QAAA,eAC/BrG,OAAA;YAAK+G,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAG,CAAE;YAAAb,QAAA,gBACvDrG,OAAA;cAAM+G,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEG,UAAU,EAAE;cAAO,CAAE;cAAAd,QAAA,GAAC,mBAC5C,EAAC,GAAG;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP5G,OAAA;cAAK8G,SAAS,EAAC,yBAAyB;cAAAT,QAAA,gBACtCrG,OAAA;gBACEoH,IAAI,EAAC,MAAM;gBACXL,KAAK,EAAE;kBAAEE,IAAI,EAAE;gBAAE,CAAE;gBACnBH,SAAS,EAAC,mBAAmB;gBAC7BO,WAAW,EAAC,gCAAgC;gBAC5CzD,KAAK,EAAEnD,UAAW;gBAClB6G,QAAQ,EAAE9D,YAAa;gBACvB+D,MAAM,EAAEtB,UAAW,CAAC;gBAAA;gBACpBuB,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC,IAAI,CAAE,CAAC;cAAA;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,EACD/F,YAAY,IAAIF,OAAO,CAACsD,MAAM,GAAG,CAAC,iBACjCjE,OAAA;gBAAI8G,SAAS,EAAC,UAAU;gBAAAT,QAAA,EACrB1F,OAAO,CAACiE,GAAG,CAAC,CAACwB,MAAM,EAAEqB,KAAK,kBACzBzH,OAAA;kBAEE8G,SAAS,EAAC,aAAa;kBACvBY,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAACC,MAAM,CAAE;kBAAAC,QAAA,gBAE5CrG,OAAA;oBAAK8G,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,GACnCD,MAAM,EACN/E,SAAS,IAAIF,WAAW,CAAC8C,MAAM,GAAG,CAAC,iBAClCjE,OAAA;sBACE8G,SAAS,EAAC,sBAAsB;sBAChCC,KAAK,EAAE;wBACLY,eAAe,EAAE,OACfxG,WAAW,CAACsG,KAAK,CAAC,CAACG,KAAK,GACpBzG,WAAW,CAACsG,KAAK,CAAC,CAACG,KAAK,CAACC,UAAU,GACnC,+KAA+K;sBAEvL;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN5G,OAAA;oBACE8G,SAAS,EAAC,QAAQ;oBAClBC,KAAK,EAAE;sBAAEe,MAAM,EAAE,WAAW;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA,GAtBFa,KAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuBR,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5G,OAAA;UAAQ8G,SAAS,EAAC,eAAe;UAACY,OAAO,EAAE/B,gBAAiB;UAAAU,QAAA,EAAC;QAE7D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5G,OAAA;QAAK8G,SAAS,EAAC,YAAY;QAAAT,QAAA,eACzBrG,OAAA,CAACgI,YAAY;UACXC,OAAO,EAAE1G,cAAe;UACxBC,iBAAiB,EAAEA,iBAAkB;UACrC0G,oBAAoB,EAAEnG,WAAY;UAClCC,aAAa,EAAEA,aAAc;UAC7BM,YAAY,EAAEA,YAAa;UAC3BF,UAAU,EAAEA;QAAW;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACR5G,OAAA,CAACd,YAAY;MACXqB,MAAM,EAAEQ,WAAY;MACpB8F,OAAO,EAAEnE,iBAAkB;MAC3BE,eAAe,EAAEA,eAAgB;MACjClB,UAAU,EAAEA,UAAW;MACvBM,aAAa,EAAEA,aAAc;MAC7BE,OAAO,EAAEA,OAAQ;MACjBC,OAAO,EAAEA;IAAQ;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eACF5G,OAAA,CAACL,WAAW;MACVY,MAAM,EAAEU,UAAW;MACnB4F,OAAO,EAAElE,gBAAiB;MAC1BlB,OAAO,EAAEA,OAAQ;MACjBQ,IAAI,EAAEA,IAAK;MACXG,UAAU,EAAEA,UAAW;MACvBE,YAAY,EAAEA,YAAa;MAAA+D,QAAA,EAE1B;IAAG;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA,eACd,CAAC;AAEP;AAACxG,EAAA,CA/QQD,MAAM;EAAA,QACyBL,UAAU,EAYtBF,OAAO;AAAA;AAAAuI,EAAA,GAb1BhI,MAAM;AAiRf,MAAM6H,YAAY,GAAGA,CAAC;EACpBC,OAAO;EACPzG,iBAAiB;EACjB0G,oBAAoB;EACpBlG,aAAa;EACbI,UAAU;EACVE;AACF,CAAC,KAAK;EAAA8F,GAAA;EACJ,MAAMC,QAAQ,GAAI3D,IAAI,IAAK;IACzB,OAAO;MACLL,IAAI,EAAEK,IAAI,CAACL,IAAI;MACfiE,WAAW,EAAE5D,IAAI,CAAC4D,WAAW;MAC7BC,QAAQ,EAAE7D,IAAI,CAAC8D,eAAe,CAACnE,IAAI;MACnCoE,KAAK,EAAE/D,IAAI,CAACgE,aAAa,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC5CC,QAAQ,EAAElE,IAAI,CAACkD,KAAK,GAChBlD,IAAI,CAACkD,KAAK,CAACC,UAAU,GACrB,+KAA+K;MACnLgB,UAAU,EAAEnE,IAAI,CAAC8D,eAAe,CAAC/C,GAAG;MACpCqD,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAExE,IAAI,CAAC8D,eAAe,CAACQ,KAAK;MACrCG,SAAS,EAAEzE,IAAI,CAACe;IAClB,CAAC;EACH,CAAC;EACD,MAAM;IAAEpF,YAAY;IAAEC;EAAY,CAAC,GAAGR,UAAU,CAAC,CAAC;EAClD,MAAM;IAAE+B,IAAI;IAAEC;EAAQ,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACnC,MAAM,CAACwJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjK,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAMkK,QAAQ,GAAGrB,OAAO,CAACrD,GAAG,CAAEF,IAAI,IAAK2D,QAAQ,CAAC3D,IAAI,CAAC,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtK,QAAQ,CAClEuK,KAAK,CAACP,oBAAoB,CAACnF,MAAM,CAAC,CAAC2F,IAAI,CAAC,KAAK,CAC/C,CAAC;EACD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1K,QAAQ,CACtDuK,KAAK,CAACP,oBAAoB,CAACnF,MAAM,CAAC,CAAC2F,IAAI,CAAC,EAAE,CAC5C,CAAC;EACD,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAG5K,QAAQ,CAC1CgK,oBAAoB,CAACxE,GAAG,CAAEqF,OAAO,IAAKA,OAAO,CAACnB,QAAQ,CAAC,CAAC;EAC1D,CAAC;EAED,MAAM,CAACvI,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAClC,IAAIuK,KAAK,CAACP,oBAAoB,CAACnF,MAAM,CAAC,CAAC2F,IAAI,CAAC,KAAK,CACnD,CAAC,CAAC,CAAC;EACH,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAG/K,QAAQ,CAAC,EAAE,CAAC;EACtCE,SAAS,CAAC,MAAM;IACd,IAAI2I,OAAO,IAAIA,OAAO,CAAChE,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMmG,QAAQ,GAAGnC,OAAO,CAACrD,GAAG,CAACyD,QAAQ,CAAC;MACtC/E,OAAO,CAAC+G,GAAG,CAACpC,OAAO,EAAEmB,oBAAoB,CAAC;MAC1C,IACE,CAACA,oBAAoB,CAACjF,IAAI,CAAEO,IAAI,IAC9BuD,OAAO,CAAC9D,IAAI,CAAEmG,EAAE,IAAKA,EAAE,CAAC7E,GAAG,KAAKf,IAAI,CAACyE,SAAS,CAChD,CAAC,EACD;QACAE,uBAAuB,CAAEkB,QAAQ,IAAK,CAAC,GAAGH,QAAQ,EAAE,GAAGG,QAAQ,CAAC,CAAC;MACnE;MACA/I,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC,EAAE,CAACyG,OAAO,CAAC,CAAC;EACb,MAAMuC,oBAAoB,GAAGA,CAACjC,QAAQ,EAAEd,KAAK,KAAK;IAChD4B,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAMsH,OAAO,GAAG,CAAC,GAAGtH,IAAI,CAAC;MACzBsH,OAAO,CAAChD,KAAK,CAAC,CAACc,QAAQ,GAAGA,QAAQ,CAAC,CAAC;MACpC,OAAOkC,OAAO;IAChB,CAAC,CAAC;;IAEF;IACAX,mBAAmB,CAAE3G,IAAI,IAAK;MAC5B,MAAMuH,oBAAoB,GAAG,CAAC,GAAGvH,IAAI,CAAC;MACtCuH,oBAAoB,CAACjD,KAAK,CAAC,GAAGc,QAAQ,CAAC,CAAC;MACxC,OAAOmC,oBAAoB;IAC7B,CAAC,CAAC;;IAEF;IACAhB,yBAAyB,CAAEvG,IAAI,IAAK;MAClC,MAAMwH,gBAAgB,GAAG,CAAC,GAAGxH,IAAI,CAAC;MAClCwH,gBAAgB,CAAClD,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;MACjC,OAAOkD,gBAAgB;IACzB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,mBAAmB,GAAInD,KAAK,IAAK;IACrCiC,yBAAyB,CAAEvG,IAAI,IAAK;MAClC,MAAMwH,gBAAgB,GAAG,CAAC,GAAGxH,IAAI,CAAC;MAClCwH,gBAAgB,CAAClD,KAAK,CAAC,GAAG,CAACkD,gBAAgB,CAAClD,KAAK,CAAC,CAAC,CAAC;MACpD,OAAOkD,gBAAgB;IACzB,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIC,GAAG,GAAG,CAAC;IACX1B,oBAAoB,CAAC2B,OAAO,CAAEd,OAAO,IAAK;MACxCa,GAAG,IAAIb,OAAO,CAACxB,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGsB,OAAO,CAACnB,QAAQ;IAC5D,CAAC,CAAC;IACF,OAAOgC,GAAG;EACZ,CAAC;EACD,MAAME,cAAc,GAAIvD,KAAK,IAAK;IAChCjH,SAAS,CAAE2C,IAAI,IAAK;MAClB,MAAM8H,OAAO,GAAG,CAAC,GAAG9H,IAAI,CAAC;MACzB8H,OAAO,CAACxD,KAAK,CAAC,GAAG,CAACwD,OAAO,CAACxD,KAAK,CAAC,CAAC,CAAC;MAClC,OAAOwD,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAG7L,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM8L,mBAAmB,GAAG9L,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM+L,iBAAiB,GAAI3D,KAAK,IAAK;IACnC+B,oBAAoB,CAAErG,IAAI,IAAMA,IAAI,KAAKsE,KAAK,GAAG,IAAI,GAAGA,KAAM,CAAC;EACjE,CAAC;EAED,MAAM4D,kBAAkB,GAAGA,CAAC5D,KAAK,EAAE6D,SAAS,KAAK;IAC/CjC,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAMoI,eAAe,GAAG,CAAC,GAAGpI,IAAI,CAAC;MACjCoI,eAAe,CAAC9D,KAAK,CAAC,CAACsB,MAAM,GAAGuC,SAAS;MACzC9B,oBAAoB,CAAC,IAAI,CAAC;MAC1B,OAAO+B,eAAe;IACxB,CAAC,CAAC;IACF;EACF,CAAC;EACD,MAAMC,kBAAkB,GAAI/H,KAAK,IAAK;IACpC,IAAIyH,WAAW,CAACO,OAAO,IAAI,CAACP,WAAW,CAACO,OAAO,CAACC,QAAQ,CAACjI,KAAK,CAACE,MAAM,CAAC,EAAE;MACtE6F,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAEDlK,SAAS,CAAC,MAAM;IACdqM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,QAAQ,GAAIrE,KAAK,IAAK;IAC1B4B,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAM4I,aAAa,GAAG,CAAC,GAAG5I,IAAI,CAAC;MAC/B4I,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,IAAI,CAAC,CAAC,CAAC;MACpC,OAAOiD,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAIvE,KAAK,IAAK;IAC1B4B,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAM4I,aAAa,GAAG,CAAC,GAAG5I,IAAI,CAAC;MAC/B,IAAI4I,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,GAAG,CAAC,EAAE;QACrCiD,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,IAAI,CAAC,CAAC,CAAC;MACtC;MACA,OAAOiD,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,YAAY,GAAIxE,KAAK,IAAK;IAC9B4B,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAMsH,OAAO,GAAG,CAAC,GAAGtH,IAAI,CAAC;MACzBsH,OAAO,CAACyB,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOgD,OAAO;IAChB,CAAC,CAAC;IAEFjK,SAAS,CAAE2C,IAAI,IAAK;MAClB,MAAM8H,OAAO,GAAG,CAAC,GAAG9H,IAAI,CAAC;MACzB8H,OAAO,CAACiB,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOwD,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMkB,oBAAoB,GAAGA,CAAC1E,KAAK,EAAE2E,CAAC,KAAK;IACzC,MAAMC,WAAW,GAAGD,CAAC,CAACzI,MAAM,CAACC,KAAK,CAAC,CAAC;IACpCyF,uBAAuB,CAAElG,IAAI,IAAK;MAChC;MACA,MAAMmJ,WAAW,GAAG,CAAC,GAAGnJ,IAAI,CAAC;MAC7B;MACAmJ,WAAW,CAAC7E,KAAK,CAAC,GAAG;QACnB,GAAG6E,WAAW,CAAC7E,KAAK,CAAC;QACrBqB,QAAQ,EAAEuD;MACZ,CAAC;MACD,OAAOC,WAAW,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,oBAAoB,GAAI9E,KAAK,IAAK;IACtC4B,uBAAuB,CAAElG,IAAI,IAAK;MAChC,MAAMoI,eAAe,GAAG,CAAC,GAAGnC,oBAAoB,CAAC;MACjDmC,eAAe,CAAC9D,KAAK,CAAC,CAACuB,KAAK,GAAG,CAACuC,eAAe,CAAC9D,KAAK,CAAC,CAACuB,KAAK;MAC5D,OAAOuC,eAAe;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BlJ,OAAO,CAAC+G,GAAG,CAAC,mBAAmB,CAAC;IAChC,MAAMoC,eAAe,GAAGrD,oBAAoB,CAACsD,MAAM,CACjD,CAACC,GAAG,EAAEjI,IAAI,KAAK;MACb;MACA,IAAI,CAACiI,GAAG,CAACC,QAAQ,CAAClI,IAAI,CAAC6D,QAAQ,CAAC,EAAE;QAChCoE,GAAG,CAACC,QAAQ,CAAClI,IAAI,CAAC6D,QAAQ,CAAC,GAAG,EAAE;MAClC;MACAoE,GAAG,CAACC,QAAQ,CAAClI,IAAI,CAAC6D,QAAQ,CAAC,CAACsE,IAAI,CAACnI,IAAI,CAAC,CAAC,CAAC;MACxC,OAAOiI,GAAG;IACZ,CAAC,EACD;MAAE9K,IAAI,EAAE,CAAC,CAAC;MAAE+K,QAAQ,EAAE,CAAC;IAAE,CAC3B,CAAC;IACDH,eAAe,CAAC5K,IAAI,GAAG;MACrBiL,EAAE,EAAEjL,IAAI,CAAC4D,GAAG;MACZpB,IAAI,EAAExC,IAAI,CAACwC,IAAI;MACf2E,KAAK,EAAEnH,IAAI,CAACmH,KAAK;MACjB7D,OAAO,EAAEtD,IAAI,CAACmB,QAAQ;MACtBA,QAAQ,EAAEnB,IAAI,CAACmB,QAAQ;MACvB+J,IAAI,EAAElL,IAAI,CAACkL;IACb,CAAC;IACDN,eAAe,CAACO,GAAG,GAAG9C,KAAK;IAC3B,MAAM+C,GAAG,GAAG,oDAAoD;IAEhE,IAAI;MACF5M,YAAY,CAAC,CAAC;MACd,MAAM0E,QAAQ,GAAG,MAAMhC,KAAK,CAACkK,GAAG,EAAE;QAChCnH,MAAM,EAAE,MAAM;QAAE;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB,CAAE;QACtC,CAAC;QACDmH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,eAAe,CAAC,CAAE;MACzC,CAAC,CAAC;MACFnM,WAAW,CAAC,CAAC;MACb,IAAIyE,QAAQ,CAACiB,EAAE,EAAE;QACfnG,MAAM,CAAC,CAAC,EAAE,kCAAkC,EAAE,eAAe,CAAC;QAC9D,MAAMwN,YAAY,GAAG,MAAMtI,QAAQ,CAAC7B,IAAI,CAAC,CAAC;QAC1CI,OAAO,CAAC+G,GAAG,CAAC,gCAAgC,EAAEgD,YAAY,CAAC;QAC3D;QACA;QACA/K,YAAY,CAAEa,IAAI,IAAK,CAACA,IAAI,CAAC;QAC7Bf,UAAU,CAAEe,IAAI,IAAK,CAACA,IAAI,CAAC;QAC3B3B,iBAAiB,CAAC,EAAE,CAAC;QACrB6H,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAC,MAAM;QACLxJ,MAAM,CAAC,CAAC,EAAE,oCAAoC,EAAE,OAAO,CAAC;QACxD;QACAyD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEwB,QAAQ,CAACuI,UAAU,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO/J,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC;EACF,CAAC;EAED,oBACEvD,OAAA,CAAAE,SAAA;IAAAmG,QAAA,gBACErG,OAAA;MAAK8G,SAAS,EAAC,oBAAoB;MAAAT,QAAA,EAAC;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvD5G,OAAA;MAAK8G,SAAS,EAAC,sBAAsB;MAAAT,QAAA,gBACnCrG,OAAA;QAAK8G,SAAS,EAAC,qBAAqB;QAAAT,QAAA,eAClCrG,OAAA;UAAAqG,QAAA,gBACErG,OAAA;YAAAqG,QAAA,eACErG,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAAqG,QAAA,EAAI;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZ5G,OAAA;gBAAAqG,QAAA,EAAI;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB5G,OAAA;gBAAAqG,QAAA,EAAI;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5G,OAAA;gBAAAqG,QAAA,EAAI;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB5G,OAAA;gBAAAqG,QAAA,EAAI;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5G,OAAA;gBAAAqG,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5G,OAAA;gBAAAqG,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf5G,OAAA;gBAAAqG,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf5G,OAAA;gBAAAqG,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR5G,OAAA;YAAAqG,QAAA,EACG+C,oBAAoB,CAACxE,GAAG,CAAC,CAACqF,OAAO,EAAExC,KAAK,kBACvCzH,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAAqG,QAAA,EAAKoB,KAAK,GAAG;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBACE+G,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfuG,cAAc,EAAE,QAAQ;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBAAAnH,QAAA,eAEFrG,OAAA;oBACE8G,SAAS,EAAC,gCAAgC;oBAC1CC,KAAK,EAAE;sBAAEY,eAAe,EAAE,OAAOsC,OAAO,CAACrB,QAAQ;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,gBACErG,OAAA;kBAAK8G,SAAS,EAAC,yBAAyB;kBAAAT,QAAA,EACrC4D,OAAO,CAAC5F;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN5G,OAAA;kBAAK8G,SAAS,EAAC,gCAAgC;kBAAAT,QAAA,EAC5C4D,OAAO,CAAC3B;gBAAW;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBAAK+G,KAAK,EAAE;oBAAE0G,QAAQ,EAAE;kBAAW,CAAE;kBAAApH,QAAA,EAClC4D,OAAO,CAAC1B;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBAAK8G,SAAS,EAAC,UAAU;kBAAAT,QAAA,gBACvBrG,OAAA;oBACE8G,SAAS,EAAC,iBAAiB;oBAC3BY,OAAO,EAAEA,CAAA,KAAMsE,QAAQ,CAACvE,KAAK,CAAE;oBAAApB,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA;oBACE4D,KAAK,EAAEwF,oBAAoB,CAAC3B,KAAK,CAAC,CAACqB,QAAS;oBAC5ChC,SAAS,EAAC,gBAAgB;oBAC1BQ,QAAQ,EAAG8E,CAAC,IAAKD,oBAAoB,CAAC1E,KAAK,EAAE2E,CAAC;kBAAE;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACF5G,OAAA;oBACE8G,SAAS,EAAC,iBAAiB;oBAC3BY,OAAO,EAAEA,CAAA,KAAMoE,QAAQ,CAACrE,KAAK,CAAE;oBAAApB,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,GACG,CACC4D,OAAO,CAACxB,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAChCS,oBAAoB,CAAC3B,KAAK,CAAC,CAACqB,QAAQ,EACpC4E,cAAc,CAAC,CAAC,EAAE,GAAG,EAAC,KAE1B;cAAA;gBAAAjH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBACE8G,SAAS,EAAE,kBAAkBsC,oBAAoB,CAAC3B,KAAK,CAAC,CAACsB,MAAM,EAAG;kBAClErB,OAAO,EAAEA,CAAA,KAAM0D,iBAAiB,CAAC3D,KAAK,CAAE;kBACxCV,KAAK,EAAE;oBAAE0G,QAAQ,EAAE,UAAU;oBAAEE,MAAM,EAAE;kBAAU,CAAE;kBAAAtH,QAAA,GAElD4D,OAAO,CAAClB,MAAM,EACdQ,iBAAiB,KAAK9B,KAAK,iBAC1BzH,OAAA;oBAAK4N,GAAG,EAAE1C,WAAY;oBAACpE,SAAS,EAAC,UAAU;oBAAAT,QAAA,gBACzCrG,OAAA;sBACE8G,SAAS,EAAC,eAAe;sBACzBY,OAAO,EAAEA,CAAA,KAAM2D,kBAAkB,CAAC5D,KAAK,EAAE,SAAS,CAAE;sBAAApB,QAAA,EACrD;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5G,OAAA;sBACE8G,SAAS,EAAC,gBAAgB;sBAC1BY,OAAO,EAAEA,CAAA,KACP2D,kBAAkB,CAAC5D,KAAK,EAAE,YAAY,CACvC;sBAAApB,QAAA,EACF;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5G,OAAA;sBACE8G,SAAS,EAAC,gBAAgB;sBAC1BY,OAAO,EAAEA,CAAA,KACP2D,kBAAkB,CAAC5D,KAAK,EAAE,UAAU,CACrC;sBAAApB,QAAA,EACF;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBACEoH,IAAI,EAAC,UAAU;kBACfyG,OAAO,EAAE5D,OAAO,CAAChB,SAAU;kBAC3B3B,QAAQ,EAAEA,CAAA,KAAM2E,YAAY,CAACxE,KAAK,CAAE,CAAC;kBAAA;kBACrCqF,EAAE,EAAE,YAAYrF,KAAK;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACL5G,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBACEoH,IAAI,EAAC,UAAU;kBACfyG,OAAO,EAAEzE,oBAAoB,CAAC3B,KAAK,CAAC,CAACuB,KAAM;kBAC3C1B,QAAQ,EAAEA,CAAA,KAAMiF,oBAAoB,CAAC9E,KAAK;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GA1GEa,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2GV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5G,OAAA;QAAK8G,SAAS,EAAC,WAAW;QAAAT,QAAA,GAAC,OACpB,EAAC,GAAG,eACTrG,OAAA;UACEoH,IAAI,EAAC,MAAM;UACXL,KAAK,EAAE;YACL+G,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE,gBAAgB;YACxBC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE,OAAO;YAClBC,UAAU,EAAE,MAAM;YAClBC,YAAY,EAAE;UAChB,CAAE;UACFzK,KAAK,EAAEsG,KAAM;UACb7F,IAAI,EAAC,KAAK;UACViD,QAAQ,EAAG8E,CAAC,IAAK;YACf,IAAI,OAAO,CAACkC,IAAI,CAAClC,CAAC,CAACzI,MAAM,CAACC,KAAK,CAAC,EAAE;cAChCuG,QAAQ,CAACiC,CAAC,CAACzI,MAAM,CAACC,KAAK,CAAC;YAC1B;UACF;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF5G,OAAA;UAAM+G,KAAK,EAAE;YAAEkH,QAAQ,EAAE,EAAE;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAAlI,QAAA,GAAE,KAAK,EAAC,GAAC;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAC,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACN5G,OAAA;QAAK8G,SAAS,EAAC,WAAW;QAAAT,QAAA,GAAC,sBACf,EAAC,GAAG,eACdrG,OAAA;UAAM+G,KAAK,EAAE;YAAEkH,QAAQ,EAAE,EAAE;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAAlI,QAAA,GAC5C,CAAEwE,UAAU,CAAC,CAAC,IAAIX,KAAK,GAAG,GAAG,CAAC,GAAI,GAAG,EACnCsE,OAAO,CAAC,CAAC,CAAC,CACVC,QAAQ,CAAC,CAAC,CACV9F,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC,EAAE,GAAG,EAAC,KAEhD;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5G,OAAA;QAAK8G,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAC7BrG,OAAA;UAAQ0H,OAAO,EAAEA,CAAA,KAAM8E,YAAY,CAAC,CAAE;UAAAnG,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eAEN,CAAC;AAEP,CAAC;AAACwB,GAAA,CA1ZIJ,YAAY;EAAA,QA0BsBlI,UAAU,EACtBF,OAAO;AAAA;AAAA8O,GAAA,GA3B7B1G,YAAY;AA4ZlB,eAAe7H,MAAM;AAAC,IAAAgI,EAAA,EAAAuG,GAAA;AAAAC,YAAA,CAAAxG,EAAA;AAAAwG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}