{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\Manage_product\\\\ProductForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport \"./ProductForm.css\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport { useLoading } from \"../introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductForm = ({\n  turnoff,\n  refresh,\n  profile\n}) => {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const CLOUD_NAME = \"ddgrjo6jr\";\n  const UPLOAD_PRESET = \"my-app\";\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [error, setError] = useState(\"\");\n  const [details, setDetails] = useState(\"\");\n  const [showCamera, setShowCamera] = useState(false);\n  const [image, setImage] = useState(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const streamRef = useRef(null);\n  const [suppliers, setSuppliers] = useState([]); // state for suppliers list\n  // Bắt đầu hiển thị video từ camera\n  const scrollableRef = useRef(null);\n  const scrollToTop = () => {\n    if (scrollableRef.current) {\n      scrollableRef.current.scrollTo({\n        top: 0,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const startCamera = async () => {\n    setShowCamera(true);\n    scrollToTop();\n    streamRef.current = await navigator.mediaDevices.getUserMedia({\n      video: true\n    });\n    videoRef.current.srcObject = streamRef.current;\n  };\n  useEffect(() => {\n    const fetchSuppliers = async () => {\n      let body = {\n        user: user\n      };\n      try {\n        let response = await fetch(\"http://localhost:8080/api/products/get_supplier\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(body)\n        });\n        const data = await response.json();\n        console.log(data.suppliers);\n        setSuppliers(data.suppliers);\n      } catch (error) {\n        console.error(\"Error fetching suppliers:\", error);\n      }\n    };\n    fetchSuppliers();\n  }, []);\n  // Chụp ảnh từ video\n  const captureImage = () => {\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext(\"2d\");\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n    const imageUrl = canvas.toDataURL(\"image/png\");\n    setImage(imageUrl);\n    if (streamRef.current) {\n      const tracks = streamRef.current.getTracks();\n      tracks.forEach(track => track.stop()); // Dừng từng track trong stream\n      videoRef.current.srcObject = null; // Gán srcObject về null\n      streamRef.current = null; // Đặt lại tham chiếu stream\n    }\n    setShowCamera(false); // Đóng camera sau khi chụp\n    // Tạo một file blob từ imageUrl và đặt vào input file\n    fetch(imageUrl).then(res => res.blob()).then(blob => {\n      const file = new File([blob], \"capture.png\", {\n        type: \"image/png\"\n      });\n      const dataTransfer = new DataTransfer();\n      dataTransfer.items.add(file);\n      fileInputRef.current.files = dataTransfer.files;\n      console.log(file);\n      setFormData(prevData => ({\n        ...prevData,\n        image: file // Lưu trữ file vào state\n      }));\n    });\n  };\n  const [formData, setFormData] = useState({\n    name: \"\",\n    category: \"\",\n    brand: \"\",\n    description: \"\",\n    sku: \"\",\n    price: \"\",\n    stock_in_shelf: 0,\n    reorderLevel: 10,\n    supplier: \"\",\n    purchaseDate: \"\",\n    location: \"\",\n    stock_in_Warehouse: 0,\n    unit: \"pcs\",\n    purchasePrice: \"\",\n    notes: \"\",\n    image: \"\"\n  });\n  const handleChange = e => {\n    setError(\"\");\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Xóa dấu phân tách cũ và chuyển thành số\n    const numericValue = Number(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\n\n    // Định dạng lại nếu là số hợp lệ\n    const formattedValue = !Number.isNaN(numericValue) ? numericValue.toLocaleString(\"vn-VI\") : value;\n\n    // Cập nhật formData với giá trị đã chuyển đổi\n    setFormData({\n      ...formData,\n      [name]: typeof formattedValue === \"string\" ? formattedValue.toLowerCase().replace(/,/g, \".\") : value.replace(/,/g, \".\")\n    });\n  };\n  const handleChange_link = e => {\n    setError(\"\");\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n    fileInputRef.current.value = \"\";\n    setImage(value);\n  };\n  const handleChangeimage = e => {\n    setFormData({\n      ...formData,\n      image: e.target.files[0]\n    });\n    const file = e.target.files[0];\n    if (file) {\n      const imageUrl = URL.createObjectURL(file);\n      setImage(imageUrl);\n    }\n  };\n  const handleChangedetails = e => {\n    const {\n      value\n    } = e.target;\n    setDetails(value);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (formData.stock_in_shelf < 0 || formData.reorderLevel < 0 || formData.stock_in_Warehouse < 0) {\n      notify(2, \"Các trường số phải lớn hơn hoặc bằng 0.\", \"Lỗi\");\n      return;\n    }\n\n    // Kiểm tra các trường price và purchasePrice phải là chuỗi số hợp lệ\n    const isNumeric = value => /^\\d+(\\.\\d+)?$/.test(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\n    if (!isNumeric(formData.price) || !isNumeric(formData.purchasePrice) || formData.price < 0 || formData.purchasePrice < 0) {\n      notify(2, \"Giá bán và giá nhập phải là chuỗi số hợp lệ và lớn hơn hoặc bằng 0.\", \"Lỗi\");\n      return;\n    }\n    if (!formData.supplier && !profile) {\n      notify(3, 'Vui lòng chọn nhà cung cấp.Nếu không có nhà cung cấp bạn phải vào \"Nhà cung cấp\" để thêm', \"Cảnh báo\");\n      return;\n    }\n    if (profile && !formData.image) {\n      notify(2, \"Vui lòng thêm ảnh\", \"Lỗi\");\n      return;\n    }\n    let body = {\n      user: user,\n      newPr: {\n        ...formData\n      },\n      detail: details\n    };\n    startLoading();\n    if (formData.image) {\n      const imageData = new FormData();\n      imageData.append(\"file\", formData.image);\n      imageData.append(\"upload_preset\", UPLOAD_PRESET);\n      try {\n        const cloudinaryResponse = await fetch(`https://api.cloudinary.com/v1_1/${CLOUD_NAME}/upload`, {\n          method: \"POST\",\n          body: imageData // Gửi FormData trực tiếp mà không cần JSON.stringify\n        });\n        const data = await cloudinaryResponse.json();\n        const secure_url = data.secure_url;\n        const public_id = data.public_id;\n        // Chuẩn bị dữ liệu sản phẩm để gửi lên backend\n        body = {\n          user: user,\n          // Giả sử user có thuộc tính _id\n          newPr: {\n            ...formData,\n            image: {\n              secure_url,\n              public_id\n            } // Thêm thông tin hình ảnh\n          },\n          detail: details\n        };\n        console.log(secure_url);\n      } catch (error) {\n        console.error(\"Error uploading image:\", error);\n        notify(2, \"Đã xảy ra lỗi khi tải lên hình ảnh.\", \"Thất bại\");\n      }\n    }\n    console.log(JSON.stringify(body));\n    if (!profile) {\n      fetch(\"http://localhost:8080/api/products/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      }).then(response => response.json()).then(data => {\n        stopLoading();\n        console.log(data.message);\n        if (data.message === \"Success\") {\n          turnoff();\n          notify(1, \"thêm sản phẩm thành công\", \"Thành công\");\n          refresh();\n        } else {\n          notify(2, data.message, \"Thất bại\");\n          setError(data.message);\n        }\n      }).catch(error => {\n        notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\n        console.log(\"Lỗi:\", error);\n      });\n    } else {\n      fetch(\"http://localhost:8080/api/profile/update_avatar\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      }).then(response => response.json()).then(data => {\n        stopLoading();\n        console.log(data.respond);\n        if (data.respond === \"success\") {\n          turnoff();\n          notify(1, \"thay đổi ảnh đại diện thành công\", \"Thành công\");\n          console.log(refresh);\n          refresh();\n        } else {\n          notify(2, \"một lỗi nào đó đã xảy ra\", \"Thất bại\");\n        }\n      }).catch(error => {\n        notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\n        console.log(\"Lỗi:\", error);\n      });\n    }\n  };\n  const stopCamera = () => {\n    if (streamRef.current) {\n      const tracks = streamRef.current.getTracks();\n      tracks.forEach(track => track.stop()); // Dừng từng track trong stream\n      videoRef.current.srcObject = null; // Gán srcObject về null\n      streamRef.current = null; // Đặt lại tham chiếu stream\n    }\n    setShowCamera(false); // Đóng modal hoặc ẩn camera\n  };\n  // const handleSupplierChange = (e) => {\n  //   setFormData({\n  //     ...formData,\n  //     supplier: e.target.value\n  //   });\n  // };\n  // const handleCodeChange = (e) => {\n\n  //   setFormData({\n  //     ...formData,\n  //     sku: e.target.value\n  //   });\n  // };\n  const handleNChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value.toLowerCase()\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    ref: scrollableRef,\n    style: profile ? {\n      top: \"8%\",\n      minHeight: \"450px\"\n    } : {},\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"close-button\",\n      onClick: turnoff,\n      children: \"\\xD7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), \" \", /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: !profile ? \"Product Entry Form\" : \"Upload ảnh\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [!profile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              children: \"T\\xEAn h\\xE0ng h\\xF3a *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"name\",\n              name: \"name\",\n              value: formData.name,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: \"Lo\\u1EA1i h\\xE0ng h\\xF3a *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"category\",\n              name: \"category\",\n              value: formData.category,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"brand\",\n              children: \"Th\\u01B0\\u01A1ng hi\\u1EC7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"brand\",\n              name: \"brand\",\n              value: formData.brand,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sku\",\n              children: \"M\\xE3 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"sku\",\n              name: \"sku\",\n              value: formData.sku,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              children: \"Gi\\xE1 b\\xE1n *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"price\",\n              name: \"price\",\n              value: formData.price,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"purchasePrice\",\n              children: \"Gi\\xE1 nh\\u1EADp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"purchasePrice\",\n              name: \"purchasePrice\",\n              value: formData.purchasePrice,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stock_in_shelf\",\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng tr\\xEAn k\\u1EC7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"stock_in_shelf\",\n              name: \"stock_in_shelf\",\n              value: formData.stock_in_shelf,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"reorderLevel\",\n              children: \"Th\\xF4ng b\\xE1o c\\u1EA7n nh\\u1EADp h\\xE0ng n\\u1EBFu s\\u1ED1 l\\u01B0\\u1EE3ng d\\u01B0\\u1EDBi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"reorderLevel\",\n              name: \"reorderLevel\",\n              value: formData.reorderLevel,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"supplier\",\n              children: \"Nh\\xE0 cung c\\u1EA5p\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"supplier\",\n              name: \"supplier\",\n              value: formData.supplier,\n              onChange: handleNChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Ch\\u1ECDn nh\\xE0 cung c\\u1EA5p\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: supplier._id,\n                children: supplier.name\n              }, supplier._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"purchaseDate\",\n              children: \"Ng\\xE0y nh\\u1EADp h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"purchaseDate\",\n              name: \"purchaseDate\",\n              value: formData.purchaseDate,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"location\",\n              children: \"V\\u1ECB tr\\xED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"location\",\n              name: \"location\",\n              value: formData.location,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stock_in_Warehouse\",\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng trong kho\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"stock_in_Warehouse\",\n              name: \"stock_in_Warehouse\",\n              value: formData.stock_in_Warehouse,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-row\",\n        children: [!profile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"unit\",\n              children: \"\\u0111\\u01A1n v\\u1ECB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"unit\",\n              name: \"unit\",\n              value: formData.unit,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"notes\",\n              children: \"Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"notes\",\n              name: \"notes\",\n              value: formData.notes,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"image\",\n            children: \"Image (3 c\\xE1ch \\u0111\\u1EC3 nh\\u1EADp \\u1EA3nh)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginBottom: \"3px\"\n            },\n            children: \"1. t\\u1EA3i \\u1EA3nh l\\xEAn t\\u1EEB m\\xE1y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            ref: fileInputRef,\n            name: \"image\",\n            onChange: handleChangeimage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginBottom: \"3px\",\n              marginTop: \"3px\"\n            },\n            children: \"2. link \\u1EA3nh tr\\xEAn m\\u1EA1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"image\",\n            name: \"image\",\n            value: formData.image,\n            onChange: handleChange_link\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginBottom: \"3px\",\n              marginTop: \"3px\"\n            },\n            children: \"3. ch\\u1EE5p \\u1EA3nh tr\\u1EF1c ti\\u1EBFp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"capture\",\n            onClick: startCamera,\n            children: \"Ch\\u1EE5p \\u1EA3nh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), showCamera && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"camera-modal\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"camera-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                ref: videoRef,\n                autoPlay: true,\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button-capture\",\n                onClick: captureImage,\n                children: \"Ch\\u1EE5p\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button-capture\",\n                onClick: stopCamera,\n                children: \"H\\u1EE7y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: canvasRef,\n            style: {\n              display: \"none\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), image && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\u1EA2nh :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: \"Captured\",\n              style: {\n                width: \"300px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this), !profile ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"details\",\n            children: [\"Th\\xF4ng tin chi ti\\u1EBFt v\\u1EC1 th\\xEAm s\\u1EA3n ph\\u1EA9m\", \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"details\",\n            name: \"details\",\n            value: details,\n            onChange: handleChangedetails\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 13\n        }, this) : \"\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: \"red\"\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"submit-row\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"submit-group\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"submit\",\n            value: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"LJGg1fkEoF5rbJ3q5ZkGgVlxuBI=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useAuth", "useLoading", "notify", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductForm", "turnoff", "refresh", "profile", "_s", "startLoading", "stopLoading", "CLOUD_NAME", "UPLOAD_PRESET", "user", "loading", "error", "setError", "details", "setDetails", "showCamera", "setShowCamera", "image", "setImage", "videoRef", "canvasRef", "fileInputRef", "streamRef", "suppliers", "setSuppliers", "scrollableRef", "scrollToTop", "current", "scrollTo", "top", "behavior", "startCamera", "navigator", "mediaDevices", "getUserMedia", "video", "srcObject", "fetchSuppliers", "body", "response", "fetch", "method", "headers", "JSON", "stringify", "data", "json", "console", "log", "captureImage", "canvas", "context", "getContext", "width", "videoWidth", "height", "videoHeight", "drawImage", "imageUrl", "toDataURL", "tracks", "getTracks", "for<PERSON>ach", "track", "stop", "then", "res", "blob", "file", "File", "type", "dataTransfer", "DataTransfer", "items", "add", "files", "setFormData", "prevData", "formData", "name", "category", "brand", "description", "sku", "price", "stock_in_shelf", "reorderLevel", "supplier", "purchaseDate", "location", "stock_in_Warehouse", "unit", "purchasePrice", "notes", "handleChange", "e", "value", "target", "numericValue", "Number", "replace", "formattedValue", "isNaN", "toLocaleString", "toLowerCase", "handleChange_link", "handleChangeimage", "URL", "createObjectURL", "handleChangedetails", "handleSubmit", "preventDefault", "isNumeric", "test", "newPr", "detail", "imageData", "FormData", "append", "cloudinaryResponse", "secure_url", "public_id", "message", "catch", "respond", "stopCamera", "handleNChange", "className", "ref", "style", "minHeight", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "onChange", "required", "map", "_id", "marginBottom", "marginTop", "autoPlay", "display", "src", "alt", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/Manage_product/ProductForm.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport \"./ProductForm.css\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nconst ProductForm = ({ turnoff, refresh, profile }) => {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const CLOUD_NAME = \"ddgrjo6jr\";\r\n  const UPLOAD_PRESET = \"my-app\";\r\n  const { user, loading } = useAuth();\r\n  const [error, setError] = useState(\"\");\r\n  const [details, setDetails] = useState(\"\");\r\n  const [showCamera, setShowCamera] = useState(false);\r\n  const [image, setImage] = useState(null);\r\n  const videoRef = useRef(null);\r\n  const canvasRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const streamRef = useRef(null);\r\n  const [suppliers, setSuppliers] = useState([]); // state for suppliers list\r\n  // Bắt đầu hiển thị video từ camera\r\n  const scrollableRef = useRef(null);\r\n  const scrollToTop = () => {\r\n    if (scrollableRef.current) {\r\n      scrollableRef.current.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    }\r\n  };\r\n  const startCamera = async () => {\r\n    setShowCamera(true);\r\n    scrollToTop();\r\n    streamRef.current = await navigator.mediaDevices.getUserMedia({\r\n      video: true,\r\n    });\r\n    videoRef.current.srcObject = streamRef.current;\r\n  };\r\n  useEffect(() => {\r\n    const fetchSuppliers = async () => {\r\n      let body = {\r\n        user: user,\r\n      };\r\n      try {\r\n        let response = await fetch(\r\n          \"http://localhost:8080/api/products/get_supplier\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify(body),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        console.log(data.suppliers);\r\n        setSuppliers(data.suppliers);\r\n      } catch (error) {\r\n        console.error(\"Error fetching suppliers:\", error);\r\n      }\r\n    };\r\n    fetchSuppliers();\r\n  }, []);\r\n  // Chụp ảnh từ video\r\n  const captureImage = () => {\r\n    const video = videoRef.current;\r\n    const canvas = canvasRef.current;\r\n    const context = canvas.getContext(\"2d\");\r\n    canvas.width = video.videoWidth;\r\n    canvas.height = video.videoHeight;\r\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\r\n    const imageUrl = canvas.toDataURL(\"image/png\");\r\n    setImage(imageUrl);\r\n    if (streamRef.current) {\r\n      const tracks = streamRef.current.getTracks();\r\n      tracks.forEach((track) => track.stop()); // Dừng từng track trong stream\r\n      videoRef.current.srcObject = null; // Gán srcObject về null\r\n      streamRef.current = null; // Đặt lại tham chiếu stream\r\n    }\r\n    setShowCamera(false); // Đóng camera sau khi chụp\r\n    // Tạo một file blob từ imageUrl và đặt vào input file\r\n    fetch(imageUrl)\r\n      .then((res) => res.blob())\r\n      .then((blob) => {\r\n        const file = new File([blob], \"capture.png\", { type: \"image/png\" });\r\n        const dataTransfer = new DataTransfer();\r\n        dataTransfer.items.add(file);\r\n        fileInputRef.current.files = dataTransfer.files;\r\n        console.log(file);\r\n        setFormData((prevData) => ({\r\n          ...prevData,\r\n          image: file, // Lưu trữ file vào state\r\n        }));\r\n      });\r\n  };\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    category: \"\",\r\n    brand: \"\",\r\n    description: \"\",\r\n    sku: \"\",\r\n    price: \"\",\r\n    stock_in_shelf: 0,\r\n    reorderLevel: 10,\r\n    supplier: \"\",\r\n    purchaseDate: \"\",\r\n    location: \"\",\r\n    stock_in_Warehouse: 0,\r\n    unit: \"pcs\",\r\n    purchasePrice: \"\",\r\n    notes: \"\",\r\n    image: \"\",\r\n  });\r\n\r\n  const handleChange = (e) => {\r\n    setError(\"\");\r\n    const { name, value } = e.target;\r\n\r\n    // Xóa dấu phân tách cũ và chuyển thành số\r\n    const numericValue = Number(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\r\n\r\n    // Định dạng lại nếu là số hợp lệ\r\n    const formattedValue = !Number.isNaN(numericValue)\r\n      ? numericValue.toLocaleString(\"vn-VI\")\r\n      : value;\r\n\r\n    // Cập nhật formData với giá trị đã chuyển đổi\r\n    setFormData({\r\n      ...formData,\r\n      [name]:\r\n        typeof formattedValue === \"string\"\r\n          ? formattedValue.toLowerCase().replace(/,/g, \".\")\r\n          : value.replace(/,/g, \".\"),\r\n    });\r\n  };\r\n\r\n  const handleChange_link = (e) => {\r\n    setError(\"\");\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value,\r\n    });\r\n    fileInputRef.current.value = \"\";\r\n    setImage(value);\r\n  };\r\n  const handleChangeimage = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      image: e.target.files[0],\r\n    });\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const imageUrl = URL.createObjectURL(file);\r\n      setImage(imageUrl);\r\n    }\r\n  };\r\n  const handleChangedetails = (e) => {\r\n    const { value } = e.target;\r\n    setDetails(value);\r\n  };\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (\r\n      formData.stock_in_shelf < 0 ||\r\n      formData.reorderLevel < 0 ||\r\n      formData.stock_in_Warehouse < 0\r\n    ) {\r\n      notify(2, \"Các trường số phải lớn hơn hoặc bằng 0.\", \"Lỗi\");\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra các trường price và purchasePrice phải là chuỗi số hợp lệ\r\n    const isNumeric = (value) =>\r\n      /^\\d+(\\.\\d+)?$/.test(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\r\n    if (\r\n      !isNumeric(formData.price) ||\r\n      !isNumeric(formData.purchasePrice) ||\r\n      formData.price < 0 ||\r\n      formData.purchasePrice < 0\r\n    ) {\r\n      notify(\r\n        2,\r\n        \"Giá bán và giá nhập phải là chuỗi số hợp lệ và lớn hơn hoặc bằng 0.\",\r\n        \"Lỗi\"\r\n      );\r\n      return;\r\n    }\r\n    if (!formData.supplier && !profile) {\r\n      notify(\r\n        3,\r\n        'Vui lòng chọn nhà cung cấp.Nếu không có nhà cung cấp bạn phải vào \"Nhà cung cấp\" để thêm',\r\n        \"Cảnh báo\"\r\n      );\r\n      return;\r\n    }\r\n    if (profile && !formData.image) {\r\n      notify(2, \"Vui lòng thêm ảnh\", \"Lỗi\");\r\n      return;\r\n    }\r\n    let body = {\r\n      user: user,\r\n      newPr: { ...formData },\r\n      detail: details,\r\n    };\r\n    startLoading();\r\n    if (formData.image) {\r\n      const imageData = new FormData();\r\n      imageData.append(\"file\", formData.image);\r\n      imageData.append(\"upload_preset\", UPLOAD_PRESET);\r\n      try {\r\n        const cloudinaryResponse = await fetch(\r\n          `https://api.cloudinary.com/v1_1/${CLOUD_NAME}/upload`,\r\n          {\r\n            method: \"POST\",\r\n            body: imageData, // Gửi FormData trực tiếp mà không cần JSON.stringify\r\n          }\r\n        );\r\n        const data = await cloudinaryResponse.json();\r\n        const secure_url = data.secure_url;\r\n        const public_id = data.public_id;\r\n        // Chuẩn bị dữ liệu sản phẩm để gửi lên backend\r\n        body = {\r\n          user: user, // Giả sử user có thuộc tính _id\r\n          newPr: {\r\n            ...formData,\r\n            image: { secure_url, public_id }, // Thêm thông tin hình ảnh\r\n          },\r\n          detail: details,\r\n        };\r\n        console.log(secure_url);\r\n      } catch (error) {\r\n        console.error(\"Error uploading image:\", error);\r\n        notify(2, \"Đã xảy ra lỗi khi tải lên hình ảnh.\", \"Thất bại\");\r\n      }\r\n    }\r\n    console.log(JSON.stringify(body));\r\n    if (!profile) {\r\n      fetch(\"http://localhost:8080/api/products/create\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      })\r\n        .then((response) => response.json())\r\n        .then((data) => {\r\n          stopLoading();\r\n          console.log(data.message);\r\n          if (data.message === \"Success\") {\r\n            turnoff();\r\n            notify(1, \"thêm sản phẩm thành công\", \"Thành công\");\r\n            refresh();\r\n          } else {\r\n            notify(2, data.message, \"Thất bại\");\r\n            setError(data.message);\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\r\n          console.log(\"Lỗi:\", error);\r\n        });\r\n    } else {\r\n      fetch(\"http://localhost:8080/api/profile/update_avatar\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(body),\r\n      })\r\n        .then((response) => response.json())\r\n        .then((data) => {\r\n          stopLoading();\r\n          console.log(data.respond);\r\n          if (data.respond === \"success\") {\r\n            turnoff();\r\n            notify(1, \"thay đổi ảnh đại diện thành công\", \"Thành công\");\r\n            console.log(refresh);\r\n            refresh();\r\n          } else {\r\n            notify(2, \"một lỗi nào đó đã xảy ra\", \"Thất bại\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\r\n          console.log(\"Lỗi:\", error);\r\n        });\r\n    }\r\n  };\r\n  const stopCamera = () => {\r\n    if (streamRef.current) {\r\n      const tracks = streamRef.current.getTracks();\r\n      tracks.forEach((track) => track.stop()); // Dừng từng track trong stream\r\n      videoRef.current.srcObject = null; // Gán srcObject về null\r\n      streamRef.current = null; // Đặt lại tham chiếu stream\r\n    }\r\n    setShowCamera(false); // Đóng modal hoặc ẩn camera\r\n  };\r\n  // const handleSupplierChange = (e) => {\r\n  //   setFormData({\r\n  //     ...formData,\r\n  //     supplier: e.target.value\r\n  //   });\r\n  // };\r\n  // const handleCodeChange = (e) => {\r\n\r\n  //   setFormData({\r\n  //     ...formData,\r\n  //     sku: e.target.value\r\n  //   });\r\n  // };\r\n  const handleNChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value.toLowerCase(),\r\n    });\r\n  };\r\n  return (\r\n    <div\r\n      className=\"form-container\"\r\n      ref={scrollableRef}\r\n      style={profile ? { top: \"8%\", minHeight: \"450px\" } : {}}\r\n    >\r\n      <span className=\"close-button\" onClick={turnoff}>\r\n        &times;\r\n      </span>{\" \"}\r\n      {/* Dấu X để tắt form */}\r\n      <h2>{!profile ? \"Product Entry Form\" : \"Upload ảnh\"}</h2>\r\n      <form onSubmit={handleSubmit}>\r\n        {!profile ? (\r\n          <>\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"name\">Tên hàng hóa *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"category\">Loại hàng hóa *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"category\"\r\n                  name=\"category\"\r\n                  value={formData.category}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"brand\">Thương hiệu</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"brand\"\r\n                  name=\"brand\"\r\n                  value={formData.brand}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"sku\">Mã *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"sku\"\r\n                  name=\"sku\"\r\n                  value={formData.sku}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"price\">Giá bán *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"price\"\r\n                  name=\"price\"\r\n                  value={formData.price}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"purchasePrice\">Giá nhập</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"purchasePrice\"\r\n                  name=\"purchasePrice\"\r\n                  value={formData.purchasePrice}\r\n                  onChange={handleChange}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"stock_in_shelf\">Số lượng trên kệ</label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"stock_in_shelf\"\r\n                  name=\"stock_in_shelf\"\r\n                  value={formData.stock_in_shelf}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"reorderLevel\">\r\n                  Thông báo cần nhập hàng nếu số lượng dưới:\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"reorderLevel\"\r\n                  name=\"reorderLevel\"\r\n                  value={formData.reorderLevel}\r\n                  onChange={handleChange}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"supplier\">Nhà cung cấp</label>\r\n                <select\r\n                  id=\"supplier\"\r\n                  name=\"supplier\"\r\n                  value={formData.supplier}\r\n                  onChange={handleNChange}\r\n                >\r\n                  <option value=\"\">Chọn nhà cung cấp</option>\r\n                  {suppliers.map((supplier) => (\r\n                    <option key={supplier._id} value={supplier._id}>\r\n                      {supplier.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"purchaseDate\">Ngày nhập hàng</label>\r\n                <input\r\n                  type=\"date\"\r\n                  id=\"purchaseDate\"\r\n                  name=\"purchaseDate\"\r\n                  value={formData.purchaseDate}\r\n                  onChange={handleChange}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-row\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"location\">Vị trí</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"location\"\r\n                  name=\"location\"\r\n                  value={formData.location}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"stock_in_Warehouse\">Số lượng trong kho</label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"stock_in_Warehouse\"\r\n                  name=\"stock_in_Warehouse\"\r\n                  value={formData.stock_in_Warehouse}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </>\r\n        ) : null}\r\n        <div className=\"form-row\">\r\n          {!profile ? (\r\n            <>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"unit\">đơn vị</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"unit\"\r\n                  name=\"unit\"\r\n                  value={formData.unit}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"notes\">Notes</label>\r\n                <textarea\r\n                  id=\"notes\"\r\n                  name=\"notes\"\r\n                  value={formData.notes}\r\n                  onChange={handleNChange}\r\n                ></textarea>\r\n              </div>\r\n            </>\r\n          ) : null}\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"image\">Image (3 cách để nhập ảnh)</label>\r\n            <p style={{ marginBottom: \"3px\" }}>1. tải ảnh lên từ máy</p>\r\n            <input\r\n              type=\"file\"\r\n              ref={fileInputRef}\r\n              name=\"image\"\r\n              onChange={handleChangeimage}\r\n            />\r\n            <p style={{ marginBottom: \"3px\", marginTop: \"3px\" }}>\r\n              2. link ảnh trên mạng\r\n            </p>\r\n            <input\r\n              type=\"text\"\r\n              id=\"image\"\r\n              name=\"image\"\r\n              value={formData.image}\r\n              onChange={handleChange_link}\r\n            />\r\n            <p style={{ marginBottom: \"3px\", marginTop: \"3px\" }}>\r\n              3. chụp ảnh trực tiếp\r\n            </p>\r\n            <div className=\"capture\" onClick={startCamera}>\r\n              Chụp ảnh\r\n            </div>\r\n\r\n            {/* Modal hiển thị camera */}\r\n            {showCamera && (\r\n              <div className=\"camera-modal\">\r\n                <div className=\"camera-container\">\r\n                  <video ref={videoRef} autoPlay style={{ width: \"100%\" }} />\r\n                  <button className=\"button-capture\" onClick={captureImage}>\r\n                    Chụp\r\n                  </button>\r\n                  <button className=\"button-capture\" onClick={stopCamera}>\r\n                    Hủy\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n\r\n            {image && (\r\n              <div>\r\n                <h3>Ảnh :</h3>\r\n                <img src={image} alt=\"Captured\" style={{ width: \"300px\" }} />\r\n              </div>\r\n            )}\r\n          </div>\r\n          {!profile ? (\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"details\">\r\n                Thông tin chi tiết về thêm sản phẩm{\" \"}\r\n              </label>\r\n              <textarea\r\n                id=\"details\"\r\n                name=\"details\"\r\n                value={details}\r\n                onChange={handleChangedetails}\r\n              ></textarea>\r\n            </div>\r\n          ) : (\r\n            \"\"\r\n          )}\r\n        </div>\r\n        <p style={{ color: \"red\" }}>{error}</p>\r\n        <div className=\"submit-row\">\r\n          <div className=\"submit-group\">\r\n            <input type=\"submit\" value=\"Submit\" />\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACpE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGZ,UAAU,CAAC,CAAC;EAClD,MAAMa,UAAU,GAAG,WAAW;EAC9B,MAAMC,aAAa,GAAG,QAAQ;EAC9B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGjB,OAAO,CAAC,CAAC;EACnC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM6B,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM6B,SAAS,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM8B,YAAY,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+B,SAAS,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD;EACA,MAAMmC,aAAa,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAID,aAAa,CAACE,OAAO,EAAE;MACzBF,aAAa,CAACE,OAAO,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChE;EACF,CAAC;EACD,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9Bf,aAAa,CAAC,IAAI,CAAC;IACnBU,WAAW,CAAC,CAAC;IACbJ,SAAS,CAACK,OAAO,GAAG,MAAMK,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;MAC5DC,KAAK,EAAE;IACT,CAAC,CAAC;IACFhB,QAAQ,CAACQ,OAAO,CAACS,SAAS,GAAGd,SAAS,CAACK,OAAO;EAChD,CAAC;EACDnC,SAAS,CAAC,MAAM;IACd,MAAM6C,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIC,IAAI,GAAG;QACT7B,IAAI,EAAEA;MACR,CAAC;MACD,IAAI;QACF,IAAI8B,QAAQ,GAAG,MAAMC,KAAK,CACxB,iDAAiD,EACjD;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;QAC3B,CACF,CAAC;QACD,MAAMO,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAACH,IAAI,CAACtB,SAAS,CAAC;QAC3BC,YAAY,CAACqB,IAAI,CAACtB,SAAS,CAAC;MAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdoC,OAAO,CAACpC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IACD0B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMd,KAAK,GAAGhB,QAAQ,CAACQ,OAAO;IAC9B,MAAMuB,MAAM,GAAG9B,SAAS,CAACO,OAAO;IAChC,MAAMwB,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACvCF,MAAM,CAACG,KAAK,GAAGlB,KAAK,CAACmB,UAAU;IAC/BJ,MAAM,CAACK,MAAM,GAAGpB,KAAK,CAACqB,WAAW;IACjCL,OAAO,CAACM,SAAS,CAACtB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEe,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;IAC3D,MAAMG,QAAQ,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;IAC9CzC,QAAQ,CAACwC,QAAQ,CAAC;IAClB,IAAIpC,SAAS,CAACK,OAAO,EAAE;MACrB,MAAMiC,MAAM,GAAGtC,SAAS,CAACK,OAAO,CAACkC,SAAS,CAAC,CAAC;MAC5CD,MAAM,CAACE,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC7C,QAAQ,CAACQ,OAAO,CAACS,SAAS,GAAG,IAAI,CAAC,CAAC;MACnCd,SAAS,CAACK,OAAO,GAAG,IAAI,CAAC,CAAC;IAC5B;IACAX,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtB;IACAwB,KAAK,CAACkB,QAAQ,CAAC,CACZO,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEE,IAAI,IAAK;MACd,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,aAAa,EAAE;QAAEG,IAAI,EAAE;MAAY,CAAC,CAAC;MACnE,MAAMC,YAAY,GAAG,IAAIC,YAAY,CAAC,CAAC;MACvCD,YAAY,CAACE,KAAK,CAACC,GAAG,CAACN,IAAI,CAAC;MAC5B/C,YAAY,CAACM,OAAO,CAACgD,KAAK,GAAGJ,YAAY,CAACI,KAAK;MAC/C5B,OAAO,CAACC,GAAG,CAACoB,IAAI,CAAC;MACjBQ,WAAW,CAAEC,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACX5D,KAAK,EAAEmD,IAAI,CAAE;MACf,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAED,MAAM,CAACU,QAAQ,EAAEF,WAAW,CAAC,GAAGtF,QAAQ,CAAC;IACvCyF,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,kBAAkB,EAAE,CAAC;IACrBC,IAAI,EAAE,KAAK;IACXC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACT5E,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM6E,YAAY,GAAIC,CAAC,IAAK;IAC1BnF,QAAQ,CAAC,EAAE,CAAC;IACZ,MAAM;MAAEmE,IAAI;MAAEiB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;;IAEhC;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACH,KAAK,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;IAEvE;IACA,MAAMC,cAAc,GAAG,CAACF,MAAM,CAACG,KAAK,CAACJ,YAAY,CAAC,GAC9CA,YAAY,CAACK,cAAc,CAAC,OAAO,CAAC,GACpCP,KAAK;;IAET;IACApB,WAAW,CAAC;MACV,GAAGE,QAAQ;MACX,CAACC,IAAI,GACH,OAAOsB,cAAc,KAAK,QAAQ,GAC9BA,cAAc,CAACG,WAAW,CAAC,CAAC,CAACJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAC/CJ,KAAK,CAACI,OAAO,CAAC,IAAI,EAAE,GAAG;IAC/B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,iBAAiB,GAAIV,CAAC,IAAK;IAC/BnF,QAAQ,CAAC,EAAE,CAAC;IACZ,MAAM;MAAEmE,IAAI;MAAEiB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAAC;MACV,GAAGE,QAAQ;MACX,CAACC,IAAI,GAAGiB;IACV,CAAC,CAAC;IACF3E,YAAY,CAACM,OAAO,CAACqE,KAAK,GAAG,EAAE;IAC/B9E,QAAQ,CAAC8E,KAAK,CAAC;EACjB,CAAC;EACD,MAAMU,iBAAiB,GAAIX,CAAC,IAAK;IAC/BnB,WAAW,CAAC;MACV,GAAGE,QAAQ;MACX7D,KAAK,EAAE8E,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,MAAMP,IAAI,GAAG2B,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIP,IAAI,EAAE;MACR,MAAMV,QAAQ,GAAGiD,GAAG,CAACC,eAAe,CAACxC,IAAI,CAAC;MAC1ClD,QAAQ,CAACwC,QAAQ,CAAC;IACpB;EACF,CAAC;EACD,MAAMmD,mBAAmB,GAAId,CAAC,IAAK;IACjC,MAAM;MAAEC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAC1BnF,UAAU,CAACkF,KAAK,CAAC;EACnB,CAAC;EACD,MAAMc,YAAY,GAAG,MAAOf,CAAC,IAAK;IAChCA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAClB,IACEjC,QAAQ,CAACO,cAAc,GAAG,CAAC,IAC3BP,QAAQ,CAACQ,YAAY,GAAG,CAAC,IACzBR,QAAQ,CAACY,kBAAkB,GAAG,CAAC,EAC/B;MACA/F,MAAM,CAAC,CAAC,EAAE,yCAAyC,EAAE,KAAK,CAAC;MAC3D;IACF;;IAEA;IACA,MAAMqH,SAAS,GAAIhB,KAAK,IACtB,eAAe,CAACiB,IAAI,CAACjB,KAAK,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAClE,IACE,CAACY,SAAS,CAAClC,QAAQ,CAACM,KAAK,CAAC,IAC1B,CAAC4B,SAAS,CAAClC,QAAQ,CAACc,aAAa,CAAC,IAClCd,QAAQ,CAACM,KAAK,GAAG,CAAC,IAClBN,QAAQ,CAACc,aAAa,GAAG,CAAC,EAC1B;MACAjG,MAAM,CACJ,CAAC,EACD,qEAAqE,EACrE,KACF,CAAC;MACD;IACF;IACA,IAAI,CAACmF,QAAQ,CAACS,QAAQ,IAAI,CAACpF,OAAO,EAAE;MAClCR,MAAM,CACJ,CAAC,EACD,0FAA0F,EAC1F,UACF,CAAC;MACD;IACF;IACA,IAAIQ,OAAO,IAAI,CAAC2E,QAAQ,CAAC7D,KAAK,EAAE;MAC9BtB,MAAM,CAAC,CAAC,EAAE,mBAAmB,EAAE,KAAK,CAAC;MACrC;IACF;IACA,IAAI2C,IAAI,GAAG;MACT7B,IAAI,EAAEA,IAAI;MACVyG,KAAK,EAAE;QAAE,GAAGpC;MAAS,CAAC;MACtBqC,MAAM,EAAEtG;IACV,CAAC;IACDR,YAAY,CAAC,CAAC;IACd,IAAIyE,QAAQ,CAAC7D,KAAK,EAAE;MAClB,MAAMmG,SAAS,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAChCD,SAAS,CAACE,MAAM,CAAC,MAAM,EAAExC,QAAQ,CAAC7D,KAAK,CAAC;MACxCmG,SAAS,CAACE,MAAM,CAAC,eAAe,EAAE9G,aAAa,CAAC;MAChD,IAAI;QACF,MAAM+G,kBAAkB,GAAG,MAAM/E,KAAK,CACpC,mCAAmCjC,UAAU,SAAS,EACtD;UACEkC,MAAM,EAAE,MAAM;UACdH,IAAI,EAAE8E,SAAS,CAAE;QACnB,CACF,CAAC;QACD,MAAMvE,IAAI,GAAG,MAAM0E,kBAAkB,CAACzE,IAAI,CAAC,CAAC;QAC5C,MAAM0E,UAAU,GAAG3E,IAAI,CAAC2E,UAAU;QAClC,MAAMC,SAAS,GAAG5E,IAAI,CAAC4E,SAAS;QAChC;QACAnF,IAAI,GAAG;UACL7B,IAAI,EAAEA,IAAI;UAAE;UACZyG,KAAK,EAAE;YACL,GAAGpC,QAAQ;YACX7D,KAAK,EAAE;cAAEuG,UAAU;cAAEC;YAAU,CAAC,CAAE;UACpC,CAAC;UACDN,MAAM,EAAEtG;QACV,CAAC;QACDkC,OAAO,CAACC,GAAG,CAACwE,UAAU,CAAC;MACzB,CAAC,CAAC,OAAO7G,KAAK,EAAE;QACdoC,OAAO,CAACpC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9ChB,MAAM,CAAC,CAAC,EAAE,qCAAqC,EAAE,UAAU,CAAC;MAC9D;IACF;IACAoD,OAAO,CAACC,GAAG,CAACL,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;IACjC,IAAI,CAACnC,OAAO,EAAE;MACZqC,KAAK,CAAC,2CAA2C,EAAE;QACjDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC,CACC2B,IAAI,CAAE1B,QAAQ,IAAKA,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC,CACnCmB,IAAI,CAAEpB,IAAI,IAAK;QACdvC,WAAW,CAAC,CAAC;QACbyC,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC6E,OAAO,CAAC;QACzB,IAAI7E,IAAI,CAAC6E,OAAO,KAAK,SAAS,EAAE;UAC9BzH,OAAO,CAAC,CAAC;UACTN,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDO,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACLP,MAAM,CAAC,CAAC,EAAEkD,IAAI,CAAC6E,OAAO,EAAE,UAAU,CAAC;UACnC9G,QAAQ,CAACiC,IAAI,CAAC6E,OAAO,CAAC;QACxB;MACF,CAAC,CAAC,CACDC,KAAK,CAAEhH,KAAK,IAAK;QAChBhB,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;QAC/CoD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAErC,KAAK,CAAC;MAC5B,CAAC,CAAC;IACN,CAAC,MAAM;MACL6B,KAAK,CAAC,iDAAiD,EAAE;QACvDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC,CACC2B,IAAI,CAAE1B,QAAQ,IAAKA,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC,CACnCmB,IAAI,CAAEpB,IAAI,IAAK;QACdvC,WAAW,CAAC,CAAC;QACbyC,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC+E,OAAO,CAAC;QACzB,IAAI/E,IAAI,CAAC+E,OAAO,KAAK,SAAS,EAAE;UAC9B3H,OAAO,CAAC,CAAC;UACTN,MAAM,CAAC,CAAC,EAAE,kCAAkC,EAAE,YAAY,CAAC;UAC3DoD,OAAO,CAACC,GAAG,CAAC9C,OAAO,CAAC;UACpBA,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACLP,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,UAAU,CAAC;QACnD;MACF,CAAC,CAAC,CACDgI,KAAK,CAAEhH,KAAK,IAAK;QAChBhB,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;QAC/CoD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAErC,KAAK,CAAC;MAC5B,CAAC,CAAC;IACN;EACF,CAAC;EACD,MAAMkH,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIvG,SAAS,CAACK,OAAO,EAAE;MACrB,MAAMiC,MAAM,GAAGtC,SAAS,CAACK,OAAO,CAACkC,SAAS,CAAC,CAAC;MAC5CD,MAAM,CAACE,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC7C,QAAQ,CAACQ,OAAO,CAACS,SAAS,GAAG,IAAI,CAAC,CAAC;MACnCd,SAAS,CAACK,OAAO,GAAG,IAAI,CAAC,CAAC;IAC5B;IACAX,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAM8G,aAAa,GAAI/B,CAAC,IAAK;IAC3B,MAAM;MAAEhB,IAAI;MAAEiB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAAC;MACV,GAAGE,QAAQ;MACX,CAACC,IAAI,GAAGiB,KAAK,CAACQ,WAAW,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;EACD,oBACE3G,OAAA;IACEkI,SAAS,EAAC,gBAAgB;IAC1BC,GAAG,EAAEvG,aAAc;IACnBwG,KAAK,EAAE9H,OAAO,GAAG;MAAE0B,GAAG,EAAE,IAAI;MAAEqG,SAAS,EAAE;IAAQ,CAAC,GAAG,CAAC,CAAE;IAAAC,QAAA,gBAExDtI,OAAA;MAAMkI,SAAS,EAAC,cAAc;MAACK,OAAO,EAAEnI,OAAQ;MAAAkI,QAAA,EAAC;IAEjD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAAC,GAAG,eAEX3I,OAAA;MAAAsI,QAAA,EAAK,CAAChI,OAAO,GAAG,oBAAoB,GAAG;IAAY;MAAAkI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACzD3I,OAAA;MAAM4I,QAAQ,EAAE3B,YAAa;MAAAqB,QAAA,GAC1B,CAAChI,OAAO,gBACPN,OAAA,CAAAE,SAAA;QAAAoI,QAAA,gBACEtI,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,MAAM;cACT5D,IAAI,EAAC,MAAM;cACXiB,KAAK,EAAElB,QAAQ,CAACC,IAAK;cACrB6D,QAAQ,EAAEd,aAAc;cACxBe,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,UAAU;cAAAP,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjD3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,UAAU;cACb5D,IAAI,EAAC,UAAU;cACfiB,KAAK,EAAElB,QAAQ,CAACE,QAAS;cACzB4D,QAAQ,EAAEd,aAAc;cACxBe,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3I,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,OAAO;cAAAP,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,OAAO;cACV5D,IAAI,EAAC,OAAO;cACZiB,KAAK,EAAElB,QAAQ,CAACG,KAAM;cACtB2D,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,KAAK;cAAAP,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjC3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,KAAK;cACR5D,IAAI,EAAC,KAAK;cACViB,KAAK,EAAElB,QAAQ,CAACK,GAAI;cACpByD,QAAQ,EAAEd,aAAc;cACxBe,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3I,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,OAAO;cAAAP,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,OAAO;cACV5D,IAAI,EAAC,OAAO;cACZiB,KAAK,EAAElB,QAAQ,CAACM,KAAM;cACtBwD,QAAQ,EAAE9C,YAAa;cACvB+C,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,eAAe;cAAAP,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,eAAe;cAClB5D,IAAI,EAAC,eAAe;cACpBiB,KAAK,EAAElB,QAAQ,CAACc,aAAc;cAC9BgD,QAAQ,EAAE9C;YAAa;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3I,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,gBAAgB;cAAAP,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxD3I,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbqE,EAAE,EAAC,gBAAgB;cACnB5D,IAAI,EAAC,gBAAgB;cACrBiB,KAAK,EAAElB,QAAQ,CAACO,cAAe;cAC/BuD,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,cAAc;cAAAP,QAAA,EAAC;YAE9B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3I,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbqE,EAAE,EAAC,cAAc;cACjB5D,IAAI,EAAC,cAAc;cACnBiB,KAAK,EAAElB,QAAQ,CAACQ,YAAa;cAC7BsD,QAAQ,EAAE9C;YAAa;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3I,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,UAAU;cAAAP,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9C3I,OAAA;cACE8I,EAAE,EAAC,UAAU;cACb5D,IAAI,EAAC,UAAU;cACfiB,KAAK,EAAElB,QAAQ,CAACS,QAAS;cACzBqD,QAAQ,EAAEd,aAAc;cAAAK,QAAA,gBAExBtI,OAAA;gBAAQmG,KAAK,EAAC,EAAE;gBAAAmC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1CjH,SAAS,CAACuH,GAAG,CAAEvD,QAAQ,iBACtB1F,OAAA;gBAA2BmG,KAAK,EAAET,QAAQ,CAACwD,GAAI;gBAAAZ,QAAA,EAC5C5C,QAAQ,CAACR;cAAI,GADHQ,QAAQ,CAACwD,GAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,cAAc;cAAAP,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpD3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,cAAc;cACjB5D,IAAI,EAAC,cAAc;cACnBiB,KAAK,EAAElB,QAAQ,CAACU,YAAa;cAC7BoD,QAAQ,EAAE9C;YAAa;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3I,OAAA;UAAKkI,SAAS,EAAC,UAAU;UAAAI,QAAA,gBACvBtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,UAAU;cAAAP,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxC3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,UAAU;cACb5D,IAAI,EAAC,UAAU;cACfiB,KAAK,EAAElB,QAAQ,CAACW,QAAS;cACzBmD,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,oBAAoB;cAAAP,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9D3I,OAAA;cACEyE,IAAI,EAAC,QAAQ;cACbqE,EAAE,EAAC,oBAAoB;cACvB5D,IAAI,EAAC,oBAAoB;cACzBiB,KAAK,EAAElB,QAAQ,CAACY,kBAAmB;cACnCkD,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CAAC,GACD,IAAI,eACR3I,OAAA;QAAKkI,SAAS,EAAC,UAAU;QAAAI,QAAA,GACtB,CAAChI,OAAO,gBACPN,OAAA,CAAAE,SAAA;UAAAoI,QAAA,gBACEtI,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpC3I,OAAA;cACEyE,IAAI,EAAC,MAAM;cACXqE,EAAE,EAAC,MAAM;cACT5D,IAAI,EAAC,MAAM;cACXiB,KAAK,EAAElB,QAAQ,CAACa,IAAK;cACrBiD,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3I,OAAA;YAAKkI,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzBtI,OAAA;cAAO6I,OAAO,EAAC,OAAO;cAAAP,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpC3I,OAAA;cACE8I,EAAE,EAAC,OAAO;cACV5D,IAAI,EAAC,OAAO;cACZiB,KAAK,EAAElB,QAAQ,CAACe,KAAM;cACtB+C,QAAQ,EAAEd;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACN,CAAC,GACD,IAAI,eACR3I,OAAA;UAAKkI,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzBtI,OAAA;YAAO6I,OAAO,EAAC,OAAO;YAAAP,QAAA,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzD3I,OAAA;YAAGoI,KAAK,EAAE;cAAEe,YAAY,EAAE;YAAM,CAAE;YAAAb,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5D3I,OAAA;YACEyE,IAAI,EAAC,MAAM;YACX0D,GAAG,EAAE3G,YAAa;YAClB0D,IAAI,EAAC,OAAO;YACZ6D,QAAQ,EAAElC;UAAkB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF3I,OAAA;YAAGoI,KAAK,EAAE;cAAEe,YAAY,EAAE,KAAK;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAd,QAAA,EAAC;UAErD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3I,OAAA;YACEyE,IAAI,EAAC,MAAM;YACXqE,EAAE,EAAC,OAAO;YACV5D,IAAI,EAAC,OAAO;YACZiB,KAAK,EAAElB,QAAQ,CAAC7D,KAAM;YACtB2H,QAAQ,EAAEnC;UAAkB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF3I,OAAA;YAAGoI,KAAK,EAAE;cAAEe,YAAY,EAAE,KAAK;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAd,QAAA,EAAC;UAErD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3I,OAAA;YAAKkI,SAAS,EAAC,SAAS;YAACK,OAAO,EAAErG,WAAY;YAAAoG,QAAA,EAAC;UAE/C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAGLzH,UAAU,iBACTlB,OAAA;YAAKkI,SAAS,EAAC,cAAc;YAAAI,QAAA,eAC3BtI,OAAA;cAAKkI,SAAS,EAAC,kBAAkB;cAAAI,QAAA,gBAC/BtI,OAAA;gBAAOmI,GAAG,EAAE7G,QAAS;gBAAC+H,QAAQ;gBAACjB,KAAK,EAAE;kBAAE5E,KAAK,EAAE;gBAAO;cAAE;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3D3I,OAAA;gBAAQkI,SAAS,EAAC,gBAAgB;gBAACK,OAAO,EAAEnF,YAAa;gBAAAkF,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3I,OAAA;gBAAQkI,SAAS,EAAC,gBAAgB;gBAACK,OAAO,EAAEP,UAAW;gBAAAM,QAAA,EAAC;cAExD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED3I,OAAA;YAAQmI,GAAG,EAAE5G,SAAU;YAAC6G,KAAK,EAAE;cAAEkB,OAAO,EAAE;YAAO;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAErDvH,KAAK,iBACJpB,OAAA;YAAAsI,QAAA,gBACEtI,OAAA;cAAAsI,QAAA,EAAI;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd3I,OAAA;cAAKuJ,GAAG,EAAEnI,KAAM;cAACoI,GAAG,EAAC,UAAU;cAACpB,KAAK,EAAE;gBAAE5E,KAAK,EAAE;cAAQ;YAAE;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACL,CAACrI,OAAO,gBACPN,OAAA;UAAKkI,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzBtI,OAAA;YAAO6I,OAAO,EAAC,SAAS;YAAAP,QAAA,GAAC,+DACY,EAAC,GAAG;UAAA;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACR3I,OAAA;YACE8I,EAAE,EAAC,SAAS;YACZ5D,IAAI,EAAC,SAAS;YACdiB,KAAK,EAAEnF,OAAQ;YACf+H,QAAQ,EAAE/B;UAAoB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,GAEN,EACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN3I,OAAA;QAAGoI,KAAK,EAAE;UAAEqB,KAAK,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAExH;MAAK;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvC3I,OAAA;QAAKkI,SAAS,EAAC,YAAY;QAAAI,QAAA,eACzBtI,OAAA;UAAKkI,SAAS,EAAC,cAAc;UAAAI,QAAA,eAC3BtI,OAAA;YAAOyE,IAAI,EAAC,QAAQ;YAAC0B,KAAK,EAAC;UAAQ;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpI,EAAA,CA9jBIJ,WAAW;EAAA,QACuBN,UAAU,EAGtBD,OAAO;AAAA;AAAA8J,EAAA,GAJ7BvJ,WAAW;AAgkBjB,eAAeA,WAAW;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}