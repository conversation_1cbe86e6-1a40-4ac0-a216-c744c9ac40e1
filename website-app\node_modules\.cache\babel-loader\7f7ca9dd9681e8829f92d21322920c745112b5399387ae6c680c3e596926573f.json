{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\ManageAccount\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback, useMemo } from \"react\";\nimport \"./ManageAccount.css\";\nimport { getRoles } from \"../../services/Roles/rolesService\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useNavigate } from \"react-router-dom\";\n\n// Icons\nimport { FaSearch, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaTimes, FaCheck, FaUser, FaEnvelope, FaLock, FaUser<PERSON>ag, FaCode, FaRefreshCw, FaSort, FaSortUp, FaSortDown, Fa<PERSON>ilter, FaUsers, FaShieldAlt } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManageAccount = () => {\n  _s();\n  // ✅ Danh sách roles mặc định - sử dụng useMemo để tối ưu hóa\n  const defaultRoles = useMemo(() => [{\n    _id: \"1\",\n    role: \"Admin\",\n    description: \"Quản trị viên hệ thống\"\n  }, {\n    _id: \"2\",\n    role: \"Manager\",\n    description: \"Quản lý cửa hàng\"\n  }, {\n    _id: \"3\",\n    role: \"Staff\",\n    description: \"Nhân viên bán hàng\"\n  }, {\n    _id: \"4\",\n    role: \"Cashier\",\n    description: \"Thu ngân\"\n  }, {\n    _id: \"5\",\n    role: \"Warehouse\",\n    description: \"Nhân viên kho\"\n  }, {\n    _id: \"6\",\n    role: \"Accountant\",\n    description: \"Kế toán\"\n  }, {\n    _id: \"7\",\n    role: \"Customer Service\",\n    description: \"Chăm sóc khách hàng\"\n  }, {\n    _id: \"8\",\n    role: \"Security\",\n    description: \"Bảo vệ\"\n  }], []);\n\n  // State management\n  const [accounts, setAccounts] = useState([]);\n  const [rolesData, setRolesData] = useState(defaultRoles); // ✅ Khởi tạo với roles mặc định\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [filterRole, setFilterRole] = useState(\"\");\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState(null);\n\n  // Form states\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    role: \"\",\n    code: \"\"\n  });\n  const [confirmOtp, setConfirmOtp] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n\n  // Hooks\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const dropdownRef = useRef(null);\n\n  // API Functions\n  const getAccounts = useCallback(async userId => {\n    if (!userId) {\n      console.error(\"Lỗi: userId không hợp lệ!\");\n      return;\n    }\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/list?userId=${userId}`, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Network response was not ok: ${response.statusText}`);\n      }\n      const data = await response.json();\n      console.log(\"API Response data:\", data); // ✅ Debug logging\n\n      // ✅ Đảm bảo data là array trước khi set\n      if (Array.isArray(data)) {\n        setAccounts(data);\n      } else {\n        console.warn(\"API response is not an array:\", data);\n        setAccounts([]); // Set empty array nếu response không phải array\n      }\n    } catch (error) {\n      console.error(\"Lỗi khi gọi API:\", error);\n      notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\n    } finally {\n      stopLoading();\n    }\n  }, [startLoading, stopLoading]);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setShowMenuIndex(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const fetchRoles = async () => {\n      if (user) {\n        try {\n          startLoading();\n          await getAccounts(user.id_owner);\n\n          // ✅ Cố gắng fetch roles từ API, fallback về default nếu thất bại\n          try {\n            const roles = await getRoles(user.id_owner);\n            if (Array.isArray(roles) && roles.length > 0) {\n              setRolesData(roles);\n              console.log(\"✅ Loaded roles from API:\", roles);\n            } else {\n              console.log(\"⚠️ API returned empty/invalid roles, using defaults\");\n              setRolesData(defaultRoles);\n            }\n          } catch (roleError) {\n            console.warn(\"⚠️ Failed to fetch roles from API, using defaults:\", roleError);\n            setRolesData(defaultRoles);\n          }\n          setFormData(prevData => ({\n            ...prevData,\n            id_owner: user._id\n          }));\n        } catch (error) {\n          console.error(\"❌ Error in fetchRoles:\", error);\n          setRolesData(defaultRoles); // Đảm bảo luôn có roles để hiển thị\n        } finally {\n          stopLoading();\n        }\n      }\n    };\n    fetchRoles();\n  }, [user, getAccounts, startLoading, stopLoading, defaultRoles]);\n  const toggleMenu = index => {\n    setShowMenuIndex(showMenuIndex === index ? null : index);\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // ✅ Đảm bảo accounts luôn là array trước khi filter\n  const filteredAccounts = Array.isArray(accounts) ? accounts.filter(account => {\n    const name = account.name ? account.name.toLowerCase() : \"\";\n    const email = account.email ? account.email.toLowerCase() : \"\";\n    const role = account.role ? account.role.toLowerCase() : \"\";\n    return name.includes(searchTerm.toLowerCase()) || email.includes(searchTerm.toLowerCase()) || role.includes(searchTerm.toLowerCase());\n  }) : []; // Trả về array rỗng nếu accounts không phải array\n\n  const handleCreateAccount = async e => {\n    e.preventDefault();\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: formData.role,\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      console.log(data);\n      if (confirmOtp) {\n        if (data.message === \"Staff is created successfully\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\n        }\n      } else {\n        if (data.message === \"Confirmation code sent\") {\n          setConfirmOtp(true);\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n        } else if (data.message === \"User_new updated successfully!\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const handleOpenEditModal = account => {\n    setFormData({\n      id: account._id,\n      name: account.name,\n      email: account.email,\n      role: account.role,\n      password: account.password // Assuming password can be left blank for editing\n    });\n    setShowEditModal(true);\n  };\n  const handleEditAccount = async e => {\n    e.preventDefault();\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${formData.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          role: formData.role\n        })\n      });\n      const data = await response.json();\n      console.log(\"Success:\", data);\n      stopLoading();\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\n      await getAccounts(user.id_owner);\n      setShowModal(false);\n    } catch (error) {\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\n      console.error(\"Error edit:\", error);\n    }\n  };\n\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\n  const handleDeleteAccount = async accountId => {\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${accountId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Failed to delete account: ${response.statusText}`);\n      }\n      if (user._id === accountId) {\n        logout();\n        navigate(\"/\");\n      } else {\n        await getAccounts(user.id_owner);\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting account:\", error);\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\n    } finally {\n      stopLoading();\n      setShowDeleteModal(false);\n      setSelectedAccount(null);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // ✅ Thêm function sentAgain để gửi lại mã OTP\n  const sentAgain = async () => {\n    try {\n      const dataUser = {\n        id: (user === null || user === void 0 ? void 0 : user.id) || \"\",\n        role: formData.role,\n        id_owner: (user === null || user === void 0 ? void 0 : user.id_owner) || \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      if (data.message === \"Confirmation code sent\") {\n        setFormData(prev => ({\n          ...prev,\n          code: \"\"\n        }));\n        notify(1, \"Mã xác nhận đã được gửi lại\", \"Thành công\");\n      } else {\n        notify(2, data.message || \"Không thể gửi lại mã xác nhận\", \"Thất bại\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n      notify(2, \"Có lỗi xảy ra khi gửi lại mã\", \"Lỗi\");\n    } finally {\n      stopLoading();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-table\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Qu\\u1EA3n l\\xED t\\xE0i kho\\u1EA3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"uy-search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"search-input\",\n          placeholder: \"Search for...\",\n          value: searchTerm,\n          onChange: handleSearchChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"create-order-btn\",\n          onClick: () => setShowModal(true),\n          children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleCreateAccount,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: \"10px\"\n            },\n            children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"\\uD83C\\uDFAF Ch\\u1ECDn vai tr\\xF2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: [role.role, \" - \", role.description]\n            }, role._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this), confirmOtp && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"code\",\n              placeholder: \"\\u0110i\\u1EC1n m\\xE3 x\\xE1c nh\\u1EADn \",\n              value: formData.code,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"uy-sentagain\",\n              onClick: sentAgain,\n              children: \"G\\u1EEDi l\\u1EA1i m\\xE3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 9\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowEditModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleEditAccount,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Edit Staff Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowEditModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\u1ECD T\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ph\\xE2n Quy\\u1EC1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tr\\u1EA1ng Th\\xE1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"L\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\xE0nh \\u0110\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredAccounts.map(account => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${account.status ? account.status.toLowerCase() : \"active\"}`,\n              children: account.status || \"Acctive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.salary || \"N/A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uy-action\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleMenu(account._id),\n                className: \"menu-btn\",\n                children: \"\\u22EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this), showMenuIndex === account._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uy-dropdown-menu\",\n                ref: dropdownRef,\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleOpenEditModal(account),\n                    children: \"Ch\\u1EC9nh s\\u1EEDa\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleDeleteAccount(account._id),\n                    children: \"X\\xF3a\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this)]\n        }, account._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        marginBottom: \"5px\",\n        color: \"red\"\n      },\n      children: \"L\\u01B0u \\xFD n\\u1EBFu s\\u1EEDa quy\\u1EC1n c\\u1EE7a Admin sang m\\u1ED9t quy\\u1EC1n kh\\xE1c m\\xE0 kh\\xF4ng bao g\\u1ED3m (\\\"*role\\\") b\\u1EA1n s\\u1EBD kh\\xF4ng th\\u1EC3 ph\\xE2n quy\\u1EC1n n\\u1EEFa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"deleteAccountBtn\",\n      onClick: () => handleDeleteAccount(user._id),\n      children: \"X\\xF3a T\\xE0i Kho\\u1EA3n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 411,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageAccount, \"AgdZLfxhVxU1xtn6WuQjMZMEpb4=\", false, function () {\n  return [useLoading, useAuth, useNavigate];\n});\n_c = ManageAccount;\nexport default ManageAccount;\nvar _c;\n$RefreshReg$(_c, \"ManageAccount\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "useMemo", "getRoles", "useAuth", "useLoading", "notify", "useNavigate", "FaSearch", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaEyeSlash", "FaTimes", "FaCheck", "FaUser", "FaEnvelope", "FaLock", "FaUserTag", "FaCode", "FaRefreshCw", "FaSort", "FaSortUp", "FaSortDown", "FaFilter", "FaUsers", "FaShieldAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManageAccount", "_s", "defaultRoles", "_id", "role", "description", "accounts", "setAccounts", "rolesData", "setRolesData", "searchTerm", "setSearchTerm", "sortConfig", "setSortConfig", "key", "direction", "filterRole", "setFilterRole", "showMenuIndex", "setShowMenuIndex", "showModal", "setShowModal", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedAccount", "setSelectedAccount", "formData", "setFormData", "name", "email", "password", "code", "confirmOtp", "setConfirmOtp", "showPassword", "setShowPassword", "formErrors", "setFormErrors", "startLoading", "stopLoading", "user", "logout", "navigate", "dropdownRef", "getAccounts", "userId", "console", "error", "response", "fetch", "method", "headers", "Authorization", "localStorage", "getItem", "ok", "Error", "statusText", "data", "json", "log", "Array", "isArray", "warn", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "fetchRoles", "id_owner", "roles", "length", "roleError", "prevData", "toggleMenu", "index", "handleSearchChange", "e", "value", "filteredAccounts", "filter", "account", "toLowerCase", "includes", "handleCreateAccount", "preventDefault", "dataUser", "id", "body", "JSON", "stringify", "message", "handleOpenEditModal", "handleEditAccount", "handleDeleteAccount", "accountId", "handleInputChange", "sentAgain", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "style", "marginBottom", "required", "disabled", "map", "status", "salary", "ref", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/ManageAccount/index.js"], "sourcesContent": ["import React, {\r\n  useEffect,\r\n  useRef,\r\n  useState,\r\n  useCallback,\r\n  useMemo,\r\n} from \"react\";\r\nimport \"./ManageAccount.css\";\r\nimport { getRoles } from \"../../services/Roles/rolesService\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// Icons\r\nimport {\r\n  FaSearch,\r\n  FaPlus,\r\n  FaEdit,\r\n  FaTrash,\r\n  FaEye,\r\n  FaEyeSlash,\r\n  FaTimes,\r\n  FaCheck,\r\n  FaUser,\r\n  FaEnvelope,\r\n  FaLock,\r\n  FaUserTag,\r\n  FaCode,\r\n  FaRefreshCw,\r\n  FaSort,\r\n  FaSortUp,\r\n  FaSortDown,\r\n  FaFilter,\r\n  FaUsers,\r\n  FaShieldAlt,\r\n} from \"react-icons/fa\";\r\n\r\nconst ManageAccount = () => {\r\n  // ✅ Danh sách roles mặc định - sử dụng useMemo để tối ưu hóa\r\n  const defaultRoles = useMemo(\r\n    () => [\r\n      { _id: \"1\", role: \"Admin\", description: \"Quản trị viên hệ thống\" },\r\n      { _id: \"2\", role: \"Manager\", description: \"Quản lý cửa hàng\" },\r\n      { _id: \"3\", role: \"Staff\", description: \"Nhân viên bán hàng\" },\r\n      { _id: \"4\", role: \"Cashier\", description: \"Thu ngân\" },\r\n      { _id: \"5\", role: \"Warehouse\", description: \"Nhân viên kho\" },\r\n      { _id: \"6\", role: \"Accountant\", description: \"Kế toán\" },\r\n      {\r\n        _id: \"7\",\r\n        role: \"Customer Service\",\r\n        description: \"Chăm sóc khách hàng\",\r\n      },\r\n      { _id: \"8\", role: \"Security\", description: \"Bảo vệ\" },\r\n    ],\r\n    []\r\n  );\r\n\r\n  // State management\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [rolesData, setRolesData] = useState(defaultRoles); // ✅ Khởi tạo với roles mặc định\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\r\n  const [filterRole, setFilterRole] = useState(\"\");\r\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [selectedAccount, setSelectedAccount] = useState(null);\r\n\r\n  // Form states\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    role: \"\",\r\n    code: \"\",\r\n  });\r\n  const [confirmOtp, setConfirmOtp] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [formErrors, setFormErrors] = useState({});\r\n\r\n  // Hooks\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, logout } = useAuth();\r\n  const navigate = useNavigate();\r\n  const dropdownRef = useRef(null);\r\n\r\n  // API Functions\r\n  const getAccounts = useCallback(\r\n    async (userId) => {\r\n      if (!userId) {\r\n        console.error(\"Lỗi: userId không hợp lệ!\");\r\n        return;\r\n      }\r\n\r\n      try {\r\n        startLoading();\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/user/list?userId=${userId}`,\r\n          {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\r\n            `Network response was not ok: ${response.statusText}`\r\n          );\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log(\"API Response data:\", data); // ✅ Debug logging\r\n\r\n        // ✅ Đảm bảo data là array trước khi set\r\n        if (Array.isArray(data)) {\r\n          setAccounts(data);\r\n        } else {\r\n          console.warn(\"API response is not an array:\", data);\r\n          setAccounts([]); // Set empty array nếu response không phải array\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Lỗi khi gọi API:\", error);\r\n        notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\r\n      } finally {\r\n        stopLoading();\r\n      }\r\n    },\r\n    [startLoading, stopLoading]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setShowMenuIndex(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchRoles = async () => {\r\n      if (user) {\r\n        try {\r\n          startLoading();\r\n          await getAccounts(user.id_owner);\r\n\r\n          // ✅ Cố gắng fetch roles từ API, fallback về default nếu thất bại\r\n          try {\r\n            const roles = await getRoles(user.id_owner);\r\n            if (Array.isArray(roles) && roles.length > 0) {\r\n              setRolesData(roles);\r\n              console.log(\"✅ Loaded roles from API:\", roles);\r\n            } else {\r\n              console.log(\r\n                \"⚠️ API returned empty/invalid roles, using defaults\"\r\n              );\r\n              setRolesData(defaultRoles);\r\n            }\r\n          } catch (roleError) {\r\n            console.warn(\r\n              \"⚠️ Failed to fetch roles from API, using defaults:\",\r\n              roleError\r\n            );\r\n            setRolesData(defaultRoles);\r\n          }\r\n\r\n          setFormData((prevData) => ({ ...prevData, id_owner: user._id }));\r\n        } catch (error) {\r\n          console.error(\"❌ Error in fetchRoles:\", error);\r\n          setRolesData(defaultRoles); // Đảm bảo luôn có roles để hiển thị\r\n        } finally {\r\n          stopLoading();\r\n        }\r\n      }\r\n    };\r\n    fetchRoles();\r\n  }, [user, getAccounts, startLoading, stopLoading, defaultRoles]);\r\n\r\n  const toggleMenu = (index) => {\r\n    setShowMenuIndex(showMenuIndex === index ? null : index);\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  // ✅ Đảm bảo accounts luôn là array trước khi filter\r\n  const filteredAccounts = Array.isArray(accounts)\r\n    ? accounts.filter((account) => {\r\n        const name = account.name ? account.name.toLowerCase() : \"\";\r\n        const email = account.email ? account.email.toLowerCase() : \"\";\r\n        const role = account.role ? account.role.toLowerCase() : \"\";\r\n\r\n        return (\r\n          name.includes(searchTerm.toLowerCase()) ||\r\n          email.includes(searchTerm.toLowerCase()) ||\r\n          role.includes(searchTerm.toLowerCase())\r\n        );\r\n      })\r\n    : []; // Trả về array rỗng nếu accounts không phải array\r\n\r\n  const handleCreateAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: formData.role,\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      console.log(data);\r\n\r\n      if (confirmOtp) {\r\n        if (data.message === \"Staff is created successfully\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\r\n        }\r\n      } else {\r\n        if (data.message === \"Confirmation code sent\") {\r\n          setConfirmOtp(true);\r\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n        } else if (data.message === \"User_new updated successfully!\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleOpenEditModal = (account) => {\r\n    setFormData({\r\n      id: account._id,\r\n      name: account.name,\r\n      email: account.email,\r\n      role: account.role,\r\n      password: account.password, // Assuming password can be left blank for editing\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const handleEditAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${formData.id}`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n          body: JSON.stringify({\r\n            name: formData.name,\r\n            email: formData.email,\r\n            password: formData.password,\r\n            role: formData.role,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      console.log(\"Success:\", data);\r\n      stopLoading();\r\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\r\n      await getAccounts(user.id_owner);\r\n      setShowModal(false);\r\n    } catch (error) {\r\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\r\n      console.error(\"Error edit:\", error);\r\n    }\r\n  };\r\n\r\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\r\n  const handleDeleteAccount = async (accountId) => {\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${accountId}`,\r\n        {\r\n          method: \"DELETE\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to delete account: ${response.statusText}`);\r\n      }\r\n\r\n      if (user._id === accountId) {\r\n        logout();\r\n        navigate(\"/\");\r\n      } else {\r\n        await getAccounts(user.id_owner);\r\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting account:\", error);\r\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\r\n    } finally {\r\n      stopLoading();\r\n      setShowDeleteModal(false);\r\n      setSelectedAccount(null);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  // ✅ Thêm function sentAgain để gửi lại mã OTP\r\n  const sentAgain = async () => {\r\n    try {\r\n      const dataUser = {\r\n        id: user?.id || \"\",\r\n        role: formData.role,\r\n        id_owner: user?.id_owner || \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n      };\r\n\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.message === \"Confirmation code sent\") {\r\n        setFormData((prev) => ({ ...prev, code: \"\" }));\r\n        notify(1, \"Mã xác nhận đã được gửi lại\", \"Thành công\");\r\n      } else {\r\n        notify(2, data.message || \"Không thể gửi lại mã xác nhận\", \"Thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n      notify(2, \"Có lỗi xảy ra khi gửi lại mã\", \"Lỗi\");\r\n    } finally {\r\n      stopLoading();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"account-table\">\r\n      <div className=\"account-header\">\r\n        <h2>Quản lí tài khoản</h2>\r\n        <div className=\"uy-search-container\">\r\n          <input\r\n            type=\"text\"\r\n            className=\"search-input\"\r\n            placeholder=\"Search for...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n          />\r\n          <button\r\n            className=\"create-order-btn\"\r\n            onClick={() => setShowModal(true)}\r\n          >\r\n            Tạo tài khoản nhân viên\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {showModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button className=\"close-modal\" onClick={() => setShowModal(false)}>\r\n              ✖\r\n            </button>\r\n            <form\r\n              className=\"create-account-form\"\r\n              onSubmit={handleCreateAccount}\r\n            >\r\n              <h3 style={{ marginBottom: \"10px\" }}>Tạo tài khoản nhân viên</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  🎯 Chọn vai trò\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role} - {role.description}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n\r\n              {confirmOtp && (\r\n                <>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"code\"\r\n                    placeholder=\"Điền mã xác nhận \"\r\n                    value={formData.code}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                  />\r\n                  <p className=\"uy-sentagain\" onClick={sentAgain}>\r\n                    Gửi lại mã\r\n                  </p>\r\n                </>\r\n              )}\r\n\r\n              <button type=\"submit\">\r\n                {confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"}\r\n              </button>\r\n              <button type=\"button\" onClick={() => setShowModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showEditModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button\r\n              className=\"close-modal\"\r\n              onClick={() => setShowEditModal(false)}\r\n            >\r\n              ✖\r\n            </button>\r\n            <form className=\"create-account-form\" onSubmit={handleEditAccount}>\r\n              {\" \"}\r\n              {/* Changed class name here */}\r\n              <h3>Edit Staff Account</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <button type=\"submit\">Submit</button>\r\n              <button type=\"button\" onClick={() => setShowEditModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <table>\r\n        <thead>\r\n          <tr>\r\n            <th>Họ Tên</th>\r\n            <th>Phân Quyền</th>\r\n            <th>Email</th>\r\n            <th>Trạng Thái</th>\r\n            <th>Lương</th>\r\n            <th>Hành Động</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredAccounts.map((account) => (\r\n            <tr key={account._id}>\r\n              <td>{account.name}</td>\r\n              <td>{account.role}</td>\r\n              <td>{account.email}</td>\r\n              <td>\r\n                <span\r\n                  className={`status ${\r\n                    account.status ? account.status.toLowerCase() : \"active\"\r\n                  }`}\r\n                >\r\n                  {account.status || \"Acctive\"}\r\n                </span>\r\n              </td>\r\n              <td>{account.salary || \"N/A\"}</td>\r\n              <td>\r\n                <div className=\"uy-action\">\r\n                  <button\r\n                    onClick={() => toggleMenu(account._id)}\r\n                    className=\"menu-btn\"\r\n                  >\r\n                    ⋮\r\n                  </button>\r\n                  {showMenuIndex === account._id && (\r\n                    <div className=\"uy-dropdown-menu\" ref={dropdownRef}>\r\n                      <ul>\r\n                        <li onClick={() => handleOpenEditModal(account)}>\r\n                          Chỉnh sửa\r\n                        </li>\r\n                        <li onClick={() => handleDeleteAccount(account._id)}>\r\n                          Xóa\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      <p style={{ marginBottom: \"5px\", color: \"red\" }}>\r\n        Lưu ý nếu sửa quyền của Admin sang một quyền khác mà không bao gồm\r\n        (\"*role\") bạn sẽ không thể phân quyền nữa\r\n      </p>\r\n      <button\r\n        className=\"deleteAccountBtn\"\r\n        onClick={() => handleDeleteAccount(user._id)}\r\n      >\r\n        Xóa Tài Khoản\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ManageAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,OAAO,QACF,OAAO;AACd,OAAO,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,SACEC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAMC,YAAY,GAAGhC,OAAO,CAC1B,MAAM,CACJ;IAAEiC,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAyB,CAAC,EAClE;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAmB,CAAC,EAC9D;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAqB,CAAC,EAC9D;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAAW,CAAC,EACtD;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,WAAW;IAAEC,WAAW,EAAE;EAAgB,CAAC,EAC7D;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAE;EAAU,CAAC,EACxD;IACEF,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAAS,CAAC,CACtD,EACD,EACF,CAAC;;EAED;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAACkC,YAAY,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC;IAAE8C,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC;IACvCgE,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZ9B,IAAI,EAAE,EAAE;IACR+B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM;IAAE0E,YAAY;IAAEC;EAAY,CAAC,GAAGtE,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEuE,IAAI;IAAEC;EAAO,CAAC,GAAGzE,OAAO,CAAC,CAAC;EAClC,MAAM0E,QAAQ,GAAGvE,WAAW,CAAC,CAAC;EAC9B,MAAMwE,WAAW,GAAGhF,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMiF,WAAW,GAAG/E,WAAW,CAC7B,MAAOgF,MAAM,IAAK;IAChB,IAAI,CAACA,MAAM,EAAE;MACXC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC1C;IACF;IAEA,IAAI;MACFT,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8CJ,MAAM,EAAE,EACtD;QACEK,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CACb,gCAAgCR,QAAQ,CAACS,UAAU,EACrD,CAAC;MACH;MAEA,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCb,OAAO,CAACc,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC,CAAC,CAAC;;MAEzC;MACA,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;QACvBvD,WAAW,CAACuD,IAAI,CAAC;MACnB,CAAC,MAAM;QACLZ,OAAO,CAACiB,IAAI,CAAC,+BAA+B,EAAEL,IAAI,CAAC;QACnDvD,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC7E,MAAM,CAAC,CAAC,EAAE,mCAAmC,EAAE,KAAK,CAAC;IACvD,CAAC,SAAS;MACRqE,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EACD,CAACD,YAAY,EAAEC,WAAW,CAC5B,CAAC;EAED7E,SAAS,CAAC,MAAM;IACd,MAAMsG,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAItB,WAAW,CAACuB,OAAO,IAAI,CAACvB,WAAW,CAACuB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtErD,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC;IAEDsD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAE1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENtG,SAAS,CAAC,MAAM;IACd,MAAM8G,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAIhC,IAAI,EAAE;QACR,IAAI;UACFF,YAAY,CAAC,CAAC;UACd,MAAMM,WAAW,CAACJ,IAAI,CAACiC,QAAQ,CAAC;;UAEhC;UACA,IAAI;YACF,MAAMC,KAAK,GAAG,MAAM3G,QAAQ,CAACyE,IAAI,CAACiC,QAAQ,CAAC;YAC3C,IAAIZ,KAAK,CAACC,OAAO,CAACY,KAAK,CAAC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;cAC5CtE,YAAY,CAACqE,KAAK,CAAC;cACnB5B,OAAO,CAACc,GAAG,CAAC,0BAA0B,EAAEc,KAAK,CAAC;YAChD,CAAC,MAAM;cACL5B,OAAO,CAACc,GAAG,CACT,qDACF,CAAC;cACDvD,YAAY,CAACP,YAAY,CAAC;YAC5B;UACF,CAAC,CAAC,OAAO8E,SAAS,EAAE;YAClB9B,OAAO,CAACiB,IAAI,CACV,oDAAoD,EACpDa,SACF,CAAC;YACDvE,YAAY,CAACP,YAAY,CAAC;UAC5B;UAEA6B,WAAW,CAAEkD,QAAQ,KAAM;YAAE,GAAGA,QAAQ;YAAEJ,QAAQ,EAAEjC,IAAI,CAACzC;UAAI,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,OAAOgD,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C1C,YAAY,CAACP,YAAY,CAAC,CAAC,CAAC;QAC9B,CAAC,SAAS;UACRyC,WAAW,CAAC,CAAC;QACf;MACF;IACF,CAAC;IACDiC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChC,IAAI,EAAEI,WAAW,EAAEN,YAAY,EAAEC,WAAW,EAAEzC,YAAY,CAAC,CAAC;EAEhE,MAAMgF,UAAU,GAAIC,KAAK,IAAK;IAC5BhE,gBAAgB,CAACD,aAAa,KAAKiE,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC1E,aAAa,CAAC0E,CAAC,CAACb,MAAM,CAACc,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGtB,KAAK,CAACC,OAAO,CAAC5D,QAAQ,CAAC,GAC5CA,QAAQ,CAACkF,MAAM,CAAEC,OAAO,IAAK;IAC3B,MAAMzD,IAAI,GAAGyD,OAAO,CAACzD,IAAI,GAAGyD,OAAO,CAACzD,IAAI,CAAC0D,WAAW,CAAC,CAAC,GAAG,EAAE;IAC3D,MAAMzD,KAAK,GAAGwD,OAAO,CAACxD,KAAK,GAAGwD,OAAO,CAACxD,KAAK,CAACyD,WAAW,CAAC,CAAC,GAAG,EAAE;IAC9D,MAAMtF,IAAI,GAAGqF,OAAO,CAACrF,IAAI,GAAGqF,OAAO,CAACrF,IAAI,CAACsF,WAAW,CAAC,CAAC,GAAG,EAAE;IAE3D,OACE1D,IAAI,CAAC2D,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,IACvCzD,KAAK,CAAC0D,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC,IACxCtF,IAAI,CAACuF,QAAQ,CAACjF,UAAU,CAACgF,WAAW,CAAC,CAAC,CAAC;EAE3C,CAAC,CAAC,GACF,EAAE,CAAC,CAAC;;EAER,MAAME,mBAAmB,GAAG,MAAOP,CAAC,IAAK;IACvCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAEnD,IAAI,GAAGA,IAAI,CAACmD,EAAE,GAAG,EAAE;QACvB3F,IAAI,EAAE0B,QAAQ,CAAC1B,IAAI;QACnByE,QAAQ,EAAEjC,IAAI,GAAGA,IAAI,CAACiC,QAAQ,GAAG,EAAE;QACnC5C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBI,UAAU,EAAEA,UAAU;QACtBD,IAAI,EAAEL,QAAQ,CAACK;MACjB,CAAC;MACDO,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDsC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAElD;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCpB,WAAW,CAAC,CAAC;MACbO,OAAO,CAACc,GAAG,CAACF,IAAI,CAAC;MAEjB,IAAI1B,UAAU,EAAE;QACd,IAAI0B,IAAI,CAACqC,OAAO,KAAK,+BAA+B,EAAE;UACpD7H,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDyD,WAAW,CAAC;YACVgE,EAAE,EAAEnD,IAAI,GAAGA,IAAI,CAACmD,EAAE,GAAG,EAAE;YACvB/D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZ9B,IAAI,EAAE,EAAE;YACRyE,QAAQ,EAAEjC,IAAI,GAAGA,IAAI,CAACiC,QAAQ,GAAG,EAAE;YACnC1C,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBhB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM2B,WAAW,CAACJ,IAAI,CAACiC,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLvG,MAAM,CAAC,CAAC,EAAEwF,IAAI,CAACqC,OAAO,IAAI,iBAAiB,EAAE,UAAU,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIrC,IAAI,CAACqC,OAAO,KAAK,wBAAwB,EAAE;UAC7C9D,aAAa,CAAC,IAAI,CAAC;UACnB/D,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;QACpD,CAAC,MAAM,IAAIwF,IAAI,CAACqC,OAAO,KAAK,gCAAgC,EAAE;UAC5D7H,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDyD,WAAW,CAAC;YACVgE,EAAE,EAAEnD,IAAI,GAAGA,IAAI,CAACmD,EAAE,GAAG,EAAE;YACvB/D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZ9B,IAAI,EAAE,EAAE;YACRyE,QAAQ,EAAEjC,IAAI,GAAGA,IAAI,CAACiC,QAAQ,GAAG,EAAE;YACnC1C,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBhB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM2B,WAAW,CAACJ,IAAI,CAACiC,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLvG,MAAM,CAAC,CAAC,EAAEwF,IAAI,CAACqC,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;QACpE;MACF;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMiD,mBAAmB,GAAIX,OAAO,IAAK;IACvC1D,WAAW,CAAC;MACVgE,EAAE,EAAEN,OAAO,CAACtF,GAAG;MACf6B,IAAI,EAAEyD,OAAO,CAACzD,IAAI;MAClBC,KAAK,EAAEwD,OAAO,CAACxD,KAAK;MACpB7B,IAAI,EAAEqF,OAAO,CAACrF,IAAI;MAClB8B,QAAQ,EAAEuD,OAAO,CAACvD,QAAQ,CAAE;IAC9B,CAAC,CAAC;IACFT,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM4E,iBAAiB,GAAG,MAAOhB,CAAC,IAAK;IACrCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACFnD,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCvB,QAAQ,CAACiE,EAAE,EAAE,EAC/C;QACEzC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDsC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBlE,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3B9B,IAAI,EAAE0B,QAAQ,CAAC1B;QACjB,CAAC;MACH,CACF,CAAC;MAED,MAAM0D,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCb,OAAO,CAACc,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;MAC7BnB,WAAW,CAAC,CAAC;MACbrE,MAAM,CAAC,CAAC,EAAE,gCAAgC,EAAE,YAAY,CAAC;MACzD,MAAM0E,WAAW,CAACJ,IAAI,CAACiC,QAAQ,CAAC;MAChCxD,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACd7E,MAAM,CAAC,CAAC,EAAE,8BAA8B,EAAE,UAAU,CAAC;MACrD4E,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMmD,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACF7D,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCkD,SAAS,EAAE,EAC7C;QACEjD,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6BR,QAAQ,CAACS,UAAU,EAAE,CAAC;MACrE;MAEA,IAAIjB,IAAI,CAACzC,GAAG,KAAKoG,SAAS,EAAE;QAC1B1D,MAAM,CAAC,CAAC;QACRC,QAAQ,CAAC,GAAG,CAAC;MACf,CAAC,MAAM;QACL,MAAME,WAAW,CAACJ,IAAI,CAACiC,QAAQ,CAAC;QAChCvG,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;MACrD;IACF,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7E,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;IACjD,CAAC,SAAS;MACRqE,WAAW,CAAC,CAAC;MACbhB,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM2E,iBAAiB,GAAInB,CAAC,IAAK;IAC/B,MAAM;MAAErD,IAAI;MAAEsD;IAAM,CAAC,GAAGD,CAAC,CAACb,MAAM;IAChCzC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGsD;IAAM,CAAC,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMX,QAAQ,GAAG;QACfC,EAAE,EAAE,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,EAAE,KAAI,EAAE;QAClB3F,IAAI,EAAE0B,QAAQ,CAAC1B,IAAI;QACnByE,QAAQ,EAAE,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,QAAQ,KAAI,EAAE;QAC9B5C,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE;MACjB,CAAC;MAEDU,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDsC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAElD;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACqC,OAAO,KAAK,wBAAwB,EAAE;QAC7CpE,WAAW,CAAE2E,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAEvE,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;QAC9C7D,MAAM,CAAC,CAAC,EAAE,6BAA6B,EAAE,YAAY,CAAC;MACxD,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAEwF,IAAI,CAACqC,OAAO,IAAI,+BAA+B,EAAE,UAAU,CAAC;MACxE;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9B7E,MAAM,CAAC,CAAC,EAAE,8BAA8B,EAAE,KAAK,CAAC;IAClD,CAAC,SAAS;MACRqE,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACE9C,OAAA;IAAK8G,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B/G,OAAA;MAAK8G,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/G,OAAA;QAAA+G,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BnH,OAAA;QAAK8G,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC/G,OAAA;UACEoH,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,cAAc;UACxBO,WAAW,EAAC,eAAe;UAC3B5B,KAAK,EAAE5E,UAAW;UAClByG,QAAQ,EAAE/B;QAAmB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACFnH,OAAA;UACE8G,SAAS,EAAC,kBAAkB;UAC5BS,OAAO,EAAEA,CAAA,KAAM/F,YAAY,CAAC,IAAI,CAAE;UAAAuF,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL5F,SAAS,iBACRvB,OAAA;MAAK8G,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B/G,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UAAQ8G,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAM/F,YAAY,CAAC,KAAK,CAAE;UAAAuF,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnH,OAAA;UACE8G,SAAS,EAAC,qBAAqB;UAC/BU,QAAQ,EAAEzB,mBAAoB;UAAAgB,QAAA,gBAE9B/G,OAAA;YAAIyH,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEnH,OAAA;YACEoH,IAAI,EAAC,MAAM;YACXjF,IAAI,EAAC,MAAM;YACXkF,WAAW,EAAC,WAAW;YACvB5B,KAAK,EAAExD,QAAQ,CAACE,IAAK;YACrBmF,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnH,OAAA;YACEoH,IAAI,EAAC,OAAO;YACZjF,IAAI,EAAC,OAAO;YACZkF,WAAW,EAAC,OAAO;YACnB5B,KAAK,EAAExD,QAAQ,CAACG,KAAM;YACtBkF,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnH,OAAA;YACEoH,IAAI,EAAC,UAAU;YACfjF,IAAI,EAAC,UAAU;YACfkF,WAAW,EAAC,UAAU;YACtB5B,KAAK,EAAExD,QAAQ,CAACI,QAAS;YACzBiF,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnH,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXsD,KAAK,EAAExD,QAAQ,CAAC1B,IAAK;YACrB+G,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;YAAAZ,QAAA,gBAER/G,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAACmC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRxG,SAAS,CAACkH,GAAG,CAAEtH,IAAI,iBAClBP,OAAA;cAAuByF,KAAK,EAAElF,IAAI,CAACA,IAAK;cAAAwG,QAAA,GACrCxG,IAAI,CAACA,IAAI,EAAC,KAAG,EAACA,IAAI,CAACC,WAAW;YAAA,GADpBD,IAAI,CAACD,GAAG;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAER5E,UAAU,iBACTvC,OAAA,CAAAE,SAAA;YAAA6G,QAAA,gBACE/G,OAAA;cACEoH,IAAI,EAAC,MAAM;cACXjF,IAAI,EAAC,MAAM;cACXkF,WAAW,EAAC,wCAAmB;cAC/B5B,KAAK,EAAExD,QAAQ,CAACK,IAAK;cACrBgF,QAAQ,EAAEX,iBAAkB;cAC5BgB,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFnH,OAAA;cAAG8G,SAAS,EAAC,cAAc;cAACS,OAAO,EAAEX,SAAU;cAAAG,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CACH,eAEDnH,OAAA;YAAQoH,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAClBxE,UAAU,GAAG,2BAA2B,GAAG;UAAY;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACTnH,OAAA;YAAQoH,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM/F,YAAY,CAAC,KAAK,CAAE;YAAAuF,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAxF,aAAa,iBACZ3B,OAAA;MAAK8G,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B/G,OAAA;QAAK8G,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/G,OAAA;UACE8G,SAAS,EAAC,aAAa;UACvBS,OAAO,EAAEA,CAAA,KAAM3F,gBAAgB,CAAC,KAAK,CAAE;UAAAmF,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnH,OAAA;UAAM8G,SAAS,EAAC,qBAAqB;UAACU,QAAQ,EAAEhB,iBAAkB;UAAAO,QAAA,GAC/D,GAAG,eAEJ/G,OAAA;YAAA+G,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnH,OAAA;YACEoH,IAAI,EAAC,MAAM;YACXjF,IAAI,EAAC,MAAM;YACXkF,WAAW,EAAC,WAAW;YACvB5B,KAAK,EAAExD,QAAQ,CAACE,IAAK;YACrBmF,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnH,OAAA;YACEoH,IAAI,EAAC,OAAO;YACZjF,IAAI,EAAC,OAAO;YACZkF,WAAW,EAAC,OAAO;YACnB5B,KAAK,EAAExD,QAAQ,CAACG,KAAM;YACtBkF,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnH,OAAA;YACEoH,IAAI,EAAC,UAAU;YACfjF,IAAI,EAAC,UAAU;YACfkF,WAAW,EAAC,UAAU;YACtB5B,KAAK,EAAExD,QAAQ,CAACI,QAAS;YACzBiF,QAAQ,EAAEX;UAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACFnH,OAAA;YACEmC,IAAI,EAAC,MAAM;YACXsD,KAAK,EAAExD,QAAQ,CAAC1B,IAAK;YACrB+G,QAAQ,EAAEX,iBAAkB;YAC5BgB,QAAQ;YAAAZ,QAAA,gBAER/G,OAAA;cAAQyF,KAAK,EAAC,EAAE;cAACmC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRxG,SAAS,CAACkH,GAAG,CAAEtH,IAAI,iBAClBP,OAAA;cAAuByF,KAAK,EAAElF,IAAI,CAACA,IAAK;cAAAwG,QAAA,EACrCxG,IAAI,CAACA;YAAI,GADCA,IAAI,CAACD,GAAG;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTnH,OAAA;YAAQoH,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCnH,OAAA;YAAQoH,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM3F,gBAAgB,CAAC,KAAK,CAAE;YAAAmF,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnH,OAAA;MAAA+G,QAAA,gBACE/G,OAAA;QAAA+G,QAAA,eACE/G,OAAA;UAAA+G,QAAA,gBACE/G,OAAA;YAAA+G,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfnH,OAAA;YAAA+G,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBnH,OAAA;YAAA+G,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdnH,OAAA;YAAA+G,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBnH,OAAA;YAAA+G,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdnH,OAAA;YAAA+G,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRnH,OAAA;QAAA+G,QAAA,EACGrB,gBAAgB,CAACmC,GAAG,CAAEjC,OAAO,iBAC5B5F,OAAA;UAAA+G,QAAA,gBACE/G,OAAA;YAAA+G,QAAA,EAAKnB,OAAO,CAACzD;UAAI;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBnH,OAAA;YAAA+G,QAAA,EAAKnB,OAAO,CAACrF;UAAI;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBnH,OAAA;YAAA+G,QAAA,EAAKnB,OAAO,CAACxD;UAAK;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBnH,OAAA;YAAA+G,QAAA,eACE/G,OAAA;cACE8G,SAAS,EAAE,UACTlB,OAAO,CAACkC,MAAM,GAAGlC,OAAO,CAACkC,MAAM,CAACjC,WAAW,CAAC,CAAC,GAAG,QAAQ,EACvD;cAAAkB,QAAA,EAEFnB,OAAO,CAACkC,MAAM,IAAI;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLnH,OAAA;YAAA+G,QAAA,EAAKnB,OAAO,CAACmC,MAAM,IAAI;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClCnH,OAAA;YAAA+G,QAAA,eACE/G,OAAA;cAAK8G,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/G,OAAA;gBACEuH,OAAO,EAAEA,CAAA,KAAMlC,UAAU,CAACO,OAAO,CAACtF,GAAG,CAAE;gBACvCwG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACrB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACR9F,aAAa,KAAKuE,OAAO,CAACtF,GAAG,iBAC5BN,OAAA;gBAAK8G,SAAS,EAAC,kBAAkB;gBAACkB,GAAG,EAAE9E,WAAY;gBAAA6D,QAAA,eACjD/G,OAAA;kBAAA+G,QAAA,gBACE/G,OAAA;oBAAIuH,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACX,OAAO,CAAE;oBAAAmB,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLnH,OAAA;oBAAIuH,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAACb,OAAO,CAACtF,GAAG,CAAE;oBAAAyG,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAnCEvB,OAAO,CAACtF,GAAG;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoChB,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACRnH,OAAA;MAAGyH,KAAK,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAM,CAAE;MAAAlB,QAAA,EAAC;IAGjD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACJnH,OAAA;MACE8G,SAAS,EAAC,kBAAkB;MAC5BS,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAAC1D,IAAI,CAACzC,GAAG,CAAE;MAAAyG,QAAA,EAC9C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/G,EAAA,CAnlBID,aAAa;EAAA,QAgDqB3B,UAAU,EACvBD,OAAO,EACfG,WAAW;AAAA;AAAAwJ,EAAA,GAlDxB/H,aAAa;AAqlBnB,eAAeA,aAAa;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}