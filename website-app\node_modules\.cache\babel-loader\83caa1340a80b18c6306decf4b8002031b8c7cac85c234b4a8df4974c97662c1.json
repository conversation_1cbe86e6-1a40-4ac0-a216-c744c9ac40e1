{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport Sales_daily from \"./sale_daily\";\nimport Useronline from \"./useronlinecard\";\n// src/index.js hoặc src/App.js'\nimport CalendarComponent from \"../Calendar/index.js\";\n// import React from 'react';\nimport { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from \"recharts\";\nimport \"./x1.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [totalrevenue, setTotalrevenue] = useState({\n    percentChange: \"0%\",\n    totalRevenueToday: \"0\",\n    state: \"\"\n  });\n  const [totalincome, setTotalincome] = useState({\n    profitToday: 0,\n    profitYesterday: 0,\n    percentChange: \"0%\",\n    message: \"notchange\"\n  });\n  const [data, setData] = useState([]);\n  const [topproduct, setTopproduct] = useState([]);\n  const [newcustomer, setNewcustomer] = useState({\n    customerToday: 0,\n    customerYesterday: 0,\n    percentChange: \"0%\",\n    state: \"notchange\"\n  });\n  const [pending, setPending] = useState({\n    total: 0,\n    percent: \"0%\"\n  });\n  const [act, setAct] = useState([]);\n  const datas = [{\n    name: \"Jan\",\n    \"Khách hàng trung thành\": 270,\n    \"khách hàng mới\": 150,\n    \"Khách hàng quay lại\": 542\n  }, {\n    name: \"Feb\",\n    \"Khách hàng trung thành\": 310,\n    \"khách hàng mới\": 180,\n    \"Khách hàng quay lại\": 520\n  }, {\n    name: \"Mar\",\n    \"Khách hàng trung thành\": 350,\n    \"khách hàng mới\": 200,\n    \"Khách hàng quay lại\": 560\n  }, {\n    name: \"Apr\",\n    \"Khách hàng trung thành\": 330,\n    \"khách hàng mới\": 220,\n    \"Khách hàng quay lại\": 480\n  }, {\n    name: \"May\",\n    \"Khách hàng trung thành\": 450,\n    \"khách hàng mới\": 260,\n    \"Khách hàng quay lại\": 550\n  }, {\n    name: \"Jun\",\n    \"Khách hàng trung thành\": 400,\n    \"khách hàng mới\": 290,\n    \"Khách hàng quay lại\": 580\n  }, {\n    name: \"Jul\",\n    \"Khách hàng trung thành\": 460,\n    \"khách hàng mới\": 320,\n    \"Khách hàng quay lại\": 620\n  }, {\n    name: \"Aug\",\n    \"Khách hàng trung thành\": 510,\n    \"khách hàng mới\": 340,\n    \"Khách hàng quay lại\": 680\n  }, {\n    name: \"Sep\",\n    \"Khách hàng trung thành\": 252,\n    \"khách hàng mới\": 360,\n    \"Khách hàng quay lại\": 740\n  }, {\n    name: \"Oct\",\n    \"Khách hàng trung thành\": 680,\n    \"khách hàng mới\": 390,\n    \"Khách hàng quay lại\": 820\n  }, {\n    name: \"Nov\",\n    \"Khách hàng trung thành\": 780,\n    \"khách hàng mới\": 420,\n    \"Khách hàng quay lại\": 890\n  }, {\n    name: \"Dec\",\n    \"Khách hàng trung thành\": 900,\n    \"khách hàng mới\": 450,\n    \"Khách hàng quay lại\": 980\n  }];\n\n  // if (!user) {\n  //   return <div>Không có người dùng nào đăng nhập.</div>;\n  // }\n  useEffect(() => {\n    const fetchData = async () => {\n      if (loading) return;\n      const get_revenue = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"Revenue:\", data);\n          setTotalrevenue(data);\n        } catch (error) {\n          console.error(\"Error fetching revenue:\", error);\n        }\n      };\n      const get_income = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"Income:\", data);\n          setTotalincome(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_customer = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"customer:\", data);\n          setNewcustomer(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_report_customer = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"customer:\", data);\n          setData(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_top_product = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"products:\", data);\n          setTopproduct(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_pending = async () => {\n        try {\n          const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          if (!response.ok) {\n            throw new Error(\"Network response was not ok\");\n          }\n          const data = await response.json();\n          console.log(\"pending:\", data);\n          setPending(data);\n        } catch (error) {\n          console.error(\"Error fetching income:\", error);\n        }\n      };\n      const get_activity = async () => {\n        try {\n          const activity = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              user: user\n            })\n          });\n          const data = await activity.json();\n          setAct(data.events);\n        } catch (error) {\n          console.error(\"Error fetching activity:\", error);\n        }\n      };\n      await Promise.all([get_revenue(), get_income(), get_customer(), get_report_customer(), get_top_product(), get_pending(), get_activity()]);\n    };\n    fetchData();\n  }, [loading]); // Thêm 'user' vào dependencies nếu cần\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"page-inner\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"dashboard-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"dashboard-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Trang ch\\u1EE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Made by team 25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"dashboard-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Manage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              children: \"Add Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"row row-card-no-pd\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        style: {\n                          whiteSpace: \"nowrap\",\n                          overflow: \"hidden\",\n                          textOverflow: \"ellipsis\"\n                        },\n                        children: \"Todays Income\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      class: \"text-muted\",\n                      children: \"All Customs Value\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    class: \"text-info fw-bold\",\n                    children: totalincome.profitToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"progress-bar bg-info\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${totalincome.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: [totalincome.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + totalincome.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Total Revenue\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      class: \"text-muted\",\n                      children: \"All Customs Value\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    class: \"text-success fw-bold\",\n                    children: totalrevenue.totalRevenueToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"progress-bar bg-success\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${totalrevenue.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: [totalrevenue.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + totalrevenue.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Pending order\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      class: \"text-muted\",\n                      children: \"Fresh Order Amount\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    class: \"text-danger fw-bold\",\n                    children: pending.total\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"progress-bar bg-danger\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${pending.percent}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: pending.percent\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-12 col-sm-6 col-md-6 col-xl-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"New Customer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      class: \"text-muted\",\n                      children: \"Joined New User\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    class: \"text-secondary fw-bold\",\n                    children: newcustomer.customerToday\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"progress progress-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"progress-bar bg-secondary\",\n                    role: \"progressbar\",\n                    style: {\n                      width: `${newcustomer.percentChange}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: \"Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    class: \"text-muted\",\n                    children: [newcustomer.percentChange, /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: \" \" + newcustomer.state\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"row row-card-no-pd\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"card-head-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-title\",\n                    children: \"Th\\u1ED1ng k\\xEA kh\\xE1ch h\\xE0ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-tools\",\n                    children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"#\",\n                      class: \"btn btn-label-success btn-round btn-sm me-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"btn-label\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          class: \"fa fa-pencil\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 469,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 25\n                      }, this), \"Export\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"#\",\n                      class: \"btn btn-label-info btn-round btn-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"btn-label\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          class: \"fa fa-print\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 475,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 25\n                      }, this), \"Print\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"chart-container\",\n                  style: {\n                    minHeight: \"375px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 400,\n                    children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                      data: datas,\n                      children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                        dataKey: \"name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                        type: \"number\",\n                        domain: [0, \"dataMax\"]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n                        strokeDasharray: \"3 3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"kh\\xE1ch h\\xE0ng m\\u1EDBi\",\n                        stroke: \"#ffa726\",\n                        fill: \"#1e88e5\",\n                        fillOpacity: 0.8\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"Kh\\xE1ch h\\xE0ng trung th\\xE0nh\",\n                        stroke: \"#ff6b6b\",\n                        fill: \"red\",\n                        fillOpacity: 0.6\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Area, {\n                        type: \"monotone\",\n                        dataKey: \"Kh\\xE1ch h\\xE0ng quay l\\u1EA1i\",\n                        stroke: \"#2196f3\",\n                        fill: \"#0277bd\",\n                        fillOpacity: 0.4\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 505,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"myChartLegend\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card card-primary\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card card-primary\",\n                children: /*#__PURE__*/_jsxDEV(Sales_daily, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: /*#__PURE__*/_jsxDEV(Useronline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"card-title\",\n                  children: \"L\\u1ECBch l\\xE0m vi\\u1EC7c\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body p-0\",\n                children: /*#__PURE__*/_jsxDEV(CalendarComponent, {\n                  defaultView: \"month\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-4\",\n            style: {\n              maxHeight: \"645px\",\n              overflowY: \"auto\",\n              marginBottom: \"15px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"card-title\",\n                  children: \"Top Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body pb-0\",\n                children: [topproduct.map((a, b) => {\n                  if (b >= 1) {\n                    return /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        class: \"separator-dashed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        class: \"d-flex\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          class: \"avatar\",\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: a.image ? a.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n                            alt: \"...\",\n                            class: \"avatar-img rounded-circle\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 638,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 637,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          class: \"flex-1 pt-1 ms-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                            class: \"fw-bold mb-1\",\n                            children: a.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 648,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          class: \"d-flex ms-auto align-items-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                            class: \"text-info fw-bold\",\n                            children: a.rate\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 653,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 652,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true);\n                  }\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"d-flex \",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      class: \"avatar\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: a.image ? a.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n                        alt: \"...\",\n                        class: \"avatar-img rounded-circle\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 662,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      class: \"flex-1 pt-1 ms-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                        class: \"fw-bold mb-1\",\n                        children: a.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 672,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      class: \"d-flex ms-auto align-items-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                        class: \"text-info fw-bold\",\n                        children: a.rate\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 23\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"pull-in\",\n                  children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                    id: \"topProductsChart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"row\",\n          style: {\n            marginTop: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"card-head-row card-tools-still-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-title\",\n                    children: \"Recent Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-tools\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"ol\", {\n                  class: \"activity-feed\",\n                  children: act.map(act => {\n                    return /*#__PURE__*/_jsxDEV(\"li\", {\n                      class: \"feed-item \" + act.type,\n                      children: [/*#__PURE__*/_jsxDEV(\"time\", {\n                        class: \"date\",\n                        datetime: act.date,\n                        children: act.date\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 783,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text\",\n                        dangerouslySetInnerHTML: {\n                          __html: act.detail // Hiển thị HTML (thẻ <br /> sẽ được xử lý)\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 786,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"card-head-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-title\",\n                    children: \"Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"card-tools\",\n                    children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                      class: \"nav nav-pills nav-secondary nav-pills-no-bd nav-sm\",\n                      id: \"pills-tab\",\n                      role: \"tablist\",\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        class: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          class: \"nav-link\",\n                          id: \"pills-today\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-today\",\n                          role: \"tab\",\n                          \"aria-selected\": \"true\",\n                          children: \"Today\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 865,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 864,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        class: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          class: \"nav-link active\",\n                          id: \"pills-week\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-week\",\n                          role: \"tab\",\n                          \"aria-selected\": \"false\",\n                          children: \"Week\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 877,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 876,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        class: \"nav-item\",\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          class: \"nav-link\",\n                          id: \"pills-month\",\n                          \"data-bs-toggle\": \"pill\",\n                          href: \"#pills-month\",\n                          role: \"tab\",\n                          \"aria-selected\": \"false\",\n                          children: \"Month\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 889,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                class: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"avatar avatar-online\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"avatar-title rounded-circle border border-white bg-info\",\n                      children: \"J\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 907,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      class: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Joko Subianto\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text-warning ps-3\",\n                        children: \"pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"text-muted\",\n                      children: \"I am facing some trouble with my viewport. When i start my\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      class: \"text-muted\",\n                      children: \"8:40 PM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"avatar avatar-offline\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"avatar-title rounded-circle border border-white bg-secondary\",\n                      children: \"P\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 928,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      class: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Prabowo Widodo\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text-success ps-3\",\n                        children: \"open\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"text-muted\",\n                      children: \"I have some query regarding the license issue.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 937,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      class: \"text-muted\",\n                      children: \"1 Day Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"avatar avatar-away\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"avatar-title rounded-circle border border-white bg-danger\",\n                      children: \"L\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 948,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      class: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Lee Chong Wei\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text-muted ps-3\",\n                        children: \"closed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"text-muted\",\n                      children: \"Is there any update plan for RTL version near future?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      class: \"text-muted\",\n                      children: \"2 Days Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 962,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"avatar avatar-offline\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"avatar-title rounded-circle border border-white bg-secondary\",\n                      children: \"P\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 968,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      class: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Peter Parker\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text-success ps-3\",\n                        children: \"open\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 975,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"text-muted\",\n                      children: \"I have some query regarding the license issue.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 977,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      class: \"text-muted\",\n                      children: \"2 Day Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"separator-dashed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  class: \"d-flex\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"avatar avatar-away\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"avatar-title rounded-circle border border-white bg-danger\",\n                      children: \"L\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"flex-1 ms-3 pt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      class: \"text-uppercase fw-bold mb-1\",\n                      children: [\"Logan Paul \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        class: \"text-muted ps-3\",\n                        children: \"closed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 994,\n                        columnNumber: 36\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 993,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      class: \"text-muted\",\n                      children: \"Is there any update plan for RTL version near future?\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 992,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    class: \"float-end pt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      class: \"text-muted\",\n                      children: \"2 Days Ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1001,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1000,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      class: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"container-fluid d-flex justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n          class: \"pull-left\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            class: \"nav\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              class: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"nav-link\",\n                href: \"#\",\n                children: \"TeaM_25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              class: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"nav-link\",\n                href: \"#\",\n                children: [\" \", \"Help\", \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1019,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              class: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"nav-link\",\n                href: \"#\",\n                children: [\" \", \"Licenses\", \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1012,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"copyright\",\n          children: [\"2024, made with \", /*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"fa fa-heart heart text-danger\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 29\n          }, this), \" by team_25\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Distributed by\", /*#__PURE__*/_jsxDEV(\"a\", {\n            target: \"_blank\",\n            href: \"https://themewagon.com/\",\n            children: \"team_25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1037,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1011,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1010,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Home, \"C/LdXGOHJT+1fiSVoulkKeCWn78=\", false, function () {\n  return [useAuth];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "Sales_daily", "Useronline", "CalendarComponent", "AreaChart", "Area", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "user", "loading", "totalrevenue", "setTotalrevenue", "percentChange", "totalRevenueToday", "state", "totalincome", "setTotalincome", "profitToday", "profitYesterday", "message", "data", "setData", "topproduct", "setTopproduct", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customerToday", "customerYesterday", "pending", "setPending", "total", "percent", "act", "setAct", "datas", "name", "fetchData", "get_revenue", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "console", "log", "error", "get_income", "get_customer", "get_report_customer", "get_top_product", "get_pending", "get_activity", "activity", "events", "Promise", "all", "children", "class", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "style", "whiteSpace", "overflow", "textOverflow", "role", "width", "minHeight", "height", "dataKey", "type", "domain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "fill", "fillOpacity", "id", "defaultView", "maxHeight", "overflowY", "marginBottom", "map", "a", "b", "src", "image", "secure_url", "alt", "rate", "marginTop", "datetime", "date", "dangerouslySetInnerHTML", "__html", "detail", "target", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport Sales_daily from \"./sale_daily\";\r\nimport Useronline from \"./useronlinecard\";\r\n// src/index.js hoặc src/App.js'\r\nimport CalendarComponent from \"../Calendar/index.js\";\r\n// import React from 'react';\r\nimport {\r\n  AreaChart,\r\n  Area,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n} from \"recharts\";\r\n\r\nimport \"./x1.css\";\r\nfunction Home() {\r\n  const { user, loading } = useAuth();\r\n  const [totalrevenue, setTotalrevenue] = useState({\r\n    percentChange: \"0%\",\r\n    totalRevenueToday: \"0\",\r\n    state: \"\",\r\n  });\r\n  const [totalincome, setTotalincome] = useState({\r\n    profitToday: 0,\r\n    profitYesterday: 0,\r\n    percentChange: \"0%\",\r\n    message: \"notchange\",\r\n  });\r\n  const [data, setData] = useState([]);\r\n  const [topproduct, setTopproduct] = useState([]);\r\n  const [newcustomer, setNewcustomer] = useState({\r\n    customerToday: 0,\r\n    customerYesterday: 0,\r\n    percentChange: \"0%\",\r\n    state: \"notchange\",\r\n  });\r\n  const [pending, setPending] = useState({ total: 0, percent: \"0%\" });\r\n  const [act, setAct] = useState([]);\r\n  const datas = [\r\n    {\r\n      name: \"Jan\",\r\n      \"Khách hàng trung thành\": 270,\r\n      \"khách hàng mới\": 150,\r\n      \"Khách hàng quay lại\": 542,\r\n    },\r\n    {\r\n      name: \"Feb\",\r\n      \"Khách hàng trung thành\": 310,\r\n      \"khách hàng mới\": 180,\r\n      \"Khách hàng quay lại\": 520,\r\n    },\r\n    {\r\n      name: \"Mar\",\r\n      \"Khách hàng trung thành\": 350,\r\n      \"khách hàng mới\": 200,\r\n      \"Khách hàng quay lại\": 560,\r\n    },\r\n    {\r\n      name: \"Apr\",\r\n      \"Khách hàng trung thành\": 330,\r\n      \"khách hàng mới\": 220,\r\n      \"Khách hàng quay lại\": 480,\r\n    },\r\n    {\r\n      name: \"May\",\r\n      \"Khách hàng trung thành\": 450,\r\n      \"khách hàng mới\": 260,\r\n      \"Khách hàng quay lại\": 550,\r\n    },\r\n    {\r\n      name: \"Jun\",\r\n      \"Khách hàng trung thành\": 400,\r\n      \"khách hàng mới\": 290,\r\n      \"Khách hàng quay lại\": 580,\r\n    },\r\n    {\r\n      name: \"Jul\",\r\n      \"Khách hàng trung thành\": 460,\r\n      \"khách hàng mới\": 320,\r\n      \"Khách hàng quay lại\": 620,\r\n    },\r\n    {\r\n      name: \"Aug\",\r\n      \"Khách hàng trung thành\": 510,\r\n      \"khách hàng mới\": 340,\r\n      \"Khách hàng quay lại\": 680,\r\n    },\r\n    {\r\n      name: \"Sep\",\r\n      \"Khách hàng trung thành\": 252,\r\n      \"khách hàng mới\": 360,\r\n      \"Khách hàng quay lại\": 740,\r\n    },\r\n    {\r\n      name: \"Oct\",\r\n      \"Khách hàng trung thành\": 680,\r\n      \"khách hàng mới\": 390,\r\n      \"Khách hàng quay lại\": 820,\r\n    },\r\n    {\r\n      name: \"Nov\",\r\n      \"Khách hàng trung thành\": 780,\r\n      \"khách hàng mới\": 420,\r\n      \"Khách hàng quay lại\": 890,\r\n    },\r\n    {\r\n      name: \"Dec\",\r\n      \"Khách hàng trung thành\": 900,\r\n      \"khách hàng mới\": 450,\r\n      \"Khách hàng quay lại\": 980,\r\n    },\r\n  ];\r\n\r\n  // if (!user) {\r\n  //   return <div>Không có người dùng nào đăng nhập.</div>;\r\n  // }\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (loading) return;\r\n      const get_revenue = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"Revenue:\", data);\r\n          setTotalrevenue(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching revenue:\", error);\r\n        }\r\n      };\r\n\r\n      const get_income = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"Income:\", data);\r\n          setTotalincome(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_customer = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"customer:\", data);\r\n          setNewcustomer(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_report_customer = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"customer:\", data);\r\n          setData(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_top_product = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n\r\n          const data = await response.json();\r\n          console.log(\"products:\", data);\r\n          setTopproduct(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_pending = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Network response was not ok\");\r\n          }\r\n          const data = await response.json();\r\n          console.log(\"pending:\", data);\r\n          setPending(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching income:\", error);\r\n        }\r\n      };\r\n      const get_activity = async () => {\r\n        try {\r\n          const activity = await fetch(\r\n            \"http://localhost:8080/api/home/<USER>\",\r\n            {\r\n              method: \"POST\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n              body: JSON.stringify({\r\n                user: user,\r\n              }),\r\n            }\r\n          );\r\n          const data = await activity.json();\r\n\r\n          setAct(data.events);\r\n        } catch (error) {\r\n          console.error(\"Error fetching activity:\", error);\r\n        }\r\n      };\r\n\r\n      await Promise.all([\r\n        get_revenue(),\r\n        get_income(),\r\n        get_customer(),\r\n        get_report_customer(),\r\n        get_top_product(),\r\n        get_pending(),\r\n        get_activity(),\r\n      ]);\r\n    };\r\n\r\n    fetchData();\r\n  }, [loading]); // Thêm 'user' vào dependencies nếu cần\r\n\r\n  return (\r\n    <>\r\n      <div class=\"container\">\r\n        <div class=\"page-inner\">\r\n          <div class=\"dashboard-container\">\r\n            <div class=\"dashboard-title\">\r\n              <h3>Trang chủ</h3>\r\n              <h6>Made by team 25</h6>\r\n            </div>\r\n            <div class=\"dashboard-actions\">\r\n              <a href=\"#\">Manage</a>\r\n              <a href=\"#\">Add Customer</a>\r\n            </div>\r\n          </div>\r\n          <div class=\"row row-card-no-pd\">\r\n            <div class=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b\r\n                          style={{\r\n                            whiteSpace: \"nowrap\",\r\n                            overflow: \"hidden\",\r\n                            textOverflow: \"ellipsis\",\r\n                          }}\r\n                        >\r\n                          Todays Income\r\n                        </b>\r\n                      </h6>\r\n                      <p class=\"text-muted\">All Customs Value</p>\r\n                    </div>\r\n                    <h4 class=\"text-info fw-bold\">{totalincome.profitToday}</h4>\r\n                  </div>\r\n                  <div class=\"progress progress-sm\">\r\n                    <div\r\n                      class=\"progress-bar bg-info\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${totalincome.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <p class=\"text-muted\">Change</p>\r\n                    <p class=\"text-muted\">\r\n                      {totalincome.percentChange}\r\n                      <small>{\" \" + totalincome.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>Total Revenue</b>\r\n                      </h6>\r\n                      <p class=\"text-muted\">All Customs Value</p>\r\n                    </div>\r\n                    <h4 class=\"text-success fw-bold\">\r\n                      {totalrevenue.totalRevenueToday}\r\n                    </h4>\r\n                  </div>\r\n                  <div class=\"progress progress-sm\">\r\n                    <div\r\n                      class=\"progress-bar bg-success\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${totalrevenue.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <p class=\"text-muted\">Change</p>\r\n                    <p class=\"text-muted\">\r\n                      {totalrevenue.percentChange}\r\n                      <small>{\" \" + totalrevenue.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>Pending order</b>\r\n                      </h6>\r\n                      <p class=\"text-muted\">Fresh Order Amount</p>\r\n                    </div>\r\n                    <h4 class=\"text-danger fw-bold\">{pending.total}</h4>\r\n                  </div>\r\n                  <div class=\"progress progress-sm\">\r\n                    <div\r\n                      class=\"progress-bar bg-danger\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${pending.percent}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <p class=\"text-muted\">Change</p>\r\n                    <p class=\"text-muted\">{pending.percent}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-12 col-sm-6 col-md-6 col-xl-3\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <div>\r\n                      <h6>\r\n                        <b>New Customer</b>\r\n                      </h6>\r\n                      <p class=\"text-muted\">Joined New User</p>\r\n                    </div>\r\n                    <h4 class=\"text-secondary fw-bold\">\r\n                      {newcustomer.customerToday}\r\n                    </h4>\r\n                  </div>\r\n                  <div class=\"progress progress-sm\">\r\n                    <div\r\n                      class=\"progress-bar bg-secondary\"\r\n                      role=\"progressbar\"\r\n                      style={{ width: `${newcustomer.percentChange}` }}\r\n                    ></div>\r\n                  </div>\r\n                  <div class=\"d-flex justify-content-between\">\r\n                    <p class=\"text-muted\">Change</p>\r\n                    <p class=\"text-muted\">\r\n                      {newcustomer.percentChange}\r\n                      <small>{\" \" + newcustomer.state}</small>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row row-card-no-pd\">\r\n            <div class=\"col-md-8\">\r\n              <div class=\"card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-head-row\">\r\n                    <div class=\"card-title\">Thống kê khách hàng</div>\r\n                    <div class=\"card-tools\">\r\n                      <a\r\n                        href=\"#\"\r\n                        class=\"btn btn-label-success btn-round btn-sm me-2\"\r\n                      >\r\n                        <span class=\"btn-label\">\r\n                          <i class=\"fa fa-pencil\"></i>\r\n                        </span>\r\n                        Export\r\n                      </a>\r\n                      <a href=\"#\" class=\"btn btn-label-info btn-round btn-sm\">\r\n                        <span class=\"btn-label\">\r\n                          <i class=\"fa fa-print\"></i>\r\n                        </span>\r\n                        Print\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-body\">\r\n                  <div class=\"chart-container\" style={{ minHeight: \"375px\" }}>\r\n                    <ResponsiveContainer width=\"100%\" height={400}>\r\n                      <AreaChart data={datas}>\r\n                        <XAxis dataKey=\"name\" />\r\n                        <YAxis type=\"number\" domain={[0, \"dataMax\"]} />\r\n                        <CartesianGrid strokeDasharray=\"3 3\" />\r\n                        <Tooltip />\r\n                        <Legend />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"khách hàng mới\"\r\n                          stroke=\"#ffa726\"\r\n                          fill=\"#1e88e5\"\r\n                          fillOpacity={0.8}\r\n                        />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"Khách hàng trung thành\"\r\n                          stroke=\"#ff6b6b\"\r\n                          fill=\"red\"\r\n                          fillOpacity={0.6}\r\n                        />\r\n                        <Area\r\n                          type=\"monotone\"\r\n                          dataKey=\"Khách hàng quay lại\"\r\n                          stroke=\"#2196f3\"\r\n                          fill=\"#0277bd\"\r\n                          fillOpacity={0.4}\r\n                        />\r\n                      </AreaChart>\r\n                    </ResponsiveContainer>\r\n                  </div>\r\n                  <div id=\"myChartLegend\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n              <div class=\"card card-primary\">\r\n                <div class=\"card card-primary\">\r\n                  <Sales_daily />\r\n                </div>\r\n              </div>\r\n              <div class=\"card\">\r\n                <Useronline />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"col-md-8\">\r\n              <div class=\"card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-title\">Lịch làm việc</div>\r\n                </div>\r\n                <div class=\"card-body p-0\">\r\n                  <CalendarComponent defaultView=\"month\" />\r\n                  {/* <div class=\"table-responsive\">\r\n                  <table class=\"table align-items-center\">\r\n                    <thead class=\"thead-light\">\r\n                      <tr>\r\n                        <th scope=\"col\">Page name</th>\r\n                        <th scope=\"col\">Visitors</th>\r\n                        <th scope=\"col\">Unique users</th>\r\n                        <th scope=\"col\">Bounce rate</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/</th>\r\n                        <td>4,569</td>\r\n                        <td>340</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/index.html</th>\r\n                        <td>3,985</td>\r\n                        <td>319</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/charts.html</th>\r\n                        <td>3,513</td>\r\n                        <td>294</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          36,49%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/tables.html</th>\r\n                        <td>2,050</td>\r\n                        <td>147</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          50,87%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/profile.html</th>\r\n                        <td>1,795</td>\r\n                        <td>190</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-down text-danger me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/</th>\r\n                        <td>4,569</td>\r\n                        <td>340</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-up text-success me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th scope=\"row\">/kaiadmin/index.html</th>\r\n                        <td>3,985</td>\r\n                        <td>319</td>\r\n                        <td>\r\n                          <i class=\"fas fa-arrow-down text-warning me-3\"></i>\r\n                          46,53%\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div> */}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              class=\"col-md-4\"\r\n              style={{\r\n                maxHeight: \"645px\",\r\n                overflowY: \"auto\",\r\n                marginBottom: \"15px\",\r\n              }}\r\n            >\r\n              <div class=\"card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-title\">Top Products</div>\r\n                </div>\r\n                <div class=\"card-body pb-0\">\r\n                  {topproduct.map((a, b) => {\r\n                    if (b >= 1) {\r\n                      return (\r\n                        <>\r\n                          <div class=\"separator-dashed\"></div>\r\n                          <div class=\"d-flex\">\r\n                            <div class=\"avatar\">\r\n                              <img\r\n                                src={\r\n                                  a.image\r\n                                    ? a.image.secure_url\r\n                                    : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                                }\r\n                                alt=\"...\"\r\n                                class=\"avatar-img rounded-circle\"\r\n                              />\r\n                            </div>\r\n                            <div class=\"flex-1 pt-1 ms-2\">\r\n                              <h6 class=\"fw-bold mb-1\">{a.name}</h6>\r\n                              {/* <small class=\"text-muted\">The Best Donuts</small> */}\r\n                            </div>\r\n                            <div class=\"d-flex ms-auto align-items-center\">\r\n                              <h4 class=\"text-info fw-bold\">{a.rate}</h4>\r\n                            </div>\r\n                          </div>\r\n                        </>\r\n                      );\r\n                    }\r\n                    return (\r\n                      <div class=\"d-flex \">\r\n                        <div class=\"avatar\">\r\n                          <img\r\n                            src={\r\n                              a.image\r\n                                ? a.image.secure_url\r\n                                : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                            }\r\n                            alt=\"...\"\r\n                            class=\"avatar-img rounded-circle\"\r\n                          />\r\n                        </div>\r\n                        <div class=\"flex-1 pt-1 ms-2\">\r\n                          <h6 class=\"fw-bold mb-1\">{a.name}</h6>\r\n                          {/* <small class=\"text-muted\">Cascading Style Sheets</small> */}\r\n                        </div>\r\n                        <div class=\"d-flex ms-auto align-items-center\">\r\n                          <h4 class=\"text-info fw-bold\">{a.rate}</h4>\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {/* <div class=\"d-flex \">\r\n                  <div class=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct.svg\"\r\n                      alt=\"...\"\r\n                      class=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"flex-1 pt-1 ms-2\">\r\n                    <h6 class=\"fw-bold mb-1\">CSS</h6>\r\n                    <small class=\"text-muted\">Cascading Style Sheets</small>\r\n                  </div>\r\n                  <div class=\"d-flex ms-auto align-items-center\">\r\n                    <h4 class=\"text-info fw-bold\">+$17</h4>\r\n                  </div>\r\n                </div>\r\n                <div class=\"separator-dashed\"></div>\r\n                <div class=\"d-flex\">\r\n                  <div class=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct.svg\"\r\n                      alt=\"...\"\r\n                      class=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"flex-1 pt-1 ms-2\">\r\n                    <h6 class=\"fw-bold mb-1\">J.CO Donuts</h6>\r\n                    <small class=\"text-muted\">The Best Donuts</small>\r\n                  </div>\r\n                  <div class=\"d-flex ms-auto align-items-center\">\r\n                    <h4 class=\"text-info fw-bold\">+$300</h4>\r\n                  </div>\r\n                </div>\r\n                <div class=\"separator-dashed\"></div>\r\n                <div class=\"d-flex\">\r\n                  <div class=\"avatar\">\r\n                    <img\r\n                      src=\"assets/img/logoproduct3.svg\"\r\n                      alt=\"...\"\r\n                      class=\"avatar-img rounded-circle\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"flex-1 pt-1 ms-2\">\r\n                    <h6 class=\"fw-bold mb-1\">Ready Pro</h6>\r\n                    <small class=\"text-muted\">\r\n                      Bootstrap 5 Admin Dashboard\r\n                    </small>\r\n                  </div>\r\n                  <div class=\"d-flex ms-auto align-items-center\">\r\n                    <h4 class=\"text-info fw-bold\">+$350</h4>\r\n                  </div>\r\n                </div> */}\r\n                  <div class=\"separator-dashed\"></div>\r\n                  <div class=\"pull-in\">\r\n                    <canvas id=\"topProductsChart\"></canvas>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row\" style={{ marginTop: \"10px\" }}>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-head-row card-tools-still-right\">\r\n                    <div class=\"card-title\">Recent Activity</div>\r\n                    <div class=\"card-tools\">\r\n                      {/* <div class=\"dropdown\">\r\n                      <button\r\n                        class=\"btn btn-icon btn-clean\"\r\n                        type=\"button\"\r\n                        id=\"dropdownMenuButton\"\r\n                        data-bs-toggle=\"dropdown\"\r\n                        aria-haspopup=\"true\"\r\n                        aria-expanded=\"false\"\r\n                      >\r\n                        <i class=\"fas fa-ellipsis-h\"></i>\r\n                      </button>\r\n                      <div\r\n                        class=\"dropdown-menu\"\r\n                        aria-labelledby=\"dropdownMenuButton\"\r\n                      >\r\n                        <a class=\"dropdown-item\" href=\"#\">\r\n                          Action\r\n                        </a>\r\n                        <a class=\"dropdown-item\" href=\"#\">\r\n                          Another action\r\n                        </a>\r\n                        <a class=\"dropdown-item\" href=\"#\">\r\n                          Something else here\r\n                        </a>\r\n                      </div>\r\n                    </div> */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-body\">\r\n                  <ol class=\"activity-feed\">\r\n                    {act.map((act) => {\r\n                      return (\r\n                        <li class={\"feed-item \" + act.type}>\r\n                          <time class=\"date\" datetime={act.date}>\r\n                            {act.date}\r\n                          </time>\r\n                          <span\r\n                            class=\"text\"\r\n                            dangerouslySetInnerHTML={{\r\n                              __html: act.detail, // Hiển thị HTML (thẻ <br /> sẽ được xử lý)\r\n                            }}\r\n                          ></span>\r\n                        </li>\r\n                      );\r\n                    })}\r\n                    {/* <li class=\"feed-item feed-item-secondary\">\r\n                    <time class=\"date\" datetime=\"9-25\">\r\n                      Sep 25\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Responded to need\r\n                      <a href=\"#\">\"Volunteer opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li class=\"feed-item feed-item-success\">\r\n                    <time class=\"date\" datetime=\"9-24\">\r\n                      Sep 24\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Added an interest\r\n                      <a href=\"#\">\"Volunteer Activities\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li class=\"feed-item feed-item-info\">\r\n                    <time class=\"date\" datetime=\"9-23\">\r\n                      Sep 23\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Joined the group\r\n                      <a href=\"single-group.php\">\"Boardsmanship Forum\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li class=\"feed-item feed-item-warning\">\r\n                    <time class=\"date\" datetime=\"9-21\">\r\n                      Sep 21\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Responded to need\r\n                      <a href=\"#\">\"In-Kind Opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li class=\"feed-item feed-item-danger\">\r\n                    <time class=\"date\" datetime=\"9-18\">\r\n                      Sep 18\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Created need\r\n                      <a href=\"#\">\"Volunteer Opportunity\"</a>\r\n                    </span>\r\n                  </li>\r\n                  <li class=\"feed-item\">\r\n                    <time class=\"date\" datetime=\"9-17\">\r\n                      Sep 17\r\n                    </time>\r\n                    <span class=\"text\">\r\n                      Attending the event\r\n                      <a href=\"single-event.php\">\"Some New Event\"</a>\r\n                    </span>\r\n                  </li> */}\r\n                  </ol>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"card\">\r\n                <div class=\"card-header\">\r\n                  <div class=\"card-head-row\">\r\n                    <div class=\"card-title\">Information</div>\r\n                    <div class=\"card-tools\">\r\n                      <ul\r\n                        class=\"nav nav-pills nav-secondary nav-pills-no-bd nav-sm\"\r\n                        id=\"pills-tab\"\r\n                        role=\"tablist\"\r\n                      >\r\n                        <li class=\"nav-item\">\r\n                          <a\r\n                            class=\"nav-link\"\r\n                            id=\"pills-today\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-today\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"true\"\r\n                          >\r\n                            Today\r\n                          </a>\r\n                        </li>\r\n                        <li class=\"nav-item\">\r\n                          <a\r\n                            class=\"nav-link active\"\r\n                            id=\"pills-week\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-week\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"false\"\r\n                          >\r\n                            Week\r\n                          </a>\r\n                        </li>\r\n                        <li class=\"nav-item\">\r\n                          <a\r\n                            class=\"nav-link\"\r\n                            id=\"pills-month\"\r\n                            data-bs-toggle=\"pill\"\r\n                            href=\"#pills-month\"\r\n                            role=\"tab\"\r\n                            aria-selected=\"false\"\r\n                          >\r\n                            Month\r\n                          </a>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"card-body\">\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"avatar avatar-online\">\r\n                      <span class=\"avatar-title rounded-circle border border-white bg-info\">\r\n                        J\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"flex-1 ms-3 pt-1\">\r\n                      <h6 class=\"text-uppercase fw-bold mb-1\">\r\n                        Joko Subianto\r\n                        <span class=\"text-warning ps-3\">pending</span>\r\n                      </h6>\r\n                      <span class=\"text-muted\">\r\n                        I am facing some trouble with my viewport. When i start\r\n                        my\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"float-end pt-1\">\r\n                      <small class=\"text-muted\">8:40 PM</small>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"separator-dashed\"></div>\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"avatar avatar-offline\">\r\n                      <span class=\"avatar-title rounded-circle border border-white bg-secondary\">\r\n                        P\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"flex-1 ms-3 pt-1\">\r\n                      <h6 class=\"text-uppercase fw-bold mb-1\">\r\n                        Prabowo Widodo\r\n                        <span class=\"text-success ps-3\">open</span>\r\n                      </h6>\r\n                      <span class=\"text-muted\">\r\n                        I have some query regarding the license issue.\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"float-end pt-1\">\r\n                      <small class=\"text-muted\">1 Day Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"separator-dashed\"></div>\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"avatar avatar-away\">\r\n                      <span class=\"avatar-title rounded-circle border border-white bg-danger\">\r\n                        L\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"flex-1 ms-3 pt-1\">\r\n                      <h6 class=\"text-uppercase fw-bold mb-1\">\r\n                        Lee Chong Wei\r\n                        <span class=\"text-muted ps-3\">closed</span>\r\n                      </h6>\r\n                      <span class=\"text-muted\">\r\n                        Is there any update plan for RTL version near future?\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"float-end pt-1\">\r\n                      <small class=\"text-muted\">2 Days Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"separator-dashed\"></div>\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"avatar avatar-offline\">\r\n                      <span class=\"avatar-title rounded-circle border border-white bg-secondary\">\r\n                        P\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"flex-1 ms-3 pt-1\">\r\n                      <h6 class=\"text-uppercase fw-bold mb-1\">\r\n                        Peter Parker\r\n                        <span class=\"text-success ps-3\">open</span>\r\n                      </h6>\r\n                      <span class=\"text-muted\">\r\n                        I have some query regarding the license issue.\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"float-end pt-1\">\r\n                      <small class=\"text-muted\">2 Day Ago</small>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"separator-dashed\"></div>\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"avatar avatar-away\">\r\n                      <span class=\"avatar-title rounded-circle border border-white bg-danger\">\r\n                        L\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"flex-1 ms-3 pt-1\">\r\n                      <h6 class=\"text-uppercase fw-bold mb-1\">\r\n                        Logan Paul <span class=\"text-muted ps-3\">closed</span>\r\n                      </h6>\r\n                      <span class=\"text-muted\">\r\n                        Is there any update plan for RTL version near future?\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"float-end pt-1\">\r\n                      <small class=\"text-muted\">2 Days Ago</small>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <footer class=\"footer\">\r\n        <div class=\"container-fluid d-flex justify-content-between\">\r\n          <nav class=\"pull-left\">\r\n            <ul class=\"nav\">\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  TeaM_25\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  {\" \"}\r\n                  Help{\" \"}\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  {\" \"}\r\n                  Licenses{\" \"}\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n          <div class=\"copyright\">\r\n            2024, made with <i class=\"fa fa-heart heart text-danger\"></i> by\r\n            team_25\r\n          </div>\r\n          <div>\r\n            Distributed by\r\n            <a target=\"_blank\" href=\"https://themewagon.com/\">\r\n              team_25\r\n            </a>\r\n            .\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,UAAU,MAAM,kBAAkB;AACzC;AACA,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAEjB,OAAO,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAClB,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACnC,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC;IAC/CuB,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,GAAG;IACtBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC;IAC7C4B,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBN,aAAa,EAAE,IAAI;IACnBO,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC;IAC7CqC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBf,aAAa,EAAE,IAAI;IACnBE,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,KAAK,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EACnE,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM6C,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,EACD;IACEA,IAAI,EAAE,KAAK;IACX,wBAAwB,EAAE,GAAG;IAC7B,gBAAgB,EAAE,GAAG;IACrB,qBAAqB,EAAE;EACzB,CAAC,CACF;;EAED;EACA;EACA;EACA/C,SAAS,CAAC,MAAM;IACd,MAAMgD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI3B,OAAO,EAAE;MACb,MAAM4B,WAAW,GAAG,MAAAA,CAAA,KAAY;QAC9B,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8C,EAC9C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE7B,IAAI,CAAC;UAC7BT,eAAe,CAACS,IAAI,CAAC;QACvB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QACjD;MACF,CAAC;MAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B,IAAI;UACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAC1B,6CAA6C,EAC7C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE7B,IAAI,CAAC;UAC5BJ,cAAc,CAACI,IAAI,CAAC;QACtB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;QAC/B,IAAI;UACF,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAC1B,6CAA6C,EAC7C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7B,IAAI,CAAC;UAC9BK,cAAc,CAACL,IAAI,CAAC;QACtB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;QACtC,IAAI;UACF,MAAMf,QAAQ,GAAG,MAAMC,KAAK,CAC1B,uDAAuD,EACvD;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7B,IAAI,CAAC;UAC9BC,OAAO,CAACD,IAAI,CAAC;QACf,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;QAClC,IAAI;UACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,qDAAqD,EACrD;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UAEA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7B,IAAI,CAAC;UAC9BG,aAAa,CAACH,IAAI,CAAC;QACrB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAMK,WAAW,GAAG,MAAAA,CAAA,KAAY;QAC9B,IAAI;UACF,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8C,EAC9C;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UAED,IAAI,CAAC8B,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAChD;UACA,MAAM1B,IAAI,GAAG,MAAMkB,QAAQ,CAACS,IAAI,CAAC,CAAC;UAClCC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE7B,IAAI,CAAC;UAC7BS,UAAU,CAACT,IAAI,CAAC;QAClB,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MACD,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;QAC/B,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMlB,KAAK,CAC1B,gDAAgD,EAChD;YACEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBpC,IAAI,EAAEA;YACR,CAAC;UACH,CACF,CAAC;UACD,MAAMY,IAAI,GAAG,MAAMqC,QAAQ,CAACV,IAAI,CAAC,CAAC;UAElCd,MAAM,CAACb,IAAI,CAACsC,MAAM,CAAC;QACrB,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC;MAED,MAAMS,OAAO,CAACC,GAAG,CAAC,CAChBvB,WAAW,CAAC,CAAC,EACbc,UAAU,CAAC,CAAC,EACZC,YAAY,CAAC,CAAC,EACdC,mBAAmB,CAAC,CAAC,EACrBC,eAAe,CAAC,CAAC,EACjBC,WAAW,CAAC,CAAC,EACbC,YAAY,CAAC,CAAC,CACf,CAAC;IACJ,CAAC;IAEDpB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,oBACEN,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA;MAAK2D,KAAK,EAAC,WAAW;MAAAD,QAAA,eACpB1D,OAAA;QAAK2D,KAAK,EAAC,YAAY;QAAAD,QAAA,gBACrB1D,OAAA;UAAK2D,KAAK,EAAC,qBAAqB;UAAAD,QAAA,gBAC9B1D,OAAA;YAAK2D,KAAK,EAAC,iBAAiB;YAAAD,QAAA,gBAC1B1D,OAAA;cAAA0D,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB/D,OAAA;cAAA0D,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,mBAAmB;YAAAD,QAAA,gBAC5B1D,OAAA;cAAGgE,IAAI,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtB/D,OAAA;cAAGgE,IAAI,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/D,OAAA;UAAK2D,KAAK,EAAC,oBAAoB;UAAAD,QAAA,gBAC7B1D,OAAA;YAAK2D,KAAK,EAAC,mCAAmC;YAAAD,QAAA,eAC5C1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,eACf1D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAA0D,QAAA,gBACE1D,OAAA;sBAAA0D,QAAA,eACE1D,OAAA;wBACEiE,KAAK,EAAE;0BACLC,UAAU,EAAE,QAAQ;0BACpBC,QAAQ,EAAE,QAAQ;0BAClBC,YAAY,EAAE;wBAChB,CAAE;wBAAAV,QAAA,EACH;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACL/D,OAAA;sBAAG2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACN/D,OAAA;oBAAI2D,KAAK,EAAC,mBAAmB;oBAAAD,QAAA,EAAE9C,WAAW,CAACE;kBAAW;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,sBAAsB;kBAAAD,QAAA,eAC/B1D,OAAA;oBACE2D,KAAK,EAAC,sBAAsB;oBAC5BU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG1D,WAAW,CAACH,aAAa;oBAAG;kBAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChC/D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,GAClB9C,WAAW,CAACH,aAAa,eAC1BT,OAAA;sBAAA0D,QAAA,EAAQ,GAAG,GAAG9C,WAAW,CAACD;oBAAK;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,mCAAmC;YAAAD,QAAA,eAC5C1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,eACf1D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAA0D,QAAA,gBACE1D,OAAA;sBAAA0D,QAAA,eACE1D,OAAA;wBAAA0D,QAAA,EAAG;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACL/D,OAAA;sBAAG2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACN/D,OAAA;oBAAI2D,KAAK,EAAC,sBAAsB;oBAAAD,QAAA,EAC7BnD,YAAY,CAACG;kBAAiB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,sBAAsB;kBAAAD,QAAA,eAC/B1D,OAAA;oBACE2D,KAAK,EAAC,yBAAyB;oBAC/BU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG/D,YAAY,CAACE,aAAa;oBAAG;kBAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChC/D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,GAClBnD,YAAY,CAACE,aAAa,eAC3BT,OAAA;sBAAA0D,QAAA,EAAQ,GAAG,GAAGnD,YAAY,CAACI;oBAAK;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,mCAAmC;YAAAD,QAAA,eAC5C1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,eACf1D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAA0D,QAAA,gBACE1D,OAAA;sBAAA0D,QAAA,eACE1D,OAAA;wBAAA0D,QAAA,EAAG;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACL/D,OAAA;sBAAG2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACN/D,OAAA;oBAAI2D,KAAK,EAAC,qBAAqB;oBAAAD,QAAA,EAAEjC,OAAO,CAACE;kBAAK;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,sBAAsB;kBAAAD,QAAA,eAC/B1D,OAAA;oBACE2D,KAAK,EAAC,wBAAwB;oBAC9BU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAG7C,OAAO,CAACG,OAAO;oBAAG;kBAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChC/D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAEjC,OAAO,CAACG;kBAAO;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,mCAAmC;YAAAD,QAAA,eAC5C1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,eACf1D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAA0D,QAAA,gBACE1D,OAAA;sBAAA0D,QAAA,eACE1D,OAAA;wBAAA0D,QAAA,EAAG;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACL/D,OAAA;sBAAG2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACN/D,OAAA;oBAAI2D,KAAK,EAAC,wBAAwB;oBAAAD,QAAA,EAC/BrC,WAAW,CAACE;kBAAa;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,sBAAsB;kBAAAD,QAAA,eAC/B1D,OAAA;oBACE2D,KAAK,EAAC,2BAA2B;oBACjCU,IAAI,EAAC,aAAa;oBAClBJ,KAAK,EAAE;sBAAEK,KAAK,EAAE,GAAGjD,WAAW,CAACZ,aAAa;oBAAG;kBAAE;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC,gCAAgC;kBAAAD,QAAA,gBACzC1D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChC/D,OAAA;oBAAG2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,GAClBrC,WAAW,CAACZ,aAAa,eAC1BT,OAAA;sBAAA0D,QAAA,EAAQ,GAAG,GAAGrC,WAAW,CAACV;oBAAK;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/D,OAAA;UAAK2D,KAAK,EAAC,oBAAoB;UAAAD,QAAA,gBAC7B1D,OAAA;YAAK2D,KAAK,EAAC,UAAU;YAAAD,QAAA,eACnB1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,gBACf1D,OAAA;gBAAK2D,KAAK,EAAC,aAAa;gBAAAD,QAAA,eACtB1D,OAAA;kBAAK2D,KAAK,EAAC,eAAe;kBAAAD,QAAA,gBACxB1D,OAAA;oBAAK2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjD/D,OAAA;oBAAK2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,gBACrB1D,OAAA;sBACEgE,IAAI,EAAC,GAAG;sBACRL,KAAK,EAAC,6CAA6C;sBAAAD,QAAA,gBAEnD1D,OAAA;wBAAM2D,KAAK,EAAC,WAAW;wBAAAD,QAAA,eACrB1D,OAAA;0BAAG2D,KAAK,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,UAET;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ/D,OAAA;sBAAGgE,IAAI,EAAC,GAAG;sBAACL,KAAK,EAAC,qCAAqC;sBAAAD,QAAA,gBACrD1D,OAAA;wBAAM2D,KAAK,EAAC,WAAW;wBAAAD,QAAA,eACrB1D,OAAA;0BAAG2D,KAAK,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,SAET;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,iBAAiB;kBAACM,KAAK,EAAE;oBAAEM,SAAS,EAAE;kBAAQ,CAAE;kBAAAb,QAAA,eACzD1D,OAAA,CAACF,mBAAmB;oBAACwE,KAAK,EAAC,MAAM;oBAACE,MAAM,EAAE,GAAI;oBAAAd,QAAA,eAC5C1D,OAAA,CAACT,SAAS;sBAAC0B,IAAI,EAAEc,KAAM;sBAAA2B,QAAA,gBACrB1D,OAAA,CAACP,KAAK;wBAACgF,OAAO,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxB/D,OAAA,CAACN,KAAK;wBAACgF,IAAI,EAAC,QAAQ;wBAACC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/C/D,OAAA,CAACL,aAAa;wBAACiF,eAAe,EAAC;sBAAK;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvC/D,OAAA,CAACJ,OAAO;wBAAAgE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACX/D,OAAA,CAACH,MAAM;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACV/D,OAAA,CAACR,IAAI;wBACHkF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,2BAAgB;wBACxBI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,SAAS;wBACdC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACF/D,OAAA,CAACR,IAAI;wBACHkF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,iCAAwB;wBAChCI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,KAAK;wBACVC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACF/D,OAAA,CAACR,IAAI;wBACHkF,IAAI,EAAC,UAAU;wBACfD,OAAO,EAAC,gCAAqB;wBAC7BI,MAAM,EAAC,SAAS;wBAChBC,IAAI,EAAC,SAAS;wBACdC,WAAW,EAAE;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACN/D,OAAA;kBAAKgF,EAAE,EAAC;gBAAe;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,UAAU;YAAAD,QAAA,gBACnB1D,OAAA;cAAK2D,KAAK,EAAC,mBAAmB;cAAAD,QAAA,eAC5B1D,OAAA;gBAAK2D,KAAK,EAAC,mBAAmB;gBAAAD,QAAA,eAC5B1D,OAAA,CAACZ,WAAW;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,eACf1D,OAAA,CAACX,UAAU;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/D,OAAA;UAAK2D,KAAK,EAAC,KAAK;UAAAD,QAAA,gBACd1D,OAAA;YAAK2D,KAAK,EAAC,UAAU;YAAAD,QAAA,eACnB1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,gBACf1D,OAAA;gBAAK2D,KAAK,EAAC,aAAa;gBAAAD,QAAA,eACtB1D,OAAA;kBAAK2D,KAAK,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACN/D,OAAA;gBAAK2D,KAAK,EAAC,eAAe;gBAAAD,QAAA,eACxB1D,OAAA,CAACV,iBAAiB;kBAAC2F,WAAW,EAAC;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8EtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YACE2D,KAAK,EAAC,UAAU;YAChBM,KAAK,EAAE;cACLiB,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,MAAM;cACjBC,YAAY,EAAE;YAChB,CAAE;YAAA1B,QAAA,eAEF1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,gBACf1D,OAAA;gBAAK2D,KAAK,EAAC,aAAa;gBAAAD,QAAA,eACtB1D,OAAA;kBAAK2D,KAAK,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN/D,OAAA;gBAAK2D,KAAK,EAAC,gBAAgB;gBAAAD,QAAA,GACxBvC,UAAU,CAACkE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;kBACxB,IAAIA,CAAC,IAAI,CAAC,EAAE;oBACV,oBACEvF,OAAA,CAAAE,SAAA;sBAAAwD,QAAA,gBACE1D,OAAA;wBAAK2D,KAAK,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpC/D,OAAA;wBAAK2D,KAAK,EAAC,QAAQ;wBAAAD,QAAA,gBACjB1D,OAAA;0BAAK2D,KAAK,EAAC,QAAQ;0BAAAD,QAAA,eACjB1D,OAAA;4BACEwF,GAAG,EACDF,CAAC,CAACG,KAAK,GACHH,CAAC,CAACG,KAAK,CAACC,UAAU,GAClB,+KACL;4BACDC,GAAG,EAAC,KAAK;4BACThC,KAAK,EAAC;0BAA2B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN/D,OAAA;0BAAK2D,KAAK,EAAC,kBAAkB;0BAAAD,QAAA,eAC3B1D,OAAA;4BAAI2D,KAAK,EAAC,cAAc;4BAAAD,QAAA,EAAE4B,CAAC,CAACtD;0BAAI;4BAAA4B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEnC,CAAC,eACN/D,OAAA;0BAAK2D,KAAK,EAAC,mCAAmC;0BAAAD,QAAA,eAC5C1D,OAAA;4BAAI2D,KAAK,EAAC,mBAAmB;4BAAAD,QAAA,EAAE4B,CAAC,CAACM;0BAAI;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,eACN,CAAC;kBAEP;kBACA,oBACE/D,OAAA;oBAAK2D,KAAK,EAAC,SAAS;oBAAAD,QAAA,gBAClB1D,OAAA;sBAAK2D,KAAK,EAAC,QAAQ;sBAAAD,QAAA,eACjB1D,OAAA;wBACEwF,GAAG,EACDF,CAAC,CAACG,KAAK,GACHH,CAAC,CAACG,KAAK,CAACC,UAAU,GAClB,+KACL;wBACDC,GAAG,EAAC,KAAK;wBACThC,KAAK,EAAC;sBAA2B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN/D,OAAA;sBAAK2D,KAAK,EAAC,kBAAkB;sBAAAD,QAAA,eAC3B1D,OAAA;wBAAI2D,KAAK,EAAC,cAAc;wBAAAD,QAAA,EAAE4B,CAAC,CAACtD;sBAAI;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEnC,CAAC,eACN/D,OAAA;sBAAK2D,KAAK,EAAC,mCAAmC;sBAAAD,QAAA,eAC5C1D,OAAA;wBAAI2D,KAAK,EAAC,mBAAmB;wBAAAD,QAAA,EAAE4B,CAAC,CAACM;sBAAI;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAEV,CAAC,CAAC,eAqDF/D,OAAA;kBAAK2D,KAAK,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC/D,OAAA;kBAAK2D,KAAK,EAAC,SAAS;kBAAAD,QAAA,eAClB1D,OAAA;oBAAQgF,EAAE,EAAC;kBAAkB;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/D,OAAA;UAAK2D,KAAK,EAAC,KAAK;UAACM,KAAK,EAAE;YAAE4B,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,gBAC5C1D,OAAA;YAAK2D,KAAK,EAAC,UAAU;YAAAD,QAAA,eACnB1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,gBACf1D,OAAA;gBAAK2D,KAAK,EAAC,aAAa;gBAAAD,QAAA,eACtB1D,OAAA;kBAAK2D,KAAK,EAAC,sCAAsC;kBAAAD,QAAA,gBAC/C1D,OAAA;oBAAK2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7C/D,OAAA;oBAAK2D,KAAK,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BlB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,eACpB1D,OAAA;kBAAI2D,KAAK,EAAC,eAAe;kBAAAD,QAAA,EACtB7B,GAAG,CAACwD,GAAG,CAAExD,GAAG,IAAK;oBAChB,oBACE7B,OAAA;sBAAI2D,KAAK,EAAE,YAAY,GAAG9B,GAAG,CAAC6C,IAAK;sBAAAhB,QAAA,gBACjC1D,OAAA;wBAAM2D,KAAK,EAAC,MAAM;wBAACmC,QAAQ,EAAEjE,GAAG,CAACkE,IAAK;wBAAArC,QAAA,EACnC7B,GAAG,CAACkE;sBAAI;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACP/D,OAAA;wBACE2D,KAAK,EAAC,MAAM;wBACZqC,uBAAuB,EAAE;0BACvBC,MAAM,EAAEpE,GAAG,CAACqE,MAAM,CAAE;wBACtB;sBAAE;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAET,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuDA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAK2D,KAAK,EAAC,UAAU;YAAAD,QAAA,eACnB1D,OAAA;cAAK2D,KAAK,EAAC,MAAM;cAAAD,QAAA,gBACf1D,OAAA;gBAAK2D,KAAK,EAAC,aAAa;gBAAAD,QAAA,eACtB1D,OAAA;kBAAK2D,KAAK,EAAC,eAAe;kBAAAD,QAAA,gBACxB1D,OAAA;oBAAK2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzC/D,OAAA;oBAAK2D,KAAK,EAAC,YAAY;oBAAAD,QAAA,eACrB1D,OAAA;sBACE2D,KAAK,EAAC,oDAAoD;sBAC1DqB,EAAE,EAAC,WAAW;sBACdX,IAAI,EAAC,SAAS;sBAAAX,QAAA,gBAEd1D,OAAA;wBAAI2D,KAAK,EAAC,UAAU;wBAAAD,QAAA,eAClB1D,OAAA;0BACE2D,KAAK,EAAC,UAAU;0BAChBqB,EAAE,EAAC,aAAa;0BAChB,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,cAAc;0BACnBK,IAAI,EAAC,KAAK;0BACV,iBAAc,MAAM;0BAAAX,QAAA,EACrB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL/D,OAAA;wBAAI2D,KAAK,EAAC,UAAU;wBAAAD,QAAA,eAClB1D,OAAA;0BACE2D,KAAK,EAAC,iBAAiB;0BACvBqB,EAAE,EAAC,YAAY;0BACf,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,aAAa;0BAClBK,IAAI,EAAC,KAAK;0BACV,iBAAc,OAAO;0BAAAX,QAAA,EACtB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eACL/D,OAAA;wBAAI2D,KAAK,EAAC,UAAU;wBAAAD,QAAA,eAClB1D,OAAA;0BACE2D,KAAK,EAAC,UAAU;0BAChBqB,EAAE,EAAC,aAAa;0BAChB,kBAAe,MAAM;0BACrBhB,IAAI,EAAC,cAAc;0BACnBK,IAAI,EAAC,KAAK;0BACV,iBAAc,OAAO;0BAAAX,QAAA,EACtB;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/D,OAAA;gBAAK2D,KAAK,EAAC,WAAW;gBAAAD,QAAA,gBACpB1D,OAAA;kBAAK2D,KAAK,EAAC,QAAQ;kBAAAD,QAAA,gBACjB1D,OAAA;oBAAK2D,KAAK,EAAC,sBAAsB;oBAAAD,QAAA,eAC/B1D,OAAA;sBAAM2D,KAAK,EAAC,yDAAyD;sBAAAD,QAAA,EAAC;oBAEtE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,kBAAkB;oBAAAD,QAAA,gBAC3B1D,OAAA;sBAAI2D,KAAK,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,eAEtC,eAAA1D,OAAA;wBAAM2D,KAAK,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACL/D,OAAA;sBAAM2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAGzB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,gBAAgB;oBAAAD,QAAA,eACzB1D,OAAA;sBAAO2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC/D,OAAA;kBAAK2D,KAAK,EAAC,QAAQ;kBAAAD,QAAA,gBACjB1D,OAAA;oBAAK2D,KAAK,EAAC,uBAAuB;oBAAAD,QAAA,eAChC1D,OAAA;sBAAM2D,KAAK,EAAC,8DAA8D;sBAAAD,QAAA,EAAC;oBAE3E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,kBAAkB;oBAAAD,QAAA,gBAC3B1D,OAAA;sBAAI2D,KAAK,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,gBAEtC,eAAA1D,OAAA;wBAAM2D,KAAK,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,eACL/D,OAAA;sBAAM2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAEzB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,gBAAgB;oBAAAD,QAAA,eACzB1D,OAAA;sBAAO2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC/D,OAAA;kBAAK2D,KAAK,EAAC,QAAQ;kBAAAD,QAAA,gBACjB1D,OAAA;oBAAK2D,KAAK,EAAC,oBAAoB;oBAAAD,QAAA,eAC7B1D,OAAA;sBAAM2D,KAAK,EAAC,2DAA2D;sBAAAD,QAAA,EAAC;oBAExE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,kBAAkB;oBAAAD,QAAA,gBAC3B1D,OAAA;sBAAI2D,KAAK,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,eAEtC,eAAA1D,OAAA;wBAAM2D,KAAK,EAAC,iBAAiB;wBAAAD,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,eACL/D,OAAA;sBAAM2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAEzB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,gBAAgB;oBAAAD,QAAA,eACzB1D,OAAA;sBAAO2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC/D,OAAA;kBAAK2D,KAAK,EAAC,QAAQ;kBAAAD,QAAA,gBACjB1D,OAAA;oBAAK2D,KAAK,EAAC,uBAAuB;oBAAAD,QAAA,eAChC1D,OAAA;sBAAM2D,KAAK,EAAC,8DAA8D;sBAAAD,QAAA,EAAC;oBAE3E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,kBAAkB;oBAAAD,QAAA,gBAC3B1D,OAAA;sBAAI2D,KAAK,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,cAEtC,eAAA1D,OAAA;wBAAM2D,KAAK,EAAC,mBAAmB;wBAAAD,QAAA,EAAC;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,eACL/D,OAAA;sBAAM2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAEzB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,gBAAgB;oBAAAD,QAAA,eACzB1D,OAAA;sBAAO2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/D,OAAA;kBAAK2D,KAAK,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpC/D,OAAA;kBAAK2D,KAAK,EAAC,QAAQ;kBAAAD,QAAA,gBACjB1D,OAAA;oBAAK2D,KAAK,EAAC,oBAAoB;oBAAAD,QAAA,eAC7B1D,OAAA;sBAAM2D,KAAK,EAAC,2DAA2D;sBAAAD,QAAA,EAAC;oBAExE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,kBAAkB;oBAAAD,QAAA,gBAC3B1D,OAAA;sBAAI2D,KAAK,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,aAC3B,eAAA1D,OAAA;wBAAM2D,KAAK,EAAC,iBAAiB;wBAAAD,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACL/D,OAAA;sBAAM2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAEzB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAK2D,KAAK,EAAC,gBAAgB;oBAAAD,QAAA,eACzB1D,OAAA;sBAAO2D,KAAK,EAAC,YAAY;sBAAAD,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/D,OAAA;MAAQ2D,KAAK,EAAC,QAAQ;MAAAD,QAAA,eACpB1D,OAAA;QAAK2D,KAAK,EAAC,gDAAgD;QAAAD,QAAA,gBACzD1D,OAAA;UAAK2D,KAAK,EAAC,WAAW;UAAAD,QAAA,eACpB1D,OAAA;YAAI2D,KAAK,EAAC,KAAK;YAAAD,QAAA,gBACb1D,OAAA;cAAI2D,KAAK,EAAC,UAAU;cAAAD,QAAA,eAClB1D,OAAA;gBAAG2D,KAAK,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,EAAC;cAE7B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACL/D,OAAA;cAAI2D,KAAK,EAAC,UAAU;cAAAD,QAAA,eAClB1D,OAAA;gBAAG2D,KAAK,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,GACzB,GAAG,EAAC,MACD,EAAC,GAAG;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACL/D,OAAA;cAAI2D,KAAK,EAAC,UAAU;cAAAD,QAAA,eAClB1D,OAAA;gBAAG2D,KAAK,EAAC,UAAU;gBAACK,IAAI,EAAC,GAAG;gBAAAN,QAAA,GACzB,GAAG,EAAC,UACG,EAAC,GAAG;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN/D,OAAA;UAAK2D,KAAK,EAAC,WAAW;UAAAD,QAAA,GAAC,kBACL,eAAA1D,OAAA;YAAG2D,KAAK,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE/D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/D,OAAA;UAAA0D,QAAA,GAAK,gBAEH,eAAA1D,OAAA;YAAGmG,MAAM,EAAC,QAAQ;YAACnC,IAAI,EAAC,yBAAyB;YAAAN,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,KAEN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACT,CAAC;AAEP;AAAC3D,EAAA,CApgCQD,IAAI;EAAA,QACehB,OAAO;AAAA;AAAAiH,EAAA,GAD1BjG,IAAI;AAsgCb,eAAeA,IAAI;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}