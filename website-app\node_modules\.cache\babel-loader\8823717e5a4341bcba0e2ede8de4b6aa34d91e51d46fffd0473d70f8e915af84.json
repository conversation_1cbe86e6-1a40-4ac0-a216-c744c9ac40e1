{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\ManageAccount\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from \"react\";\nimport \"./ManageAccount.css\";\nimport { getRoles } from \"../../services/Roles/rolesService\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useNavigate } from \"react-router-dom\";\n\n// Icons\nimport { FaSearch, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaTimes, FaCheck, FaUser, FaEnvelope, FaLock, FaUserTag, FaCode, FaRefreshCw, FaSort, FaSortUp, FaSortDown, Fa<PERSON>ilter, Fa<PERSON><PERSON>s, FaShieldAlt } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManageAccount = () => {\n  _s();\n  // State management\n  const [accounts, setAccounts] = useState([]);\n  const [rolesData, setRolesData] = useState([]);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: \"asc\"\n  });\n  const [filterRole, setFilterRole] = useState(\"\");\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState(null);\n\n  // Form states\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    role: \"\",\n    code: \"\"\n  });\n  const [confirmOtp, setConfirmOtp] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n\n  // Hooks\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const dropdownRef = useRef(null);\n\n  // API Functions\n  const getAccounts = useCallback(async userId => {\n    if (!userId) {\n      console.error(\"Lỗi: userId không hợp lệ!\");\n      return;\n    }\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/list?userId=${userId}`, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Network response was not ok: ${response.statusText}`);\n      }\n      const data = await response.json();\n      setAccounts(data);\n    } catch (error) {\n      console.error(\"Lỗi khi gọi API:\", error);\n      notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\n    } finally {\n      stopLoading();\n    }\n  }, [startLoading, stopLoading]);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setShowMenuIndex(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const fetchRoles = async () => {\n      if (user) {\n        startLoading();\n        await getAccounts(user.id_owner);\n        const roles = await getRoles(user.id_owner);\n        setRolesData(roles);\n        stopLoading();\n        setFormData(prevData => ({\n          ...prevData,\n          id_owner: user._id\n        })); // cập nhật id_owner\n      }\n    };\n    fetchRoles();\n  }, [user]);\n  const toggleMenu = index => {\n    setShowMenuIndex(showMenuIndex === index ? null : index);\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const filteredAccounts = accounts.filter(account => {\n    const name = account.name ? account.name.toLowerCase() : \"\";\n    const email = account.email ? account.email.toLowerCase() : \"\";\n    const role = account.role ? account.role.toLowerCase() : \"\";\n    return name.includes(searchTerm.toLowerCase()) || email.includes(searchTerm.toLowerCase()) || role.includes(searchTerm.toLowerCase());\n  });\n  const handleCreateAccount = async e => {\n    e.preventDefault();\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: formData.role,\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      console.log(data);\n      if (confirmOtp) {\n        if (data.message === \"Staff is created successfully\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\n        }\n      } else {\n        if (data.message === \"Confirmation code sent\") {\n          setConfirmOtp(true);\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n        } else if (data.message === \"User_new updated successfully!\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const sentAgain = async () => {\n    setConfirmOtp(true);\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: user ? user.role : \"\",\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      // Khi gửi mã xác nhận\n      if (data.message === \"Confirmation code sent\") {\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\n        setFormData(prev => ({\n          ...prev,\n          code: \"\"\n        }));\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n      } else {\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const handleOpenEditModal = account => {\n    setFormData({\n      id: account._id,\n      name: account.name,\n      email: account.email,\n      role: account.role,\n      password: account.password // Assuming password can be left blank for editing\n    });\n    setShowEditModal(true);\n  };\n  const handleEditAccount = async e => {\n    e.preventDefault();\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${formData.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          role: formData.role\n        })\n      });\n      const data = await response.json();\n      console.log(\"Success:\", data);\n      stopLoading();\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\n      await getAccounts(user.id_owner);\n      setShowModal(false);\n    } catch (error) {\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\n      console.error(\"Error edit:\", error);\n    }\n  };\n\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\n  const handleDeleteAccount = async accountId => {\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/user/${accountId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Failed to delete account: ${response.statusText}`);\n      }\n      if (user._id === accountId) {\n        logout();\n        navigate(\"/\");\n      } else {\n        await getAccounts(user.id_owner);\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting account:\", error);\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\n    } finally {\n      stopLoading();\n      setShowDeleteModal(false);\n      setSelectedAccount(null);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-table\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Qu\\u1EA3n l\\xED t\\xE0i kho\\u1EA3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"uy-search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"search-input\",\n          placeholder: \"Search for...\",\n          value: searchTerm,\n          onChange: handleSearchChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"create-order-btn\",\n          onClick: () => setShowModal(true),\n          children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleCreateAccount,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: \"10px\"\n            },\n            children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this), confirmOtp && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"code\",\n              placeholder: \"\\u0110i\\u1EC1n m\\xE3 x\\xE1c nh\\u1EADn \",\n              value: formData.code,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"uy-sentagain\",\n              onClick: sentAgain,\n              children: \"G\\u1EEDi l\\u1EA1i m\\xE3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 9\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowEditModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleEditAccount,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Edit Staff Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowEditModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\u1ECD T\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ph\\xE2n Quy\\u1EC1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tr\\u1EA1ng Th\\xE1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"L\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\xE0nh \\u0110\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredAccounts.map(account => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${account.status ? account.status.toLowerCase() : \"active\"}`,\n              children: account.status || \"Acctive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.salary || \"N/A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uy-action\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleMenu(account._id),\n                className: \"menu-btn\",\n                children: \"\\u22EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), showMenuIndex === account._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uy-dropdown-menu\",\n                ref: dropdownRef,\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleOpenEditModal(account),\n                    children: \"Ch\\u1EC9nh s\\u1EEDa\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleDeleteAccount(account._id),\n                    children: \"X\\xF3a\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)]\n        }, account._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        marginBottom: \"5px\",\n        color: \"red\"\n      },\n      children: \"L\\u01B0u \\xFD n\\u1EBFu s\\u1EEDa quy\\u1EC1n c\\u1EE7a Admin sang m\\u1ED9t quy\\u1EC1n kh\\xE1c m\\xE0 kh\\xF4ng bao g\\u1ED3m (\\\"*role\\\") b\\u1EA1n s\\u1EBD kh\\xF4ng th\\u1EC3 ph\\xE2n quy\\u1EC1n n\\u1EEFa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"deleteAccountBtn\",\n      onClick: () => handleDeleteAccount(user._id),\n      children: \"X\\xF3a T\\xE0i Kho\\u1EA3n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageAccount, \"YEPjCGWO4eOxu8ig+k3ZE2Td7Kw=\", false, function () {\n  return [useLoading, useAuth, useNavigate];\n});\n_c = ManageAccount;\nexport default ManageAccount;\nvar _c;\n$RefreshReg$(_c, \"ManageAccount\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "getRoles", "useAuth", "useLoading", "notify", "useNavigate", "FaSearch", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaEyeSlash", "FaTimes", "FaCheck", "FaUser", "FaEnvelope", "FaLock", "FaUserTag", "FaCode", "FaRefreshCw", "FaSort", "FaSortUp", "FaSortDown", "FaFilter", "FaUsers", "FaShieldAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManageAccount", "_s", "accounts", "setAccounts", "rolesData", "setRolesData", "searchTerm", "setSearchTerm", "sortConfig", "setSortConfig", "key", "direction", "filterRole", "setFilterRole", "showMenuIndex", "setShowMenuIndex", "showModal", "setShowModal", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedAccount", "setSelectedAccount", "formData", "setFormData", "name", "email", "password", "role", "code", "confirmOtp", "setConfirmOtp", "showPassword", "setShowPassword", "formErrors", "setFormErrors", "startLoading", "stopLoading", "user", "logout", "navigate", "dropdownRef", "getAccounts", "userId", "console", "error", "response", "fetch", "method", "headers", "Authorization", "localStorage", "getItem", "ok", "Error", "statusText", "data", "json", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "fetchRoles", "id_owner", "roles", "prevData", "_id", "toggleMenu", "index", "handleSearchChange", "e", "value", "filteredAccounts", "filter", "account", "toLowerCase", "includes", "handleCreateAccount", "preventDefault", "dataUser", "id", "body", "JSON", "stringify", "log", "message", "sentAgain", "prev", "handleOpenEditModal", "handleEditAccount", "handleDeleteAccount", "accountId", "handleInputChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "style", "marginBottom", "required", "disabled", "map", "status", "salary", "ref", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/ManageAccount/index.js"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from \"react\";\r\nimport \"./ManageAccount.css\";\r\nimport { getRoles } from \"../../services/Roles/rolesService\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// Icons\r\nimport {\r\n  FaSearch,\r\n  FaPlus,\r\n  FaEdit,\r\n  FaTrash,\r\n  FaEye,\r\n  FaEyeSlash,\r\n  FaTimes,\r\n  FaCheck,\r\n  FaUser,\r\n  FaEnvelope,\r\n  FaLock,\r\n  FaUserTag,\r\n  FaCode,\r\n  FaRefreshCw,\r\n  FaSort,\r\n  FaSortUp,\r\n  FaSortDown,\r\n  FaFilter,\r\n  FaUsers,\r\n  FaShieldAlt,\r\n} from \"react-icons/fa\";\r\n\r\nconst ManageAccount = () => {\r\n  // State management\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [rolesData, setRolesData] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: \"asc\" });\r\n  const [filterRole, setFilterRole] = useState(\"\");\r\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false); // ✅ Thêm showModal state\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [selectedAccount, setSelectedAccount] = useState(null);\r\n\r\n  // Form states\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    role: \"\",\r\n    code: \"\",\r\n  });\r\n  const [confirmOtp, setConfirmOtp] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [formErrors, setFormErrors] = useState({});\r\n\r\n  // Hooks\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, logout } = useAuth();\r\n  const navigate = useNavigate();\r\n  const dropdownRef = useRef(null);\r\n\r\n  // API Functions\r\n  const getAccounts = useCallback(\r\n    async (userId) => {\r\n      if (!userId) {\r\n        console.error(\"Lỗi: userId không hợp lệ!\");\r\n        return;\r\n      }\r\n\r\n      try {\r\n        startLoading();\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/user/list?userId=${userId}`,\r\n          {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\r\n            `Network response was not ok: ${response.statusText}`\r\n          );\r\n        }\r\n\r\n        const data = await response.json();\r\n        setAccounts(data);\r\n      } catch (error) {\r\n        console.error(\"Lỗi khi gọi API:\", error);\r\n        notify(2, \"Không thể tải danh sách tài khoản\", \"Lỗi\");\r\n      } finally {\r\n        stopLoading();\r\n      }\r\n    },\r\n    [startLoading, stopLoading]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setShowMenuIndex(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchRoles = async () => {\r\n      if (user) {\r\n        startLoading();\r\n        await getAccounts(user.id_owner);\r\n        const roles = await getRoles(user.id_owner);\r\n        setRolesData(roles);\r\n        stopLoading();\r\n        setFormData((prevData) => ({ ...prevData, id_owner: user._id })); // cập nhật id_owner\r\n      }\r\n    };\r\n    fetchRoles();\r\n  }, [user]);\r\n\r\n  const toggleMenu = (index) => {\r\n    setShowMenuIndex(showMenuIndex === index ? null : index);\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  const filteredAccounts = accounts.filter((account) => {\r\n    const name = account.name ? account.name.toLowerCase() : \"\";\r\n    const email = account.email ? account.email.toLowerCase() : \"\";\r\n    const role = account.role ? account.role.toLowerCase() : \"\";\r\n\r\n    return (\r\n      name.includes(searchTerm.toLowerCase()) ||\r\n      email.includes(searchTerm.toLowerCase()) ||\r\n      role.includes(searchTerm.toLowerCase())\r\n    );\r\n  });\r\n\r\n  const handleCreateAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: formData.role,\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/create\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      console.log(data);\r\n\r\n      if (confirmOtp) {\r\n        if (data.message === \"Staff is created successfully\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\r\n        }\r\n      } else {\r\n        if (data.message === \"Confirmation code sent\") {\r\n          setConfirmOtp(true);\r\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n        } else if (data.message === \"User_new updated successfully!\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const sentAgain = async () => {\r\n    setConfirmOtp(true);\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: user ? user.role : \"\",\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\"http://localhost:8080/api/user/resend\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n        },\r\n        body: JSON.stringify({ dataUser, user }),\r\n      });\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      // Khi gửi mã xác nhận\r\n      if (data.message === \"Confirmation code sent\") {\r\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\r\n        setFormData((prev) => ({ ...prev, code: \"\" }));\r\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n      } else {\r\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleOpenEditModal = (account) => {\r\n    setFormData({\r\n      id: account._id,\r\n      name: account.name,\r\n      email: account.email,\r\n      role: account.role,\r\n      password: account.password, // Assuming password can be left blank for editing\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const handleEditAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${formData.id}`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n          body: JSON.stringify({\r\n            name: formData.name,\r\n            email: formData.email,\r\n            password: formData.password,\r\n            role: formData.role,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      console.log(\"Success:\", data);\r\n      stopLoading();\r\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\r\n      await getAccounts(user.id_owner);\r\n      setShowModal(false);\r\n    } catch (error) {\r\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\r\n      console.error(\"Error edit:\", error);\r\n    }\r\n  };\r\n\r\n  // ✅ Handle delete account sử dụng DELETE /api/user/{id}\r\n  const handleDeleteAccount = async (accountId) => {\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/user/${accountId}`,\r\n        {\r\n          method: \"DELETE\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${localStorage.getItem(\"token\")}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to delete account: ${response.statusText}`);\r\n      }\r\n\r\n      if (user._id === accountId) {\r\n        logout();\r\n        navigate(\"/\");\r\n      } else {\r\n        await getAccounts(user.id_owner);\r\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting account:\", error);\r\n      notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\r\n    } finally {\r\n      stopLoading();\r\n      setShowDeleteModal(false);\r\n      setSelectedAccount(null);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  return (\r\n    <div className=\"account-table\">\r\n      <div className=\"account-header\">\r\n        <h2>Quản lí tài khoản</h2>\r\n        <div className=\"uy-search-container\">\r\n          <input\r\n            type=\"text\"\r\n            className=\"search-input\"\r\n            placeholder=\"Search for...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n          />\r\n          <button\r\n            className=\"create-order-btn\"\r\n            onClick={() => setShowModal(true)}\r\n          >\r\n            Tạo tài khoản nhân viên\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {showModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button className=\"close-modal\" onClick={() => setShowModal(false)}>\r\n              ✖\r\n            </button>\r\n            <form\r\n              className=\"create-account-form\"\r\n              onSubmit={handleCreateAccount}\r\n            >\r\n              <h3 style={{ marginBottom: \"10px\" }}>Tạo tài khoản nhân viên</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n\r\n              {confirmOtp && (\r\n                <>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"code\"\r\n                    placeholder=\"Điền mã xác nhận \"\r\n                    value={formData.code}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                  />\r\n                  <p className=\"uy-sentagain\" onClick={sentAgain}>\r\n                    Gửi lại mã\r\n                  </p>\r\n                </>\r\n              )}\r\n\r\n              <button type=\"submit\">\r\n                {confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"}\r\n              </button>\r\n              <button type=\"button\" onClick={() => setShowModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showEditModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button\r\n              className=\"close-modal\"\r\n              onClick={() => setShowEditModal(false)}\r\n            >\r\n              ✖\r\n            </button>\r\n            <form className=\"create-account-form\" onSubmit={handleEditAccount}>\r\n              {\" \"}\r\n              {/* Changed class name here */}\r\n              <h3>Edit Staff Account</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <button type=\"submit\">Submit</button>\r\n              <button type=\"button\" onClick={() => setShowEditModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <table>\r\n        <thead>\r\n          <tr>\r\n            <th>Họ Tên</th>\r\n            <th>Phân Quyền</th>\r\n            <th>Email</th>\r\n            <th>Trạng Thái</th>\r\n            <th>Lương</th>\r\n            <th>Hành Động</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredAccounts.map((account) => (\r\n            <tr key={account._id}>\r\n              <td>{account.name}</td>\r\n              <td>{account.role}</td>\r\n              <td>{account.email}</td>\r\n              <td>\r\n                <span\r\n                  className={`status ${\r\n                    account.status ? account.status.toLowerCase() : \"active\"\r\n                  }`}\r\n                >\r\n                  {account.status || \"Acctive\"}\r\n                </span>\r\n              </td>\r\n              <td>{account.salary || \"N/A\"}</td>\r\n              <td>\r\n                <div className=\"uy-action\">\r\n                  <button\r\n                    onClick={() => toggleMenu(account._id)}\r\n                    className=\"menu-btn\"\r\n                  >\r\n                    ⋮\r\n                  </button>\r\n                  {showMenuIndex === account._id && (\r\n                    <div className=\"uy-dropdown-menu\" ref={dropdownRef}>\r\n                      <ul>\r\n                        <li onClick={() => handleOpenEditModal(account)}>\r\n                          Chỉnh sửa\r\n                        </li>\r\n                        <li onClick={() => handleDeleteAccount(account._id)}>\r\n                          Xóa\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      <p style={{ marginBottom: \"5px\", color: \"red\" }}>\r\n        Lưu ý nếu sửa quyền của Admin sang một quyền khác mà không bao gồm\r\n        (\"*role\") bạn sẽ không thể phân quyền nữa\r\n      </p>\r\n      <button\r\n        className=\"deleteAccountBtn\"\r\n        onClick={() => handleDeleteAccount(user._id)}\r\n      >\r\n        Xóa Tài Khoản\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ManageAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,SACEC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC;IAAEyC,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM;IAAEsE,YAAY;IAAEC;EAAY,CAAC,GAAGnE,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEoE,IAAI;IAAEC;EAAO,CAAC,GAAGtE,OAAO,CAAC,CAAC;EAClC,MAAMuE,QAAQ,GAAGpE,WAAW,CAAC,CAAC;EAC9B,MAAMqE,WAAW,GAAG5E,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAM6E,WAAW,GAAG3E,WAAW,CAC7B,MAAO4E,MAAM,IAAK;IAChB,IAAI,CAACA,MAAM,EAAE;MACXC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC1C;IACF;IAEA,IAAI;MACFT,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8CJ,MAAM,EAAE,EACtD;QACEK,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CACb,gCAAgCR,QAAQ,CAACS,UAAU,EACrD,CAAC;MACH;MAEA,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCzD,WAAW,CAACwD,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC1E,MAAM,CAAC,CAAC,EAAE,mCAAmC,EAAE,KAAK,CAAC;IACvD,CAAC,SAAS;MACRkE,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EACD,CAACD,YAAY,EAAEC,WAAW,CAC5B,CAAC;EAEDzE,SAAS,CAAC,MAAM;IACd,MAAM8F,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIlB,WAAW,CAACmB,OAAO,IAAI,CAACnB,WAAW,CAACmB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtElD,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC;IAEDmD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAE1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN9F,SAAS,CAAC,MAAM;IACd,MAAMsG,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI5B,IAAI,EAAE;QACRF,YAAY,CAAC,CAAC;QACd,MAAMM,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;QAChC,MAAMC,KAAK,GAAG,MAAMpG,QAAQ,CAACsE,IAAI,CAAC6B,QAAQ,CAAC;QAC3CjE,YAAY,CAACkE,KAAK,CAAC;QACnB/B,WAAW,CAAC,CAAC;QACbb,WAAW,CAAE6C,QAAQ,KAAM;UAAE,GAAGA,QAAQ;UAAEF,QAAQ,EAAE7B,IAAI,CAACgC;QAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE;IACF,CAAC;IACDJ,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,MAAMiC,UAAU,GAAIC,KAAK,IAAK;IAC5B5D,gBAAgB,CAACD,aAAa,KAAK6D,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChCtE,aAAa,CAACsE,CAAC,CAACZ,MAAM,CAACa,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,gBAAgB,GAAG7E,QAAQ,CAAC8E,MAAM,CAAEC,OAAO,IAAK;IACpD,MAAMrD,IAAI,GAAGqD,OAAO,CAACrD,IAAI,GAAGqD,OAAO,CAACrD,IAAI,CAACsD,WAAW,CAAC,CAAC,GAAG,EAAE;IAC3D,MAAMrD,KAAK,GAAGoD,OAAO,CAACpD,KAAK,GAAGoD,OAAO,CAACpD,KAAK,CAACqD,WAAW,CAAC,CAAC,GAAG,EAAE;IAC9D,MAAMnD,IAAI,GAAGkD,OAAO,CAAClD,IAAI,GAAGkD,OAAO,CAAClD,IAAI,CAACmD,WAAW,CAAC,CAAC,GAAG,EAAE;IAE3D,OACEtD,IAAI,CAACuD,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC,IACvCrD,KAAK,CAACsD,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC,IACxCnD,IAAI,CAACoD,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC;EAE3C,CAAC,CAAC;EAEF,MAAME,mBAAmB,GAAG,MAAOP,CAAC,IAAK;IACvCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE9C,IAAI,GAAGA,IAAI,CAAC8C,EAAE,GAAG,EAAE;QACvBxD,IAAI,EAAEL,QAAQ,CAACK,IAAI;QACnBuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;QACnCzC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBK,UAAU,EAAEA,UAAU;QACtBD,IAAI,EAAEN,QAAQ,CAACM;MACjB,CAAC;MACDO,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAE7C;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCpB,WAAW,CAAC,CAAC;MACbO,OAAO,CAAC4C,GAAG,CAAChC,IAAI,CAAC;MAEjB,IAAI1B,UAAU,EAAE;QACd,IAAI0B,IAAI,CAACiC,OAAO,KAAK,+BAA+B,EAAE;UACpDtH,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDqD,WAAW,CAAC;YACV4D,EAAE,EAAE9C,IAAI,GAAGA,IAAI,CAAC8C,EAAE,GAAG,EAAE;YACvB3D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;YACnCtC,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBjB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM4B,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLhG,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACiC,OAAO,IAAI,iBAAiB,EAAE,UAAU,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIjC,IAAI,CAACiC,OAAO,KAAK,wBAAwB,EAAE;UAC7C1D,aAAa,CAAC,IAAI,CAAC;UACnB5D,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;QACpD,CAAC,MAAM,IAAIqF,IAAI,CAACiC,OAAO,KAAK,gCAAgC,EAAE;UAC5DtH,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnDqD,WAAW,CAAC;YACV4D,EAAE,EAAE9C,IAAI,GAAGA,IAAI,CAAC8C,EAAE,GAAG,EAAE;YACvB3D,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;YACnCtC,IAAI,EAAE;UACR,CAAC,CAAC;UACFE,aAAa,CAAC,KAAK,CAAC;UACpBjB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAM4B,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLhG,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACiC,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;QACpE;MACF;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAM6C,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B3D,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMoD,QAAQ,GAAG;QACfC,EAAE,EAAE9C,IAAI,GAAGA,IAAI,CAAC8C,EAAE,GAAG,EAAE;QACvBxD,IAAI,EAAEU,IAAI,GAAGA,IAAI,CAACV,IAAI,GAAG,EAAE;QAC3BuC,QAAQ,EAAE7B,IAAI,GAAGA,IAAI,CAAC6B,QAAQ,GAAG,EAAE;QACnCzC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BF,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBK,UAAU,EAAEA,UAAU;QACtBD,IAAI,EAAEN,QAAQ,CAACM;MACjB,CAAC;MACDO,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEJ,QAAQ;UAAE7C;QAAK,CAAC;MACzC,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCpB,WAAW,CAAC,CAAC;MACb;MACA,IAAImB,IAAI,CAACiC,OAAO,KAAK,wBAAwB,EAAE;QAC7C1D,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QACrBP,WAAW,CAAEmE,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE9D,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;QAC9C1D,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;MACpD,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAEqF,IAAI,CAACiC,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;MACpE;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAM+C,mBAAmB,GAAId,OAAO,IAAK;IACvCtD,WAAW,CAAC;MACV4D,EAAE,EAAEN,OAAO,CAACR,GAAG;MACf7C,IAAI,EAAEqD,OAAO,CAACrD,IAAI;MAClBC,KAAK,EAAEoD,OAAO,CAACpD,KAAK;MACpBE,IAAI,EAAEkD,OAAO,CAAClD,IAAI;MAClBD,QAAQ,EAAEmD,OAAO,CAACnD,QAAQ,CAAE;IAC9B,CAAC,CAAC;IACFT,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM2E,iBAAiB,GAAG,MAAOnB,CAAC,IAAK;IACrCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF9C,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCxB,QAAQ,CAAC6D,EAAE,EAAE,EAC/C;QACEpC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB9D,IAAI,EAAEF,QAAQ,CAACE,IAAI;UACnBC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;UAC3BC,IAAI,EAAEL,QAAQ,CAACK;QACjB,CAAC;MACH,CACF,CAAC;MAED,MAAM4B,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCb,OAAO,CAAC4C,GAAG,CAAC,UAAU,EAAEhC,IAAI,CAAC;MAC7BnB,WAAW,CAAC,CAAC;MACblE,MAAM,CAAC,CAAC,EAAE,gCAAgC,EAAE,YAAY,CAAC;MACzD,MAAMuE,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;MAChCrD,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACd1E,MAAM,CAAC,CAAC,EAAE,8BAA8B,EAAE,UAAU,CAAC;MACrDyE,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMiD,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACF3D,YAAY,CAAC,CAAC;MACd,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kCAAkCgD,SAAS,EAAE,EAC7C;QACE/C,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACxD;MACF,CACF,CAAC;MAED,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6BR,QAAQ,CAACS,UAAU,EAAE,CAAC;MACrE;MAEA,IAAIjB,IAAI,CAACgC,GAAG,KAAKyB,SAAS,EAAE;QAC1BxD,MAAM,CAAC,CAAC;QACRC,QAAQ,CAAC,GAAG,CAAC;MACf,CAAC,MAAM;QACL,MAAME,WAAW,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;QAChChG,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;MACrD;IACF,CAAC,CAAC,OAAO0E,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C1E,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;IACjD,CAAC,SAAS;MACRkE,WAAW,CAAC,CAAC;MACbjB,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM0E,iBAAiB,GAAItB,CAAC,IAAK;IAC/B,MAAM;MAAEjD,IAAI;MAAEkD;IAAM,CAAC,GAAGD,CAAC,CAACZ,MAAM;IAChCtC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGkD;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,oBACEjF,OAAA;IAAKuG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxG,OAAA;MAAKuG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxG,OAAA;QAAAwG,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B5G,OAAA;QAAKuG,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCxG,OAAA;UACE6G,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,cAAc;UACxBO,WAAW,EAAC,eAAe;UAC3B7B,KAAK,EAAExE,UAAW;UAClBsG,QAAQ,EAAEhC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF5G,OAAA;UACEuG,SAAS,EAAC,kBAAkB;UAC5BS,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,IAAI,CAAE;UAAAoF,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzF,SAAS,iBACRnB,OAAA;MAAKuG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxG,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxG,OAAA;UAAQuG,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,KAAK,CAAE;UAAAoF,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5G,OAAA;UACEuG,SAAS,EAAC,qBAAqB;UAC/BU,QAAQ,EAAE1B,mBAAoB;UAAAiB,QAAA,gBAE9BxG,OAAA;YAAIkH,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE5G,OAAA;YACE6G,IAAI,EAAC,MAAM;YACX9E,IAAI,EAAC,MAAM;YACX+E,WAAW,EAAC,WAAW;YACvB7B,KAAK,EAAEpD,QAAQ,CAACE,IAAK;YACrBgF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5G,OAAA;YACE6G,IAAI,EAAC,OAAO;YACZ9E,IAAI,EAAC,OAAO;YACZ+E,WAAW,EAAC,OAAO;YACnB7B,KAAK,EAAEpD,QAAQ,CAACG,KAAM;YACtB+E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5G,OAAA;YACE6G,IAAI,EAAC,UAAU;YACf9E,IAAI,EAAC,UAAU;YACf+E,WAAW,EAAC,UAAU;YACtB7B,KAAK,EAAEpD,QAAQ,CAACI,QAAS;YACzB8E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5G,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXkD,KAAK,EAAEpD,QAAQ,CAACK,IAAK;YACrB6E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAERxG,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAACoC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRrG,SAAS,CAAC+G,GAAG,CAAEpF,IAAI,iBAClBlC,OAAA;cAAuBiF,KAAK,EAAE/C,IAAI,CAACA,IAAK;cAAAsE,QAAA,EACrCtE,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC0C,GAAG;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAERxE,UAAU,iBACTpC,OAAA,CAAAE,SAAA;YAAAsG,QAAA,gBACExG,OAAA;cACE6G,IAAI,EAAC,MAAM;cACX9E,IAAI,EAAC,MAAM;cACX+E,WAAW,EAAC,wCAAmB;cAC/B7B,KAAK,EAAEpD,QAAQ,CAACM,IAAK;cACrB4E,QAAQ,EAAET,iBAAkB;cAC5Bc,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF5G,OAAA;cAAGuG,SAAS,EAAC,cAAc;cAACS,OAAO,EAAEhB,SAAU;cAAAQ,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CACH,eAED5G,OAAA;YAAQ6G,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAClBpE,UAAU,GAAG,2BAA2B,GAAG;UAAY;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACT5G,OAAA;YAAQ6G,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,KAAK,CAAE;YAAAoF,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEArF,aAAa,iBACZvB,OAAA;MAAKuG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxG,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxG,OAAA;UACEuG,SAAS,EAAC,aAAa;UACvBS,OAAO,EAAEA,CAAA,KAAMxF,gBAAgB,CAAC,KAAK,CAAE;UAAAgF,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5G,OAAA;UAAMuG,SAAS,EAAC,qBAAqB;UAACU,QAAQ,EAAEd,iBAAkB;UAAAK,QAAA,GAC/D,GAAG,eAEJxG,OAAA;YAAAwG,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5G,OAAA;YACE6G,IAAI,EAAC,MAAM;YACX9E,IAAI,EAAC,MAAM;YACX+E,WAAW,EAAC,WAAW;YACvB7B,KAAK,EAAEpD,QAAQ,CAACE,IAAK;YACrBgF,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5G,OAAA;YACE6G,IAAI,EAAC,OAAO;YACZ9E,IAAI,EAAC,OAAO;YACZ+E,WAAW,EAAC,OAAO;YACnB7B,KAAK,EAAEpD,QAAQ,CAACG,KAAM;YACtB+E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5G,OAAA;YACE6G,IAAI,EAAC,UAAU;YACf9E,IAAI,EAAC,UAAU;YACf+E,WAAW,EAAC,UAAU;YACtB7B,KAAK,EAAEpD,QAAQ,CAACI,QAAS;YACzB8E,QAAQ,EAAET;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF5G,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXkD,KAAK,EAAEpD,QAAQ,CAACK,IAAK;YACrB6E,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAERxG,OAAA;cAAQiF,KAAK,EAAC,EAAE;cAACoC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRrG,SAAS,CAAC+G,GAAG,CAAEpF,IAAI,iBAClBlC,OAAA;cAAuBiF,KAAK,EAAE/C,IAAI,CAACA,IAAK;cAAAsE,QAAA,EACrCtE,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC0C,GAAG;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACT5G,OAAA;YAAQ6G,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC5G,OAAA;YAAQ6G,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAMxF,gBAAgB,CAAC,KAAK,CAAE;YAAAgF,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5G,OAAA;MAAAwG,QAAA,gBACExG,OAAA;QAAAwG,QAAA,eACExG,OAAA;UAAAwG,QAAA,gBACExG,OAAA;YAAAwG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5G,OAAA;YAAAwG,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB5G,OAAA;YAAAwG,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5G,OAAA;YAAAwG,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB5G,OAAA;YAAAwG,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5G,OAAA;YAAAwG,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5G,OAAA;QAAAwG,QAAA,EACGtB,gBAAgB,CAACoC,GAAG,CAAElC,OAAO,iBAC5BpF,OAAA;UAAAwG,QAAA,gBACExG,OAAA;YAAAwG,QAAA,EAAKpB,OAAO,CAACrD;UAAI;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5G,OAAA;YAAAwG,QAAA,EAAKpB,OAAO,CAAClD;UAAI;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5G,OAAA;YAAAwG,QAAA,EAAKpB,OAAO,CAACpD;UAAK;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB5G,OAAA;YAAAwG,QAAA,eACExG,OAAA;cACEuG,SAAS,EAAE,UACTnB,OAAO,CAACmC,MAAM,GAAGnC,OAAO,CAACmC,MAAM,CAAClC,WAAW,CAAC,CAAC,GAAG,QAAQ,EACvD;cAAAmB,QAAA,EAEFpB,OAAO,CAACmC,MAAM,IAAI;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL5G,OAAA;YAAAwG,QAAA,EAAKpB,OAAO,CAACoC,MAAM,IAAI;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClC5G,OAAA;YAAAwG,QAAA,eACExG,OAAA;cAAKuG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxG,OAAA;gBACEgH,OAAO,EAAEA,CAAA,KAAMnC,UAAU,CAACO,OAAO,CAACR,GAAG,CAAE;gBACvC2B,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACrB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACR3F,aAAa,KAAKmE,OAAO,CAACR,GAAG,iBAC5B5E,OAAA;gBAAKuG,SAAS,EAAC,kBAAkB;gBAACkB,GAAG,EAAE1E,WAAY;gBAAAyD,QAAA,eACjDxG,OAAA;kBAAAwG,QAAA,gBACExG,OAAA;oBAAIgH,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAACd,OAAO,CAAE;oBAAAoB,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5G,OAAA;oBAAIgH,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAAChB,OAAO,CAACR,GAAG,CAAE;oBAAA4B,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAnCExB,OAAO,CAACR,GAAG;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoChB,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACR5G,OAAA;MAAGkH,KAAK,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAM,CAAE;MAAAlB,QAAA,EAAC;IAGjD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACJ5G,OAAA;MACEuG,SAAS,EAAC,kBAAkB;MAC5BS,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAACxD,IAAI,CAACgC,GAAG,CAAE;MAAA4B,QAAA,EAC9C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxG,EAAA,CA5hBID,aAAa;EAAA,QA6BqB3B,UAAU,EACvBD,OAAO,EACfG,WAAW;AAAA;AAAAiJ,EAAA,GA/BxBxH,aAAa;AA8hBnB,eAAeA,aAAa;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}