{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport io from \"socket.io-client\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { IoCallSharp } from \"react-icons/io5\";\nimport { FaVideo } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Chat({\n  chats,\n  ring\n}) {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const chatEndRef = useRef(null);\n  const [message, setMessage] = useState(\"\");\n  const [chat, setChat] = useState([]);\n  const messageHandled = useRef(false);\n  const socketRef = useRef(null); // ✅ Sử dụng useRef để lưu socket\n  // ✅ useEffect để khởi tạo socket và fetch messages\n  useEffect(() => {\n    if (loading || !user) return;\n\n    // Khởi tạo socket connection một lần duy nhất\n    if (!socketRef.current) {\n      socketRef.current = io(\"http://localhost:8080\", {\n        transports: [\"websocket\", \"polling\"],\n        // Thử websocket trước, fallback polling\n        timeout: 20000,\n        forceNew: true,\n        autoConnect: true\n      });\n\n      // Log connection status\n      socketRef.current.on(\"connect\", () => {\n        console.log(\"Socket connected successfully\");\n      });\n      socketRef.current.on(\"connect_error\", error => {\n        console.error(\"Socket connection error:\", error);\n      });\n    }\n    const fetchMessages = async () => {\n      try {\n        const response = await fetch(\"http://localhost:8080/api/chat/getMessages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            user\n          })\n        });\n        const data = await response.json();\n        console.log(data);\n        if (Array.isArray(data)) {\n          const formattedData = data.map(msg => ({\n            ...msg,\n            isUser: msg.sender._id === user._id\n          }));\n          setChat(formattedData);\n        }\n      } catch (err) {\n        console.error(\"Error fetching messages:\", err);\n      }\n    };\n    fetchMessages();\n\n    // Cleanup function khi component unmount\n    return () => {\n      if (socketRef.current) {\n        socketRef.current.disconnect();\n        socketRef.current = null;\n      }\n    };\n  }, [loading, user]);\n  useEffect(() => {\n    socket.on(\"receive_message\", data => {\n      console.log(!messageHandled.current);\n      console.log(data.sender._id !== user._id);\n      if (!messageHandled.current && data.sender._id !== user._id) {\n        messageHandled.current = true;\n        const newMessage = {\n          ...data,\n          isUser: data.sender._id === user._id\n        };\n        console.log(\"day la chat \", chats);\n        if (!chats) {\n          ring();\n        }\n        setChat(prev => [...prev, newMessage]);\n        setTimeout(() => {\n          messageHandled.current = false; // Đặt lại để xử lý tin nhắn mới\n        }, 1000); // Ví dụ reset sau 1 giây\n      }\n    });\n\n    // Cleanup khi component unmount\n    return () => {\n      socket.disconnect();\n    };\n  }, [ring]);\n  useEffect(() => {\n    var _chatEndRef$current;\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [chat]);\n  const sendMessage = () => {\n    if (message.trim() !== \"\") {\n      const newMessage = {\n        sender: user,\n        owner: user.id_owner,\n        content: message\n      };\n      socket.emit(\"send_message\", newMessage);\n      setChat(prev => [...prev, {\n        ...{\n          sender: user,\n          content: message\n        },\n        isUser: true\n      }]);\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.container,\n      display: chats ? \"block\" : \"none\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user === null || user === void 0 ? void 0 : user.avatar,\n          alt: \"Avatar\",\n          style: styles.headerAvatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.headerName,\n          children: (user === null || user === void 0 ? void 0 : user.name) || \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerRight,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(IoCallSharp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.chatWindow,\n      children: [chat.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.messageContainer,\n          justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\"\n        },\n        children: [!msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.message,\n            backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\"\n          },\n          children: msg.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chatEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.inputContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        placeholder: \"Type a message...\",\n        style: styles.input\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        style: styles.button,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(Chat, \"P4YbUiJlIeTgiYu+lrqgoBdmdbI=\", false, function () {\n  return [useAuth];\n});\n_c = Chat;\nconst styles = {\n  container: {\n    width: \"300px\",\n    margin: \"20px auto\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"5px\",\n    overflow: \"hidden\",\n    fontFamily: \"Arial, sans-serif\",\n    position: \"fixed\",\n    right: \"150px\",\n    bottom: \"95px\",\n    zIndex: 1000\n  },\n  chatWindow: {\n    height: \"300px\",\n    overflowY: \"scroll\",\n    padding: \"10px\",\n    backgroundColor: \"#f1f1f1\"\n  },\n  messageContainer: {\n    display: \"flex\",\n    alignItems: \"center\",\n    marginBottom: \"10px\"\n  },\n  avatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    margin: \"0 10px\"\n  },\n  message: {\n    padding: \"8px 12px\",\n    borderRadius: \"10px\",\n    maxWidth: \"200px\",\n    fontSize: \"14px\"\n  },\n  inputContainer: {\n    display: \"flex\",\n    borderTop: \"1px solid #ccc\",\n    padding: \"10px\",\n    backgroundColor: \"white\"\n  },\n  input: {\n    flex: 1,\n    padding: \"8px\",\n    fontSize: \"14px\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"4px\",\n    outline: \"none\"\n  },\n  button: {\n    padding: \"8px 12px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\",\n    marginLeft: \"5px\"\n  },\n  header: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"10px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    borderBottom: \"1px solid #ccc\"\n  },\n  headerLeft: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  headerAvatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    marginRight: \"10px\"\n  },\n  headerName: {\n    fontSize: \"16px\",\n    fontWeight: \"bold\"\n  },\n  headerRight: {\n    display: \"flex\",\n    gap: \"5px\"\n  },\n  headerButton: {\n    padding: \"5px 10px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#0056b3\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\"\n\n    // Các kiểu khác giữ nguyên\n  }\n};\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "io", "useAuth", "IoCallSharp", "FaVideo", "jsxDEV", "_jsxDEV", "Cha<PERSON>", "chats", "ring", "_s", "user", "loading", "chatEndRef", "message", "setMessage", "chat", "setChat", "messageHandled", "socketRef", "current", "transports", "timeout", "forceNew", "autoConnect", "on", "console", "log", "error", "fetchMessages", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "Array", "isArray", "formattedData", "map", "msg", "isUser", "sender", "_id", "err", "disconnect", "socket", "newMessage", "prev", "setTimeout", "_chatEndRef$current", "scrollIntoView", "behavior", "sendMessage", "trim", "owner", "id_owner", "content", "emit", "style", "styles", "container", "display", "children", "header", "headerLeft", "src", "avatar", "alt", "headerAvatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "headerName", "name", "headerRight", "headerButton", "chatWindow", "index", "messageContainer", "justifyContent", "backgroundColor", "ref", "inputContainer", "type", "value", "onChange", "e", "target", "placeholder", "input", "onClick", "button", "_c", "width", "margin", "border", "borderRadius", "overflow", "fontFamily", "position", "right", "bottom", "zIndex", "height", "overflowY", "padding", "alignItems", "marginBottom", "max<PERSON><PERSON><PERSON>", "fontSize", "borderTop", "flex", "outline", "color", "cursor", "marginLeft", "borderBottom", "marginRight", "fontWeight", "gap", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport io from \"socket.io-client\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { IoCallSharp } from \"react-icons/io5\";\r\nimport { FaVideo } from \"react-icons/fa\";\r\n\r\nfunction Chat({ chats, ring }) {\r\n  const { user, loading } = useAuth();\r\n  const chatEndRef = useRef(null);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [chat, setChat] = useState([]);\r\n  const messageHandled = useRef(false);\r\n  const socketRef = useRef(null); // ✅ Sử dụng useRef để lưu socket\r\n  // ✅ useEffect để khởi tạo socket và fetch messages\r\n  useEffect(() => {\r\n    if (loading || !user) return;\r\n\r\n    // Khởi tạo socket connection một lần duy nhất\r\n    if (!socketRef.current) {\r\n      socketRef.current = io(\"http://localhost:8080\", {\r\n        transports: [\"websocket\", \"polling\"], // Thử websocket trước, fallback polling\r\n        timeout: 20000,\r\n        forceNew: true,\r\n        autoConnect: true,\r\n      });\r\n\r\n      // Log connection status\r\n      socketRef.current.on(\"connect\", () => {\r\n        console.log(\"Socket connected successfully\");\r\n      });\r\n\r\n      socketRef.current.on(\"connect_error\", (error) => {\r\n        console.error(\"Socket connection error:\", error);\r\n      });\r\n    }\r\n\r\n    const fetchMessages = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          \"http://localhost:8080/api/chat/getMessages\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({ user }),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        console.log(data);\r\n        if (Array.isArray(data)) {\r\n          const formattedData = data.map((msg) => ({\r\n            ...msg,\r\n            isUser: msg.sender._id === user._id,\r\n          }));\r\n          setChat(formattedData);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching messages:\", err);\r\n      }\r\n    };\r\n\r\n    fetchMessages();\r\n\r\n    // Cleanup function khi component unmount\r\n    return () => {\r\n      if (socketRef.current) {\r\n        socketRef.current.disconnect();\r\n        socketRef.current = null;\r\n      }\r\n    };\r\n  }, [loading, user]);\r\n  useEffect(() => {\r\n    socket.on(\"receive_message\", (data) => {\r\n      console.log(!messageHandled.current);\r\n      console.log(data.sender._id !== user._id);\r\n      if (!messageHandled.current && data.sender._id !== user._id) {\r\n        messageHandled.current = true;\r\n\r\n        const newMessage = {\r\n          ...data,\r\n          isUser: data.sender._id === user._id,\r\n        };\r\n        console.log(\"day la chat \", chats);\r\n        if (!chats) {\r\n          ring();\r\n        }\r\n        setChat((prev) => [...prev, newMessage]);\r\n        setTimeout(() => {\r\n          messageHandled.current = false; // Đặt lại để xử lý tin nhắn mới\r\n        }, 1000); // Ví dụ reset sau 1 giây\r\n      }\r\n    });\r\n\r\n    // Cleanup khi component unmount\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n  }, [ring]);\r\n  useEffect(() => {\r\n    chatEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [chat]);\r\n  const sendMessage = () => {\r\n    if (message.trim() !== \"\") {\r\n      const newMessage = {\r\n        sender: user,\r\n        owner: user.id_owner,\r\n        content: message,\r\n      };\r\n\r\n      socket.emit(\"send_message\", newMessage);\r\n\r\n      setChat((prev) => [\r\n        ...prev,\r\n        {\r\n          ...{ sender: user, content: message },\r\n          isUser: true,\r\n        },\r\n      ]);\r\n\r\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ ...styles.container, display: chats ? \"block\" : \"none\" }}>\r\n      {/* Header */}\r\n      <div style={styles.header}>\r\n        <div style={styles.headerLeft}>\r\n          <img src={user?.avatar} alt=\"Avatar\" style={styles.headerAvatar} />\r\n          <span style={styles.headerName}>{user?.name || \"Chat\"}</span>\r\n        </div>\r\n        <div style={styles.headerRight}>\r\n          <button style={styles.headerButton}>\r\n            <IoCallSharp />\r\n          </button>\r\n          <button style={styles.headerButton}>\r\n            <FaVideo />\r\n          </button>\r\n        </div>\r\n      </div>\r\n      {/* Chat Window */}\r\n      <div style={styles.chatWindow}>\r\n        {chat.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              ...styles.messageContainer,\r\n              justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\",\r\n            }}\r\n          >\r\n            {!msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n            <div\r\n              style={{\r\n                ...styles.message,\r\n                backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\",\r\n              }}\r\n            >\r\n              {msg.content}\r\n            </div>\r\n            {msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n          </div>\r\n        ))}\r\n        <div ref={chatEndRef} />\r\n      </div>\r\n      {/* Input Field */}\r\n      <div style={styles.inputContainer}>\r\n        <input\r\n          type=\"text\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          placeholder=\"Type a message...\"\r\n          style={styles.input}\r\n        />\r\n        <button onClick={sendMessage} style={styles.button}>\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst styles = {\r\n  container: {\r\n    width: \"300px\",\r\n    margin: \"20px auto\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"5px\",\r\n    overflow: \"hidden\",\r\n    fontFamily: \"Arial, sans-serif\",\r\n    position: \"fixed\",\r\n    right: \"150px\",\r\n    bottom: \"95px\",\r\n    zIndex: 1000,\r\n  },\r\n  chatWindow: {\r\n    height: \"300px\",\r\n    overflowY: \"scroll\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#f1f1f1\",\r\n  },\r\n  messageContainer: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    marginBottom: \"10px\",\r\n  },\r\n  avatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    margin: \"0 10px\",\r\n  },\r\n  message: {\r\n    padding: \"8px 12px\",\r\n    borderRadius: \"10px\",\r\n    maxWidth: \"200px\",\r\n    fontSize: \"14px\",\r\n  },\r\n  inputContainer: {\r\n    display: \"flex\",\r\n    borderTop: \"1px solid #ccc\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"white\",\r\n  },\r\n  input: {\r\n    flex: 1,\r\n    padding: \"8px\",\r\n    fontSize: \"14px\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"4px\",\r\n    outline: \"none\",\r\n  },\r\n  button: {\r\n    padding: \"8px 12px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n    marginLeft: \"5px\",\r\n  },\r\n  header: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    borderBottom: \"1px solid #ccc\",\r\n  },\r\n  headerLeft: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n  },\r\n  headerAvatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    marginRight: \"10px\",\r\n  },\r\n  headerName: {\r\n    fontSize: \"16px\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  headerRight: {\r\n    display: \"flex\",\r\n    gap: \"5px\",\r\n  },\r\n  headerButton: {\r\n    padding: \"5px 10px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#0056b3\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n\r\n    // Các kiểu khác giữ nguyên\r\n  },\r\n};\r\n\r\nexport default Chat;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,IAAIA,CAAC;EAAEC,KAAK;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAMW,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMoB,cAAc,GAAGlB,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMmB,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAChC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIa,OAAO,IAAI,CAACD,IAAI,EAAE;;IAEtB;IACA,IAAI,CAACQ,SAAS,CAACC,OAAO,EAAE;MACtBD,SAAS,CAACC,OAAO,GAAGnB,EAAE,CAAC,uBAAuB,EAAE;QAC9CoB,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QAAE;QACtCC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACAL,SAAS,CAACC,OAAO,CAACK,EAAE,CAAC,SAAS,EAAE,MAAM;QACpCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,CAAC;MAEFR,SAAS,CAACC,OAAO,CAACK,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;QAC/CF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,4CAA4C,EAC5C;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEzB;UAAK,CAAC;QAC/B,CACF,CAAC;QACD,MAAM0B,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCZ,OAAO,CAACC,GAAG,CAACU,IAAI,CAAC;QACjB,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACvB,MAAMI,aAAa,GAAGJ,IAAI,CAACK,GAAG,CAAEC,GAAG,KAAM;YACvC,GAAGA,GAAG;YACNC,MAAM,EAAED,GAAG,CAACE,MAAM,CAACC,GAAG,KAAKnC,IAAI,CAACmC;UAClC,CAAC,CAAC,CAAC;UACH7B,OAAO,CAACwB,aAAa,CAAC;QACxB;MACF,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZrB,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEmB,GAAG,CAAC;MAChD;IACF,CAAC;IAEDlB,aAAa,CAAC,CAAC;;IAEf;IACA,OAAO,MAAM;MACX,IAAIV,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC4B,UAAU,CAAC,CAAC;QAC9B7B,SAAS,CAACC,OAAO,GAAG,IAAI;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAACR,OAAO,EAAED,IAAI,CAAC,CAAC;EACnBZ,SAAS,CAAC,MAAM;IACdkD,MAAM,CAACxB,EAAE,CAAC,iBAAiB,EAAGY,IAAI,IAAK;MACrCX,OAAO,CAACC,GAAG,CAAC,CAACT,cAAc,CAACE,OAAO,CAAC;MACpCM,OAAO,CAACC,GAAG,CAACU,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKnC,IAAI,CAACmC,GAAG,CAAC;MACzC,IAAI,CAAC5B,cAAc,CAACE,OAAO,IAAIiB,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKnC,IAAI,CAACmC,GAAG,EAAE;QAC3D5B,cAAc,CAACE,OAAO,GAAG,IAAI;QAE7B,MAAM8B,UAAU,GAAG;UACjB,GAAGb,IAAI;UACPO,MAAM,EAAEP,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKnC,IAAI,CAACmC;QACnC,CAAC;QACDpB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEnB,KAAK,CAAC;QAClC,IAAI,CAACA,KAAK,EAAE;UACVC,IAAI,CAAC,CAAC;QACR;QACAQ,OAAO,CAAEkC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;QACxCE,UAAU,CAAC,MAAM;UACflC,cAAc,CAACE,OAAO,GAAG,KAAK,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACX6B,MAAM,CAACD,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACvC,IAAI,CAAC,CAAC;EACVV,SAAS,CAAC,MAAM;IAAA,IAAAsD,mBAAA;IACd,CAAAA,mBAAA,GAAAxC,UAAU,CAACO,OAAO,cAAAiC,mBAAA,uBAAlBA,mBAAA,CAAoBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACvC,IAAI,CAAC,CAAC;EACV,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI1C,OAAO,CAAC2C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACzB,MAAMP,UAAU,GAAG;QACjBL,MAAM,EAAElC,IAAI;QACZ+C,KAAK,EAAE/C,IAAI,CAACgD,QAAQ;QACpBC,OAAO,EAAE9C;MACX,CAAC;MAEDmC,MAAM,CAACY,IAAI,CAAC,cAAc,EAAEX,UAAU,CAAC;MAEvCjC,OAAO,CAAEkC,IAAI,IAAK,CAChB,GAAGA,IAAI,EACP;QACE,GAAG;UAAEN,MAAM,EAAElC,IAAI;UAAEiD,OAAO,EAAE9C;QAAQ,CAAC;QACrC8B,MAAM,EAAE;MACV,CAAC,CACF,CAAC;MAEF7B,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAED,oBACET,OAAA;IAAKwD,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACC,SAAS;MAAEC,OAAO,EAAEzD,KAAK,GAAG,OAAO,GAAG;IAAO,CAAE;IAAA0D,QAAA,gBAErE5D,OAAA;MAAKwD,KAAK,EAAEC,MAAM,CAACI,MAAO;MAAAD,QAAA,gBACxB5D,OAAA;QAAKwD,KAAK,EAAEC,MAAM,CAACK,UAAW;QAAAF,QAAA,gBAC5B5D,OAAA;UAAK+D,GAAG,EAAE1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACS;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEtE,OAAA;UAAMwD,KAAK,EAAEC,MAAM,CAACc,UAAW;UAAAX,QAAA,EAAE,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,KAAI;QAAM;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNtE,OAAA;QAAKwD,KAAK,EAAEC,MAAM,CAACgB,WAAY;QAAAb,QAAA,gBAC7B5D,OAAA;UAAQwD,KAAK,EAAEC,MAAM,CAACiB,YAAa;UAAAd,QAAA,eACjC5D,OAAA,CAACH,WAAW;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACTtE,OAAA;UAAQwD,KAAK,EAAEC,MAAM,CAACiB,YAAa;UAAAd,QAAA,eACjC5D,OAAA,CAACF,OAAO;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA;MAAKwD,KAAK,EAAEC,MAAM,CAACkB,UAAW;MAAAf,QAAA,GAC3BlD,IAAI,CAAC0B,GAAG,CAAC,CAACC,GAAG,EAAEuC,KAAK,kBACnB5E,OAAA;QAEEwD,KAAK,EAAE;UACL,GAAGC,MAAM,CAACoB,gBAAgB;UAC1BC,cAAc,EAAEzC,GAAG,CAACC,MAAM,GAAG,UAAU,GAAG;QAC5C,CAAE;QAAAsB,QAAA,GAED,CAACvB,GAAG,CAACC,MAAM,iBACVtC,OAAA;UAAK+D,GAAG,EAAE1B,GAAG,CAACE,MAAM,CAACyB,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE,eACDtE,OAAA;UACEwD,KAAK,EAAE;YACL,GAAGC,MAAM,CAACjD,OAAO;YACjBuE,eAAe,EAAE1C,GAAG,CAACC,MAAM,GAAG,SAAS,GAAG;UAC5C,CAAE;UAAAsB,QAAA,EAEDvB,GAAG,CAACiB;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACLjC,GAAG,CAACC,MAAM,iBACTtC,OAAA;UAAK+D,GAAG,EAAE1B,GAAG,CAACE,MAAM,CAACyB,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE;MAAA,GAnBIM,KAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN,CAAC,eACFtE,OAAA;QAAKgF,GAAG,EAAEzE;MAAW;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAENtE,OAAA;MAAKwD,KAAK,EAAEC,MAAM,CAACwB,cAAe;MAAArB,QAAA,gBAChC5D,OAAA;QACEkF,IAAI,EAAC,MAAM;QACXC,KAAK,EAAE3E,OAAQ;QACf4E,QAAQ,EAAGC,CAAC,IAAK5E,UAAU,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC5CI,WAAW,EAAC,mBAAmB;QAC/B/B,KAAK,EAAEC,MAAM,CAAC+B;MAAM;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACFtE,OAAA;QAAQyF,OAAO,EAAEvC,WAAY;QAACM,KAAK,EAAEC,MAAM,CAACiC,MAAO;QAAA9B,QAAA,EAAC;MAEpD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClE,EAAA,CAlLQH,IAAI;EAAA,QACeL,OAAO;AAAA;AAAA+F,EAAA,GAD1B1F,IAAI;AAoLb,MAAMwD,MAAM,GAAG;EACbC,SAAS,EAAE;IACTkC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACV,CAAC;EACD1B,UAAU,EAAE;IACV2B,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE;EACnB,CAAC;EACDF,gBAAgB,EAAE;IAChBlB,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACD1C,MAAM,EAAE;IACN4B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBF,MAAM,EAAE;EACV,CAAC;EACDrF,OAAO,EAAE;IACPgG,OAAO,EAAE,UAAU;IACnBT,YAAY,EAAE,MAAM;IACpBY,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACD3B,cAAc,EAAE;IACdtB,OAAO,EAAE,MAAM;IACfkD,SAAS,EAAE,gBAAgB;IAC3BL,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE;EACnB,CAAC;EACDS,KAAK,EAAE;IACLsB,IAAI,EAAE,CAAC;IACPN,OAAO,EAAE,KAAK;IACdI,QAAQ,EAAE,MAAM;IAChBd,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBgB,OAAO,EAAE;EACX,CAAC;EACDrB,MAAM,EAAE;IACNc,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChB7B,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE;EACd,CAAC;EACDrD,MAAM,EAAE;IACNF,OAAO,EAAE,MAAM;IACfmB,cAAc,EAAE,eAAe;IAC/B2B,UAAU,EAAE,QAAQ;IACpBD,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE;EAChB,CAAC;EACDrD,UAAU,EAAE;IACVH,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE;EACd,CAAC;EACDvC,YAAY,EAAE;IACZ0B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBqB,WAAW,EAAE;EACf,CAAC;EACD7C,UAAU,EAAE;IACVqC,QAAQ,EAAE,MAAM;IAChBS,UAAU,EAAE;EACd,CAAC;EACD5C,WAAW,EAAE;IACXd,OAAO,EAAE,MAAM;IACf2D,GAAG,EAAE;EACP,CAAC;EACD5C,YAAY,EAAE;IACZ8B,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChB7B,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE;;IAER;EACF;AACF,CAAC;AAED,eAAehH,IAAI;AAAC,IAAA0F,EAAA;AAAA4B,YAAA,CAAA5B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}