{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { FaRegUser } from \"react-icons/fa\";\nimport { FaChild } from \"react-icons/fa\";\nimport { FaCheckSquare } from \"react-icons/fa\";\nimport { MdEmail } from \"react-icons/md\";\nimport { RiLockPasswordFill } from \"react-icons/ri\";\nimport \"./Profile.css\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport Avatar from \"../../components/Avatar\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport ProfilePictureOptions from \"./image.js\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Profile() {\n  _s();\n  const {\n    user,\n    logout,\n    loading\n  } = useAuth();\n  const [edit, setEdit] = useState(false);\n  const [editImage, setEditImage] = useState(false);\n  const [data, setData] = useState(null);\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const [newData, setNewData] = useState(null);\n  const [refresh, setRefresh] = useState(false);\n  const [showBankForm, setShowBankForm] = useState(false);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [newBankAccount, setNewBankAccount] = useState({\n    accountNumber: \"\",\n    bankName: \"\",\n    name: \"\"\n  });\n  const [x, SetX] = useState(false);\n  useEffect(() => {\n    const fetchProfile = async () => {\n      if (loading) return;\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/profile/get_profile\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          user\n        })\n      });\n      const response2 = await fetch(\"http://localhost:8080/api/bank/get_bank\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          user\n        })\n      });\n      if (!response.ok || !response2.ok) {\n        notify(2, \"network is not okay!\", \"Thất bại\");\n      }\n      const profileData = await response.json();\n      const acc = await response2.json();\n      console.log(profileData);\n      stopLoading();\n      setData(profileData);\n      setNewData(profileData);\n      setBankAccounts(acc);\n    };\n    fetchProfile();\n  }, [loading, x]);\n  const handleEditChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const saveChanges = async () => {\n    startLoading();\n    const response = await fetch(\"http://localhost:8080/api/profile/change_profile\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        user: newData\n      })\n    });\n    if (!response.ok) throw new Error(\"Network response was not ok\");\n    const result = await response.json();\n    stopLoading();\n    if (result.respond === \"success\") {\n      notify(1, \"Cập nhật thông tin cá nhân thành công\", \"Thành công\");\n      setEdit(false);\n      setRefresh(prev => !prev);\n    }\n  };\n  const handleBankInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewBankAccount(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const addBankAccount = async e => {\n    e.preventDefault();\n    if (data.role != \"Admin\") {\n      notify(2, \"chỉ có chủ mới có quyền thêm tài khoản\", \"Thất bại\");\n      return;\n    }\n    startLoading();\n    let body = {\n      user: {\n        ...user,\n        id: user._id\n      },\n      newPr: {\n        ...newBankAccount\n      }\n    };\n    console.log(body);\n    fetch(\"http://localhost:8080/api/bank/add_bank\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(body)\n    }).then(response => response.json()).then(data => {\n      stopLoading();\n      console.log(data.message);\n      if (data.message === \"Success\") {\n        notify(1, \"thêm tài khoản thành công\", \"Thành công\");\n        SetX(a => !a);\n      } else {\n        notify(1, \"thêm tài khoản thành công\", \"Thành công\");\n        SetX(a => !a);\n      }\n    }).catch(error => {\n      notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\n      console.log(\"Lỗi:\", error);\n    });\n  };\n  const handleDeleteAccount = async index => {\n    if (data.role != \"Admin\") {\n      notify(2, \"chỉ có chủ mới có quyền xoá tài khoản\", \"Thất bại\");\n      return;\n    }\n    const accountToDelete = bankAccounts[index];\n    const response = await fetch(\"http://localhost:8080/api/bank/delete_bank\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        user: user,\n        accountNumber: accountToDelete.accountNumber,\n        bankName: accountToDelete.bankName\n      })\n    });\n    console.log(response);\n    if (response.ok) {\n      setBankAccounts(prev => prev.filter((_, i) => i !== index));\n      notify(1, \"Xóa tài khoản thành công\", \"Thành công\");\n    } else {\n      notify(1, \"Xóa tài khoản thành công\", \"Thành công\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://th.bing.com/th?id=ORMS.********************************&pid=Wdp&w=612&h=304&qlt=90&c=1&rs=1&dpr=1.5&p=0\",\n        alt: \"Profile Banner\",\n        className: \"banner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-picture\",\n          onClick: () => setEditImage(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"uy-avatar\",\n            style: {\n              cursor: \"pointer\"\n            },\n            children: data ? /*#__PURE__*/_jsxDEV(Avatar, {\n              name: data.name,\n              imageUrl: data.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 23\n            }, this) : \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), editImage && /*#__PURE__*/_jsxDEV(ProfilePictureOptions, {\n          image: data.avatar,\n          reload: () => setRefresh(prev => !prev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-info\",\n        children: [!edit ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-info__name\",\n          children: data ? data.name : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          value: newData ? newData.name : \"\",\n          onChange: handleEditChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), edit ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"message-btn\",\n            onClick: saveChanges,\n            children: \"L\\u01B0u\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"message-btn\",\n            onClick: () => setEdit(false),\n            style: {\n              marginLeft: \"10px\"\n            },\n            children: \"Tho\\xE1t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"message-btn\",\n          onClick: () => setEdit(true),\n          children: \"Edit profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"connect-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Th\\xF4ng tin c\\xE1 nh\\xE2n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            children: [/*#__PURE__*/_jsxDEV(FaRegUser, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), \" Qu\\xE1n c\\u1EE7a : \", data ? data.id_owner.name : \"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            children: [/*#__PURE__*/_jsxDEV(FaChild, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), \" v\\u1ECB tr\\xED : \", data ? data.role : \"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckSquare, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), \" Quy\\u1EC1n :\", \" \", data ? data.right ? data.right.permissions.map(p => p).join(\", \") : data.role == \"Admin\" ? \"tất cả các quyền\" : \"Không có quyền gì\" : \"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            children: [/*#__PURE__*/_jsxDEV(MdEmail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), \" Email : \", data ? data.email : \"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            children: [/*#__PURE__*/_jsxDEV(RiLockPasswordFill, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), \" M\\u1EADt kh\\u1EA9u :\", !edit ? data ? data.password : \"\" : /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"password\",\n              value: newData ? newData.password : \"\",\n              onChange: handleEditChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bank-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Th\\xF4ng tin t\\xE0i kho\\u1EA3n ng\\xE2n h\\xE0ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"message-btn\",\n        onClick: () => {\n          setShowBankForm(prev => !prev);\n          setNewBankAccount({\n            accountNumber: \"\",\n            bankName: \"\",\n            name: \"\",\n            image: \"\"\n          });\n        },\n        children: showBankForm ? \"Đóng form\" : \"Thêm tài khoản ngân hàng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), showBankForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bank-form\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: addBankAccount,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"accountNumber\",\n            placeholder: \"S\\u1ED1 t\\xE0i kho\\u1EA3n\",\n            value: newBankAccount.accountNumber,\n            onChange: handleBankInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bank-select-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"bankName\",\n              className: \"bank-select-label\",\n              children: \"Ch\\u1ECDn ng\\xE2n h\\xE0ng:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"bankName\",\n              name: \"bankName\",\n              value: newBankAccount.bankName,\n              onChange: handleBankInputChange,\n              required: true,\n              className: \"bank-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                disabled: true,\n                children: \"Ch\\u1ECDn ng\\xE2n h\\xE0ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"VCB\",\n                children: \"Vietcombank (VCB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"TCB\",\n                children: \"Techcombank (TCB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ICB\",\n                children: \"VietinBank (ICB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"BIDV\",\n                children: \"BIDV\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"STB\",\n                children: \"Sacombank (STB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"MB\",\n                children: \"MB Bank (MB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ACB\",\n                children: \"ACB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"VPB\",\n                children: \"VPBank (VPB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"HDB\",\n                children: \"HDBank (HDB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"SHB\",\n                children: \"SHB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Oceanbank\",\n                children: \"OceanBank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"DOB\",\n                children: \"DongA Bank (DOB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"VBA\",\n                children: \"Agribank (VBA)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EIB\",\n                children: \"Eximbank (EIB)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"T\\xEAn\",\n            value: newBankAccount.name,\n            onChange: handleBankInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"message-btn\",\n            children: \"L\\u01B0u t\\xE0i kho\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: bankAccounts.map((account, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            marginBottom: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [account.name, \" - \", account.bankName, \" (\", account.accountNumber, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              marginLeft: \"10px\",\n              cursor: \"pointer\"\n            },\n            onClick: () => handleDeleteAccount(index),\n            className: \"delete_account\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-logout\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"message-btn logout\",\n        onClick: logout,\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n}\n_s(Profile, \"MsdUiMHhkbr4qrWbLactePfoKuc=\", false, function () {\n  return [useAuth, useLoading];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaRegUser", "<PERSON>a<PERSON><PERSON><PERSON>", "FaCheckSquare", "MdEmail", "RiLockPasswordFill", "useAuth", "Avatar", "useLoading", "ProfilePictureOptions", "notify", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "user", "logout", "loading", "edit", "setEdit", "editImage", "setEditImage", "data", "setData", "startLoading", "stopLoading", "newData", "setNewData", "refresh", "setRefresh", "showBankForm", "setShowBankForm", "bankAccounts", "setBankAccounts", "newBankAccount", "set<PERSON>ewBankAccount", "accountNumber", "bankName", "name", "x", "SetX", "fetchProfile", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "response2", "ok", "profileData", "json", "acc", "console", "log", "handleEditChange", "e", "value", "target", "prev", "saveChanges", "Error", "result", "respond", "handleBankInputChange", "addBankAccount", "preventDefault", "role", "id", "_id", "newPr", "then", "message", "a", "catch", "error", "handleDeleteAccount", "index", "accountToDelete", "filter", "_", "i", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "cursor", "imageUrl", "avatar", "image", "reload", "type", "onChange", "marginLeft", "href", "id_owner", "right", "permissions", "map", "p", "join", "email", "password", "onSubmit", "placeholder", "required", "htmlFor", "disabled", "account", "display", "alignItems", "marginBottom", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/Profile/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { FaRegUser } from \"react-icons/fa\";\r\nimport { FaChild } from \"react-icons/fa\";\r\nimport { FaCheckSquare } from \"react-icons/fa\";\r\nimport { MdEmail } from \"react-icons/md\";\r\nimport { RiLockPasswordFill } from \"react-icons/ri\";\r\nimport \"./Profile.css\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport Avatar from \"../../components/Avatar\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport ProfilePictureOptions from \"./image.js\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\n\r\nfunction Profile() {\r\n  const { user, logout, loading } = useAuth();\r\n  const [edit, setEdit] = useState(false);\r\n  const [editImage, setEditImage] = useState(false);\r\n  const [data, setData] = useState(null);\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const [newData, setNewData] = useState(null);\r\n  const [refresh, setRefresh] = useState(false);\r\n  const [showBankForm, setShowBankForm] = useState(false);\r\n  const [bankAccounts, setBankAccounts] = useState([]);\r\n  const [newBankAccount, setNewBankAccount] = useState({\r\n    accountNumber: \"\",\r\n    bankName: \"\",\r\n    name: \"\",\r\n  });\r\n  const [x, SetX] = useState(false);\r\n  useEffect(() => {\r\n    const fetchProfile = async () => {\r\n      if (loading) return;\r\n      startLoading();\r\n      const response = await fetch(\r\n        \"http://localhost:8080/api/profile/get_profile\",\r\n        {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ user }),\r\n        }\r\n      );\r\n      const response2 = await fetch(\"http://localhost:8080/api/bank/get_bank\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ user }),\r\n      });\r\n      if (!response.ok || !response2.ok) {\r\n        notify(2, \"network is not okay!\", \"Thất bại\");\r\n      }\r\n      const profileData = await response.json();\r\n      const acc = await response2.json();\r\n      console.log(profileData);\r\n      stopLoading();\r\n      setData(profileData);\r\n      setNewData(profileData);\r\n      setBankAccounts(acc);\r\n    };\r\n\r\n    fetchProfile();\r\n  }, [loading, x]);\r\n\r\n  const handleEditChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setNewData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const saveChanges = async () => {\r\n    startLoading();\r\n    const response = await fetch(\r\n      \"http://localhost:8080/api/profile/change_profile\",\r\n      {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ user: newData }),\r\n      }\r\n    );\r\n    if (!response.ok) throw new Error(\"Network response was not ok\");\r\n\r\n    const result = await response.json();\r\n    stopLoading();\r\n    if (result.respond === \"success\") {\r\n      notify(1, \"Cập nhật thông tin cá nhân thành công\", \"Thành công\");\r\n      setEdit(false);\r\n      setRefresh((prev) => !prev);\r\n    }\r\n  };\r\n\r\n  const handleBankInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setNewBankAccount((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n  const addBankAccount = async (e) => {\r\n    e.preventDefault();\r\n    if (data.role != \"Admin\") {\r\n      notify(2, \"chỉ có chủ mới có quyền thêm tài khoản\", \"Thất bại\");\r\n      return;\r\n    }\r\n    startLoading();\r\n    let body = {\r\n      user: { ...user, id: user._id },\r\n      newPr: { ...newBankAccount },\r\n    };\r\n    console.log(body);\r\n    fetch(\"http://localhost:8080/api/bank/add_bank\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(body),\r\n    })\r\n      .then((response) => response.json())\r\n      .then((data) => {\r\n        stopLoading();\r\n        console.log(data.message);\r\n        if (data.message === \"Success\") {\r\n          notify(1, \"thêm tài khoản thành công\", \"Thành công\");\r\n          SetX((a) => !a);\r\n        } else {\r\n          notify(1, \"thêm tài khoản thành công\", \"Thành công\");\r\n          SetX((a) => !a);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        notify(2, \"thêm sản phẩm thất bại\", \"Thất bại\");\r\n        console.log(\"Lỗi:\", error);\r\n      });\r\n  };\r\n  const handleDeleteAccount = async (index) => {\r\n    if (data.role != \"Admin\") {\r\n      notify(2, \"chỉ có chủ mới có quyền xoá tài khoản\", \"Thất bại\");\r\n      return;\r\n    }\r\n    const accountToDelete = bankAccounts[index];\r\n    const response = await fetch(\"http://localhost:8080/api/bank/delete_bank\", {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({\r\n        user: user,\r\n        accountNumber: accountToDelete.accountNumber,\r\n        bankName: accountToDelete.bankName,\r\n      }),\r\n    });\r\n    console.log(response);\r\n    if (response.ok) {\r\n      setBankAccounts((prev) => prev.filter((_, i) => i !== index));\r\n      notify(1, \"Xóa tài khoản thành công\", \"Thành công\");\r\n    } else {\r\n      notify(1, \"Xóa tài khoản thành công\", \"Thành công\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"profile-container\">\r\n      <div className=\"profile-header\">\r\n        <img\r\n          src=\"https://th.bing.com/th?id=ORMS.********************************&pid=Wdp&w=612&h=304&qlt=90&c=1&rs=1&dpr=1.5&p=0\"\r\n          alt=\"Profile Banner\"\r\n          className=\"banner\"\r\n        />\r\n        <div>\r\n          <div\r\n            className=\"profile-picture\"\r\n            onClick={() => setEditImage((prev) => !prev)}\r\n          >\r\n            <div className=\"uy-avatar\" style={{ cursor: \"pointer\" }}>\r\n              {data ? <Avatar name={data.name} imageUrl={data.avatar} /> : \"\"}\r\n            </div>\r\n          </div>\r\n          {editImage && (\r\n            <ProfilePictureOptions\r\n              image={data.avatar}\r\n              reload={() => setRefresh((prev) => !prev)}\r\n            />\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"profile-info\">\r\n          {!edit ? (\r\n            <div className=\"profile-info__name\">{data ? data.name : \"\"}</div>\r\n          ) : (\r\n            <input\r\n              type=\"text\"\r\n              name=\"name\"\r\n              value={newData ? newData.name : \"\"}\r\n              onChange={handleEditChange}\r\n            />\r\n          )}\r\n\r\n          {edit ? (\r\n            <>\r\n              <button className=\"message-btn\" onClick={saveChanges}>\r\n                Lưu\r\n              </button>\r\n              <button\r\n                className=\"message-btn\"\r\n                onClick={() => setEdit(false)}\r\n                style={{ marginLeft: \"10px\" }}\r\n              >\r\n                Thoát\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <button className=\"message-btn\" onClick={() => setEdit(true)}>\r\n              Edit profile\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"connect-section\">\r\n        <div>Thông tin cá nhân</div>\r\n        <ul>\r\n          <li>\r\n            <a href=\"#\">\r\n              <FaRegUser /> Quán của : {data ? data.id_owner.name : \"\"}\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"#\">\r\n              <FaChild /> vị trí : {data ? data.role : \"\"}\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"#\">\r\n              <FaCheckSquare /> Quyền :{\" \"}\r\n              {data\r\n                ? data.right\r\n                  ? data.right.permissions.map((p) => p).join(\", \")\r\n                  : data.role == \"Admin\"\r\n                  ? \"tất cả các quyền\"\r\n                  : \"Không có quyền gì\"\r\n                : \"\"}\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"#\">\r\n              <MdEmail /> Email : {data ? data.email : \"\"}\r\n            </a>\r\n          </li>\r\n          <li>\r\n            <a href=\"#\">\r\n              <RiLockPasswordFill /> Mật khẩu :\r\n              {!edit ? (\r\n                data ? (\r\n                  data.password\r\n                ) : (\r\n                  \"\"\r\n                )\r\n              ) : (\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"password\"\r\n                  value={newData ? newData.password : \"\"}\r\n                  onChange={handleEditChange}\r\n                />\r\n              )}\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      <div className=\"bank-section\">\r\n        <div>Thông tin tài khoản ngân hàng</div>\r\n        <button\r\n          className=\"message-btn\"\r\n          onClick={() => {\r\n            setShowBankForm((prev) => !prev);\r\n            setNewBankAccount({\r\n              accountNumber: \"\",\r\n              bankName: \"\",\r\n              name: \"\",\r\n              image: \"\",\r\n            });\r\n          }}\r\n        >\r\n          {showBankForm ? \"Đóng form\" : \"Thêm tài khoản ngân hàng\"}\r\n        </button>\r\n        {showBankForm && (\r\n          <div className=\"bank-form\">\r\n            <form onSubmit={addBankAccount}>\r\n              <input\r\n                type=\"text\"\r\n                name=\"accountNumber\"\r\n                placeholder=\"Số tài khoản\"\r\n                value={newBankAccount.accountNumber}\r\n                onChange={handleBankInputChange}\r\n                required\r\n              />\r\n              <div className=\"bank-select-container\">\r\n                <label htmlFor=\"bankName\" className=\"bank-select-label\">\r\n                  Chọn ngân hàng:\r\n                </label>\r\n                <select\r\n                  id=\"bankName\"\r\n                  name=\"bankName\"\r\n                  value={newBankAccount.bankName}\r\n                  onChange={handleBankInputChange}\r\n                  required\r\n                  className=\"bank-select\"\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Chọn ngân hàng\r\n                  </option>\r\n                  <option value=\"VCB\">Vietcombank (VCB)</option>\r\n                  <option value=\"TCB\">Techcombank (TCB)</option>\r\n                  <option value=\"ICB\">VietinBank (ICB)</option>\r\n                  <option value=\"BIDV\">BIDV</option>\r\n                  <option value=\"STB\">Sacombank (STB)</option>\r\n                  <option value=\"MB\">MB Bank (MB)</option>\r\n                  <option value=\"ACB\">ACB</option>\r\n                  <option value=\"VPB\">VPBank (VPB)</option>\r\n                  <option value=\"HDB\">HDBank (HDB)</option>\r\n                  <option value=\"SHB\">SHB</option>\r\n                  <option value=\"Oceanbank\">OceanBank</option>\r\n                  <option value=\"DOB\">DongA Bank (DOB)</option>\r\n                  <option value=\"VBA\">Agribank (VBA)</option>\r\n                  <option value=\"EIB\">Eximbank (EIB)</option>\r\n                </select>\r\n              </div>\r\n\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Tên\"\r\n                value={newBankAccount.name}\r\n                onChange={handleBankInputChange}\r\n                required\r\n              />\r\n              {/* <label>mã QR</label>\r\n        <input type=\"file\" name=\"image\" onChange={handleChangeimage} required />\r\n        {image && (\r\n          <div>\r\n            <h3>Ảnh :</h3>\r\n            <img src={image} alt=\"Captured\" style={{ width: '300px' }} />\r\n          </div>\r\n        )} */}\r\n              <button className=\"message-btn\">Lưu tài khoản</button>\r\n            </form>\r\n          </div>\r\n        )}\r\n        <ul>\r\n          {bankAccounts.map((account, index) => (\r\n            <li\r\n              key={index}\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                marginBottom: \"10px\",\r\n              }}\r\n            >\r\n              <span>\r\n                {account.name} - {account.bankName} ({account.accountNumber})\r\n              </span>\r\n\r\n              <button\r\n                style={{ marginLeft: \"10px\", cursor: \"pointer\" }}\r\n                onClick={() => handleDeleteAccount(index)}\r\n                className=\"delete_account\"\r\n              >\r\n                Delete\r\n              </button>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n\r\n      <div className=\"profile-logout\">\r\n        <button className=\"message-btn logout\" onClick={logout}>\r\n          Logout\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,OAAO,eAAe;AACtB,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,OAAOC,qBAAqB,MAAM,YAAY;AAC9C,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACc,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM;IAAE2B,YAAY;IAAEC;EAAY,CAAC,GAAGnB,UAAU,CAAC,CAAC;EAClD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC;IACnDuC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,CAAC,EAAEC,IAAI,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjCC,SAAS,CAAC,MAAM;IACd,MAAM2C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAIxB,OAAO,EAAE;MACbO,YAAY,CAAC,CAAC;MACd,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+C,EAC/C;QACEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEjC;QAAK,CAAC;MAC/B,CACF,CAAC;MACD,MAAMkC,SAAS,GAAG,MAAMN,KAAK,CAAC,yCAAyC,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEjC;QAAK,CAAC;MAC/B,CAAC,CAAC;MACF,IAAI,CAAC2B,QAAQ,CAACQ,EAAE,IAAI,CAACD,SAAS,CAACC,EAAE,EAAE;QACjC1C,MAAM,CAAC,CAAC,EAAE,sBAAsB,EAAE,UAAU,CAAC;MAC/C;MACA,MAAM2C,WAAW,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MACzC,MAAMC,GAAG,GAAG,MAAMJ,SAAS,CAACG,IAAI,CAAC,CAAC;MAClCE,OAAO,CAACC,GAAG,CAACJ,WAAW,CAAC;MACxB1B,WAAW,CAAC,CAAC;MACbF,OAAO,CAAC4B,WAAW,CAAC;MACpBxB,UAAU,CAACwB,WAAW,CAAC;MACvBlB,eAAe,CAACoB,GAAG,CAAC;IACtB,CAAC;IAEDZ,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACxB,OAAO,EAAEsB,CAAC,CAAC,CAAC;EAEhB,MAAMiB,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEnB,IAAI;MAAEoB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChChC,UAAU,CAAEiC,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACtB,IAAI,GAAGoB;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BrC,YAAY,CAAC,CAAC;IACd,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kDAAkD,EAClD;MACEC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEjC,IAAI,EAAEW;MAAQ,CAAC;IACxC,CACF,CAAC;IACD,IAAI,CAACgB,QAAQ,CAACQ,EAAE,EAAE,MAAM,IAAIY,KAAK,CAAC,6BAA6B,CAAC;IAEhE,MAAMC,MAAM,GAAG,MAAMrB,QAAQ,CAACU,IAAI,CAAC,CAAC;IACpC3B,WAAW,CAAC,CAAC;IACb,IAAIsC,MAAM,CAACC,OAAO,KAAK,SAAS,EAAE;MAChCxD,MAAM,CAAC,CAAC,EAAE,uCAAuC,EAAE,YAAY,CAAC;MAChEW,OAAO,CAAC,KAAK,CAAC;MACdU,UAAU,CAAE+B,IAAI,IAAK,CAACA,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMK,qBAAqB,GAAIR,CAAC,IAAK;IACnC,MAAM;MAAEnB,IAAI;MAAEoB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCxB,iBAAiB,CAAEyB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACtB,IAAI,GAAGoB;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EACD,MAAMQ,cAAc,GAAG,MAAOT,CAAC,IAAK;IAClCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,IAAI7C,IAAI,CAAC8C,IAAI,IAAI,OAAO,EAAE;MACxB5D,MAAM,CAAC,CAAC,EAAE,wCAAwC,EAAE,UAAU,CAAC;MAC/D;IACF;IACAgB,YAAY,CAAC,CAAC;IACd,IAAIsB,IAAI,GAAG;MACT/B,IAAI,EAAE;QAAE,GAAGA,IAAI;QAAEsD,EAAE,EAAEtD,IAAI,CAACuD;MAAI,CAAC;MAC/BC,KAAK,EAAE;QAAE,GAAGrC;MAAe;IAC7B,CAAC;IACDoB,OAAO,CAACC,GAAG,CAACT,IAAI,CAAC;IACjBH,KAAK,CAAC,yCAAyC,EAAE;MAC/CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACF,IAAI;IAC3B,CAAC,CAAC,CACC0B,IAAI,CAAE9B,QAAQ,IAAKA,QAAQ,CAACU,IAAI,CAAC,CAAC,CAAC,CACnCoB,IAAI,CAAElD,IAAI,IAAK;MACdG,WAAW,CAAC,CAAC;MACb6B,OAAO,CAACC,GAAG,CAACjC,IAAI,CAACmD,OAAO,CAAC;MACzB,IAAInD,IAAI,CAACmD,OAAO,KAAK,SAAS,EAAE;QAC9BjE,MAAM,CAAC,CAAC,EAAE,2BAA2B,EAAE,YAAY,CAAC;QACpDgC,IAAI,CAAEkC,CAAC,IAAK,CAACA,CAAC,CAAC;MACjB,CAAC,MAAM;QACLlE,MAAM,CAAC,CAAC,EAAE,2BAA2B,EAAE,YAAY,CAAC;QACpDgC,IAAI,CAAEkC,CAAC,IAAK,CAACA,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,CACDC,KAAK,CAAEC,KAAK,IAAK;MAChBpE,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;MAC/C8C,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEqB,KAAK,CAAC;IAC5B,CAAC,CAAC;EACN,CAAC;EACD,MAAMC,mBAAmB,GAAG,MAAOC,KAAK,IAAK;IAC3C,IAAIxD,IAAI,CAAC8C,IAAI,IAAI,OAAO,EAAE;MACxB5D,MAAM,CAAC,CAAC,EAAE,uCAAuC,EAAE,UAAU,CAAC;MAC9D;IACF;IACA,MAAMuE,eAAe,GAAG/C,YAAY,CAAC8C,KAAK,CAAC;IAC3C,MAAMpC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;MACzEC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBjC,IAAI,EAAEA,IAAI;QACVqB,aAAa,EAAE2C,eAAe,CAAC3C,aAAa;QAC5CC,QAAQ,EAAE0C,eAAe,CAAC1C;MAC5B,CAAC;IACH,CAAC,CAAC;IACFiB,OAAO,CAACC,GAAG,CAACb,QAAQ,CAAC;IACrB,IAAIA,QAAQ,CAACQ,EAAE,EAAE;MACfjB,eAAe,CAAE2B,IAAI,IAAKA,IAAI,CAACoB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;MAC7DtE,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;IACrD,CAAC,MAAM;MACLA,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;IACrD;EACF,CAAC;EAED,oBACEE,OAAA;IAAKyE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC1E,OAAA;MAAKyE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B1E,OAAA;QACE2E,GAAG,EAAC,iHAAiH;QACrHC,GAAG,EAAC,gBAAgB;QACpBH,SAAS,EAAC;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFhF,OAAA;QAAA0E,QAAA,gBACE1E,OAAA;UACEyE,SAAS,EAAC,iBAAiB;UAC3BQ,OAAO,EAAEA,CAAA,KAAMtE,YAAY,CAAEuC,IAAI,IAAK,CAACA,IAAI,CAAE;UAAAwB,QAAA,eAE7C1E,OAAA;YAAKyE,SAAS,EAAC,WAAW;YAACS,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAT,QAAA,EACrD9D,IAAI,gBAAGZ,OAAA,CAACL,MAAM;cAACiC,IAAI,EAAEhB,IAAI,CAACgB,IAAK;cAACwD,QAAQ,EAAExE,IAAI,CAACyE;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLtE,SAAS,iBACRV,OAAA,CAACH,qBAAqB;UACpByF,KAAK,EAAE1E,IAAI,CAACyE,MAAO;UACnBE,MAAM,EAAEA,CAAA,KAAMpE,UAAU,CAAE+B,IAAI,IAAK,CAACA,IAAI;QAAE;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhF,OAAA;QAAKyE,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1B,CAAClE,IAAI,gBACJR,OAAA;UAAKyE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAE9D,IAAI,GAAGA,IAAI,CAACgB,IAAI,GAAG;QAAE;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAEjEhF,OAAA;UACEwF,IAAI,EAAC,MAAM;UACX5D,IAAI,EAAC,MAAM;UACXoB,KAAK,EAAEhC,OAAO,GAAGA,OAAO,CAACY,IAAI,GAAG,EAAG;UACnC6D,QAAQ,EAAE3C;QAAiB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACF,EAEAxE,IAAI,gBACHR,OAAA,CAAAE,SAAA;UAAAwE,QAAA,gBACE1E,OAAA;YAAQyE,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAE9B,WAAY;YAAAuB,QAAA,EAAC;UAEtD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThF,OAAA;YACEyE,SAAS,EAAC,aAAa;YACvBQ,OAAO,EAAEA,CAAA,KAAMxE,OAAO,CAAC,KAAK,CAAE;YAC9ByE,KAAK,EAAE;cAAEQ,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EAC/B;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHhF,OAAA;UAAQyE,SAAS,EAAC,aAAa;UAACQ,OAAO,EAAEA,CAAA,KAAMxE,OAAO,CAAC,IAAI,CAAE;UAAAiE,QAAA,EAAC;QAE9D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhF,OAAA;MAAKyE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1E,OAAA;QAAA0E,QAAA,EAAK;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5BhF,OAAA;QAAA0E,QAAA,gBACE1E,OAAA;UAAA0E,QAAA,eACE1E,OAAA;YAAG2F,IAAI,EAAC,GAAG;YAAAjB,QAAA,gBACT1E,OAAA,CAACX,SAAS;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAAY,EAACpE,IAAI,GAAGA,IAAI,CAACgF,QAAQ,CAAChE,IAAI,GAAG,EAAE;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACLhF,OAAA;UAAA0E,QAAA,eACE1E,OAAA;YAAG2F,IAAI,EAAC,GAAG;YAAAjB,QAAA,gBACT1E,OAAA,CAACV,OAAO;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAU,EAACpE,IAAI,GAAGA,IAAI,CAAC8C,IAAI,GAAG,EAAE;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACLhF,OAAA;UAAA0E,QAAA,eACE1E,OAAA;YAAG2F,IAAI,EAAC,GAAG;YAAAjB,QAAA,gBACT1E,OAAA,CAACT,aAAa;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAAQ,EAAC,GAAG,EAC5BpE,IAAI,GACDA,IAAI,CAACiF,KAAK,GACRjF,IAAI,CAACiF,KAAK,CAACC,WAAW,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAC/CrF,IAAI,CAAC8C,IAAI,IAAI,OAAO,GACpB,kBAAkB,GAClB,mBAAmB,GACrB,EAAE;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACLhF,OAAA;UAAA0E,QAAA,eACE1E,OAAA;YAAG2F,IAAI,EAAC,GAAG;YAAAjB,QAAA,gBACT1E,OAAA,CAACR,OAAO;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAAS,EAACpE,IAAI,GAAGA,IAAI,CAACsF,KAAK,GAAG,EAAE;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACLhF,OAAA;UAAA0E,QAAA,eACE1E,OAAA;YAAG2F,IAAI,EAAC,GAAG;YAAAjB,QAAA,gBACT1E,OAAA,CAACP,kBAAkB;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBACtB,EAAC,CAACxE,IAAI,GACJI,IAAI,GACFA,IAAI,CAACuF,QAAQ,GAEb,EACD,gBAEDnG,OAAA;cACEwF,IAAI,EAAC,MAAM;cACX5D,IAAI,EAAC,UAAU;cACfoB,KAAK,EAAEhC,OAAO,GAAGA,OAAO,CAACmF,QAAQ,GAAG,EAAG;cACvCV,QAAQ,EAAE3C;YAAiB;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENhF,OAAA;MAAKyE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1E,OAAA;QAAA0E,QAAA,EAAK;MAA6B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxChF,OAAA;QACEyE,SAAS,EAAC,aAAa;QACvBQ,OAAO,EAAEA,CAAA,KAAM;UACb5D,eAAe,CAAE6B,IAAI,IAAK,CAACA,IAAI,CAAC;UAChCzB,iBAAiB,CAAC;YAChBC,aAAa,EAAE,EAAE;YACjBC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACR0D,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAE;QAAAZ,QAAA,EAEDtD,YAAY,GAAG,WAAW,GAAG;MAA0B;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EACR5D,YAAY,iBACXpB,OAAA;QAAKyE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1E,OAAA;UAAMoG,QAAQ,EAAE5C,cAAe;UAAAkB,QAAA,gBAC7B1E,OAAA;YACEwF,IAAI,EAAC,MAAM;YACX5D,IAAI,EAAC,eAAe;YACpByE,WAAW,EAAC,2BAAc;YAC1BrD,KAAK,EAAExB,cAAc,CAACE,aAAc;YACpC+D,QAAQ,EAAElC,qBAAsB;YAChC+C,QAAQ;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFhF,OAAA;YAAKyE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC1E,OAAA;cAAOuG,OAAO,EAAC,UAAU;cAAC9B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAExD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhF,OAAA;cACE2D,EAAE,EAAC,UAAU;cACb/B,IAAI,EAAC,UAAU;cACfoB,KAAK,EAAExB,cAAc,CAACG,QAAS;cAC/B8D,QAAQ,EAAElC,qBAAsB;cAChC+C,QAAQ;cACR7B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB1E,OAAA;gBAAQgD,KAAK,EAAC,EAAE;gBAACwD,QAAQ;gBAAA9B,QAAA,EAAC;cAE1B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7ChF,OAAA;gBAAQgD,KAAK,EAAC,MAAM;gBAAA0B,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChF,OAAA;gBAAQgD,KAAK,EAAC,IAAI;gBAAA0B,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChChF,OAAA;gBAAQgD,KAAK,EAAC,WAAW;gBAAA0B,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7ChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3ChF,OAAA;gBAAQgD,KAAK,EAAC,KAAK;gBAAA0B,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhF,OAAA;YACEwF,IAAI,EAAC,MAAM;YACX5D,IAAI,EAAC,MAAM;YACXyE,WAAW,EAAC,QAAK;YACjBrD,KAAK,EAAExB,cAAc,CAACI,IAAK;YAC3B6D,QAAQ,EAAElC,qBAAsB;YAChC+C,QAAQ;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eASFhF,OAAA;YAAQyE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eACDhF,OAAA;QAAA0E,QAAA,EACGpD,YAAY,CAACyE,GAAG,CAAC,CAACU,OAAO,EAAErC,KAAK,kBAC/BpE,OAAA;UAEEkF,KAAK,EAAE;YACLwB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,YAAY,EAAE;UAChB,CAAE;UAAAlC,QAAA,gBAEF1E,OAAA;YAAA0E,QAAA,GACG+B,OAAO,CAAC7E,IAAI,EAAC,KAAG,EAAC6E,OAAO,CAAC9E,QAAQ,EAAC,IAAE,EAAC8E,OAAO,CAAC/E,aAAa,EAAC,GAC9D;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEPhF,OAAA;YACEkF,KAAK,EAAE;cAAEQ,UAAU,EAAE,MAAM;cAAEP,MAAM,EAAE;YAAU,CAAE;YACjDF,OAAO,EAAEA,CAAA,KAAMd,mBAAmB,CAACC,KAAK,CAAE;YAC1CK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC3B;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAjBJZ,KAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBR,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENhF,OAAA;MAAKyE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B1E,OAAA;QAAQyE,SAAS,EAAC,oBAAoB;QAACQ,OAAO,EAAE3E,MAAO;QAAAoE,QAAA,EAAC;MAExD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5E,EAAA,CAxWQD,OAAO;EAAA,QACoBT,OAAO,EAIHE,UAAU;AAAA;AAAAiH,EAAA,GALzC1G,OAAO;AA0WhB,eAAeA,OAAO;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}