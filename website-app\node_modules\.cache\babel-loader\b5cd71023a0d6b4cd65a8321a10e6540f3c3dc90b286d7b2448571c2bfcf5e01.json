{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport io from \"socket.io-client\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { IoCallSharp } from \"react-icons/io5\";\nimport { FaVideo } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Chat({\n  chats,\n  ring\n}) {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const chatEndRef = useRef(null);\n  const [message, setMessage] = useState(\"\");\n  const [chat, setChat] = useState([]);\n  const messageHandled = useRef(false);\n  const socket = io(\"http://localhost:8080\");\n  useEffect(() => {\n    const fetchMessages = async () => {\n      if (loading) return;\n      try {\n        const response = await fetch(\"http://localhost:8080/api/chat/getMessages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            user\n          })\n        });\n        const data = await response.json();\n        console.log(data);\n        if (Array.isArray(data)) {\n          const formattedData = data.map(msg => ({\n            ...msg,\n            isUser: msg.sender._id === user._id\n          }));\n          setChat(formattedData);\n        }\n      } catch (err) {\n        console.error(\"Error fetching messages:\", err);\n      }\n    };\n    fetchMessages();\n\n    // socket.on(\"receive_message\", (data) => {\n    //   console.log(!messageHandled.current)\n    //   console.log(data.sender._id !== user._id)\n    //   if(!messageHandled.current&&data.sender._id !== user._id){\n    //     messageHandled.current = true;\n\n    //     const newMessage = {\n    //     ...data,\n    //     isUser: data.sender._id === user._id,\n    //   };\n    //   console.log(\"day la chat \",chats)\n    //   if(!chats){ring()}\n    //   setChat((prev) => [...prev, newMessage]);\n    //   setTimeout(() => {\n    //     messageHandled.current = false;  // Đặt lại để xử lý tin nhắn mới\n    //   }, 1000); // Ví dụ reset sau 1 giây\n    //   }\n\n    // });\n\n    // // Cleanup khi component unmount\n    // return () => {\n    //   socket.disconnect();\n    // };\n  }, [loading, user]);\n  useEffect(() => {\n    socket.on(\"receive_message\", data => {\n      console.log(!messageHandled.current);\n      console.log(data.sender._id !== user._id);\n      if (!messageHandled.current && data.sender._id !== user._id) {\n        messageHandled.current = true;\n        const newMessage = {\n          ...data,\n          isUser: data.sender._id === user._id\n        };\n        console.log(\"day la chat \", chats);\n        if (!chats) {\n          ring();\n        }\n        setChat(prev => [...prev, newMessage]);\n        setTimeout(() => {\n          messageHandled.current = false; // Đặt lại để xử lý tin nhắn mới\n        }, 1000); // Ví dụ reset sau 1 giây\n      }\n    });\n\n    // Cleanup khi component unmount\n    return () => {\n      socket.disconnect();\n    };\n  }, [ring]);\n  useEffect(() => {\n    var _chatEndRef$current;\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [chat]);\n  const sendMessage = () => {\n    if (message.trim() !== \"\") {\n      const newMessage = {\n        sender: user,\n        owner: user.id_owner,\n        content: message\n      };\n      socket.emit(\"send_message\", newMessage);\n      setChat(prev => [...prev, {\n        ...{\n          sender: user,\n          content: message\n        },\n        isUser: true\n      }]);\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.container,\n      display: chats ? \"block\" : \"none\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user === null || user === void 0 ? void 0 : user.avatar,\n          alt: \"Avatar\",\n          style: styles.headerAvatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.headerName,\n          children: (user === null || user === void 0 ? void 0 : user.name) || \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerRight,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(IoCallSharp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.chatWindow,\n      children: [chat.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.messageContainer,\n          justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\"\n        },\n        children: [!msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.message,\n            backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\"\n          },\n          children: msg.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chatEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.inputContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        placeholder: \"Type a message...\",\n        style: styles.input\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        style: styles.button,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n_s(Chat, \"FpIjcFX2zl0YTXP6P8P7jywAY0I=\", false, function () {\n  return [useAuth];\n});\n_c = Chat;\nconst styles = {\n  container: {\n    width: \"300px\",\n    margin: \"20px auto\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"5px\",\n    overflow: \"hidden\",\n    fontFamily: \"Arial, sans-serif\",\n    position: \"fixed\",\n    right: \"150px\",\n    bottom: \"95px\",\n    zIndex: 1000\n  },\n  chatWindow: {\n    height: \"300px\",\n    overflowY: \"scroll\",\n    padding: \"10px\",\n    backgroundColor: \"#f1f1f1\"\n  },\n  messageContainer: {\n    display: \"flex\",\n    alignItems: \"center\",\n    marginBottom: \"10px\"\n  },\n  avatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    margin: \"0 10px\"\n  },\n  message: {\n    padding: \"8px 12px\",\n    borderRadius: \"10px\",\n    maxWidth: \"200px\",\n    fontSize: \"14px\"\n  },\n  inputContainer: {\n    display: \"flex\",\n    borderTop: \"1px solid #ccc\",\n    padding: \"10px\",\n    backgroundColor: \"white\"\n  },\n  input: {\n    flex: 1,\n    padding: \"8px\",\n    fontSize: \"14px\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"4px\",\n    outline: \"none\"\n  },\n  button: {\n    padding: \"8px 12px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\",\n    marginLeft: \"5px\"\n  },\n  header: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"10px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    borderBottom: \"1px solid #ccc\"\n  },\n  headerLeft: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  headerAvatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    marginRight: \"10px\"\n  },\n  headerName: {\n    fontSize: \"16px\",\n    fontWeight: \"bold\"\n  },\n  headerRight: {\n    display: \"flex\",\n    gap: \"5px\"\n  },\n  headerButton: {\n    padding: \"5px 10px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#0056b3\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\"\n\n    // Các kiểu khác giữ nguyên\n  }\n};\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "io", "useAuth", "IoCallSharp", "FaVideo", "jsxDEV", "_jsxDEV", "Cha<PERSON>", "chats", "ring", "_s", "user", "loading", "chatEndRef", "message", "setMessage", "chat", "setChat", "messageHandled", "socket", "fetchMessages", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "console", "log", "Array", "isArray", "formattedData", "map", "msg", "isUser", "sender", "_id", "err", "error", "on", "current", "newMessage", "prev", "setTimeout", "disconnect", "_chatEndRef$current", "scrollIntoView", "behavior", "sendMessage", "trim", "owner", "id_owner", "content", "emit", "style", "styles", "container", "display", "children", "header", "headerLeft", "src", "avatar", "alt", "headerAvatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "headerName", "name", "headerRight", "headerButton", "chatWindow", "index", "messageContainer", "justifyContent", "backgroundColor", "ref", "inputContainer", "type", "value", "onChange", "e", "target", "placeholder", "input", "onClick", "button", "_c", "width", "margin", "border", "borderRadius", "overflow", "fontFamily", "position", "right", "bottom", "zIndex", "height", "overflowY", "padding", "alignItems", "marginBottom", "max<PERSON><PERSON><PERSON>", "fontSize", "borderTop", "flex", "outline", "color", "cursor", "marginLeft", "borderBottom", "marginRight", "fontWeight", "gap", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport io from \"socket.io-client\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { IoCallSharp } from \"react-icons/io5\";\r\nimport { FaVideo } from \"react-icons/fa\";\r\n\r\nfunction Chat({ chats, ring }) {\r\n  const { user, loading } = useAuth();\r\n  const chatEndRef = useRef(null);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [chat, setChat] = useState([]);\r\n  const messageHandled = useRef(false);\r\n  const socket = io(\"http://localhost:8080\");\r\n  useEffect(() => {\r\n    const fetchMessages = async () => {\r\n      if (loading) return;\r\n      try {\r\n        const response = await fetch(\r\n          \"http://localhost:8080/api/chat/getMessages\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({ user }),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        console.log(data);\r\n        if (Array.isArray(data)) {\r\n          const formattedData = data.map((msg) => ({\r\n            ...msg,\r\n            isUser: msg.sender._id === user._id,\r\n          }));\r\n          setChat(formattedData);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching messages:\", err);\r\n      }\r\n    };\r\n\r\n    fetchMessages();\r\n\r\n    // socket.on(\"receive_message\", (data) => {\r\n    //   console.log(!messageHandled.current)\r\n    //   console.log(data.sender._id !== user._id)\r\n    //   if(!messageHandled.current&&data.sender._id !== user._id){\r\n    //     messageHandled.current = true;\r\n\r\n    //     const newMessage = {\r\n    //     ...data,\r\n    //     isUser: data.sender._id === user._id,\r\n    //   };\r\n    //   console.log(\"day la chat \",chats)\r\n    //   if(!chats){ring()}\r\n    //   setChat((prev) => [...prev, newMessage]);\r\n    //   setTimeout(() => {\r\n    //     messageHandled.current = false;  // Đặt lại để xử lý tin nhắn mới\r\n    //   }, 1000); // Ví dụ reset sau 1 giây\r\n    //   }\r\n\r\n    // });\r\n\r\n    // // Cleanup khi component unmount\r\n    // return () => {\r\n    //   socket.disconnect();\r\n    // };\r\n  }, [loading, user]);\r\n  useEffect(() => {\r\n    socket.on(\"receive_message\", (data) => {\r\n      console.log(!messageHandled.current);\r\n      console.log(data.sender._id !== user._id);\r\n      if (!messageHandled.current && data.sender._id !== user._id) {\r\n        messageHandled.current = true;\r\n\r\n        const newMessage = {\r\n          ...data,\r\n          isUser: data.sender._id === user._id,\r\n        };\r\n        console.log(\"day la chat \", chats);\r\n        if (!chats) {\r\n          ring();\r\n        }\r\n        setChat((prev) => [...prev, newMessage]);\r\n        setTimeout(() => {\r\n          messageHandled.current = false; // Đặt lại để xử lý tin nhắn mới\r\n        }, 1000); // Ví dụ reset sau 1 giây\r\n      }\r\n    });\r\n\r\n    // Cleanup khi component unmount\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n  }, [ring]);\r\n  useEffect(() => {\r\n    chatEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [chat]);\r\n  const sendMessage = () => {\r\n    if (message.trim() !== \"\") {\r\n      const newMessage = {\r\n        sender: user,\r\n        owner: user.id_owner,\r\n        content: message,\r\n      };\r\n\r\n      socket.emit(\"send_message\", newMessage);\r\n\r\n      setChat((prev) => [\r\n        ...prev,\r\n        {\r\n          ...{ sender: user, content: message },\r\n          isUser: true,\r\n        },\r\n      ]);\r\n\r\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ ...styles.container, display: chats ? \"block\" : \"none\" }}>\r\n      {/* Header */}\r\n      <div style={styles.header}>\r\n        <div style={styles.headerLeft}>\r\n          <img src={user?.avatar} alt=\"Avatar\" style={styles.headerAvatar} />\r\n          <span style={styles.headerName}>{user?.name || \"Chat\"}</span>\r\n        </div>\r\n        <div style={styles.headerRight}>\r\n          <button style={styles.headerButton}>\r\n            <IoCallSharp />\r\n          </button>\r\n          <button style={styles.headerButton}>\r\n            <FaVideo />\r\n          </button>\r\n        </div>\r\n      </div>\r\n      {/* Chat Window */}\r\n      <div style={styles.chatWindow}>\r\n        {chat.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              ...styles.messageContainer,\r\n              justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\",\r\n            }}\r\n          >\r\n            {!msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n            <div\r\n              style={{\r\n                ...styles.message,\r\n                backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\",\r\n              }}\r\n            >\r\n              {msg.content}\r\n            </div>\r\n            {msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n          </div>\r\n        ))}\r\n        <div ref={chatEndRef} />\r\n      </div>\r\n      {/* Input Field */}\r\n      <div style={styles.inputContainer}>\r\n        <input\r\n          type=\"text\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          placeholder=\"Type a message...\"\r\n          style={styles.input}\r\n        />\r\n        <button onClick={sendMessage} style={styles.button}>\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst styles = {\r\n  container: {\r\n    width: \"300px\",\r\n    margin: \"20px auto\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"5px\",\r\n    overflow: \"hidden\",\r\n    fontFamily: \"Arial, sans-serif\",\r\n    position: \"fixed\",\r\n    right: \"150px\",\r\n    bottom: \"95px\",\r\n    zIndex: 1000,\r\n  },\r\n  chatWindow: {\r\n    height: \"300px\",\r\n    overflowY: \"scroll\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#f1f1f1\",\r\n  },\r\n  messageContainer: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    marginBottom: \"10px\",\r\n  },\r\n  avatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    margin: \"0 10px\",\r\n  },\r\n  message: {\r\n    padding: \"8px 12px\",\r\n    borderRadius: \"10px\",\r\n    maxWidth: \"200px\",\r\n    fontSize: \"14px\",\r\n  },\r\n  inputContainer: {\r\n    display: \"flex\",\r\n    borderTop: \"1px solid #ccc\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"white\",\r\n  },\r\n  input: {\r\n    flex: 1,\r\n    padding: \"8px\",\r\n    fontSize: \"14px\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"4px\",\r\n    outline: \"none\",\r\n  },\r\n  button: {\r\n    padding: \"8px 12px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n    marginLeft: \"5px\",\r\n  },\r\n  header: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    borderBottom: \"1px solid #ccc\",\r\n  },\r\n  headerLeft: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n  },\r\n  headerAvatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    marginRight: \"10px\",\r\n  },\r\n  headerName: {\r\n    fontSize: \"16px\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  headerRight: {\r\n    display: \"flex\",\r\n    gap: \"5px\",\r\n  },\r\n  headerButton: {\r\n    padding: \"5px 10px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#0056b3\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n\r\n    // Các kiểu khác giữ nguyên\r\n  },\r\n};\r\n\r\nexport default Chat;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,IAAIA,CAAC;EAAEC,KAAK;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAMW,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMoB,cAAc,GAAGlB,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMmB,MAAM,GAAGlB,EAAE,CAAC,uBAAuB,CAAC;EAC1CF,SAAS,CAAC,MAAM;IACd,MAAMqB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAIR,OAAO,EAAE;MACb,IAAI;QACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAC1B,4CAA4C,EAC5C;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhB;UAAK,CAAC;QAC/B,CACF,CAAC;QACD,MAAMiB,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;QACjB,IAAII,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACvB,MAAMM,aAAa,GAAGN,IAAI,CAACO,GAAG,CAAEC,GAAG,KAAM;YACvC,GAAGA,GAAG;YACNC,MAAM,EAAED,GAAG,CAACE,MAAM,CAACC,GAAG,KAAK5B,IAAI,CAAC4B;UAClC,CAAC,CAAC,CAAC;UACHtB,OAAO,CAACiB,aAAa,CAAC;QACxB;MACF,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZV,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;MAChD;IACF,CAAC;IAEDpB,aAAa,CAAC,CAAC;;IAEf;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IAEA;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACR,OAAO,EAAED,IAAI,CAAC,CAAC;EACnBZ,SAAS,CAAC,MAAM;IACdoB,MAAM,CAACuB,EAAE,CAAC,iBAAiB,EAAGd,IAAI,IAAK;MACrCE,OAAO,CAACC,GAAG,CAAC,CAACb,cAAc,CAACyB,OAAO,CAAC;MACpCb,OAAO,CAACC,GAAG,CAACH,IAAI,CAACU,MAAM,CAACC,GAAG,KAAK5B,IAAI,CAAC4B,GAAG,CAAC;MACzC,IAAI,CAACrB,cAAc,CAACyB,OAAO,IAAIf,IAAI,CAACU,MAAM,CAACC,GAAG,KAAK5B,IAAI,CAAC4B,GAAG,EAAE;QAC3DrB,cAAc,CAACyB,OAAO,GAAG,IAAI;QAE7B,MAAMC,UAAU,GAAG;UACjB,GAAGhB,IAAI;UACPS,MAAM,EAAET,IAAI,CAACU,MAAM,CAACC,GAAG,KAAK5B,IAAI,CAAC4B;QACnC,CAAC;QACDT,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEvB,KAAK,CAAC;QAClC,IAAI,CAACA,KAAK,EAAE;UACVC,IAAI,CAAC,CAAC;QACR;QACAQ,OAAO,CAAE4B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;QACxCE,UAAU,CAAC,MAAM;UACf5B,cAAc,CAACyB,OAAO,GAAG,KAAK,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXxB,MAAM,CAAC4B,UAAU,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACtC,IAAI,CAAC,CAAC;EACVV,SAAS,CAAC,MAAM;IAAA,IAAAiD,mBAAA;IACd,CAAAA,mBAAA,GAAAnC,UAAU,CAAC8B,OAAO,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC,EAAE,CAAClC,IAAI,CAAC,CAAC;EACV,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIrC,OAAO,CAACsC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACzB,MAAMR,UAAU,GAAG;QACjBN,MAAM,EAAE3B,IAAI;QACZ0C,KAAK,EAAE1C,IAAI,CAAC2C,QAAQ;QACpBC,OAAO,EAAEzC;MACX,CAAC;MAEDK,MAAM,CAACqC,IAAI,CAAC,cAAc,EAAEZ,UAAU,CAAC;MAEvC3B,OAAO,CAAE4B,IAAI,IAAK,CAChB,GAAGA,IAAI,EACP;QACE,GAAG;UAAEP,MAAM,EAAE3B,IAAI;UAAE4C,OAAO,EAAEzC;QAAQ,CAAC;QACrCuB,MAAM,EAAE;MACV,CAAC,CACF,CAAC;MAEFtB,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAED,oBACET,OAAA;IAAKmD,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACC,SAAS;MAAEC,OAAO,EAAEpD,KAAK,GAAG,OAAO,GAAG;IAAO,CAAE;IAAAqD,QAAA,gBAErEvD,OAAA;MAAKmD,KAAK,EAAEC,MAAM,CAACI,MAAO;MAAAD,QAAA,gBACxBvD,OAAA;QAAKmD,KAAK,EAAEC,MAAM,CAACK,UAAW;QAAAF,QAAA,gBAC5BvD,OAAA;UAAK0D,GAAG,EAAErD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACS;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEjE,OAAA;UAAMmD,KAAK,EAAEC,MAAM,CAACc,UAAW;UAAAX,QAAA,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,IAAI,KAAI;QAAM;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNjE,OAAA;QAAKmD,KAAK,EAAEC,MAAM,CAACgB,WAAY;QAAAb,QAAA,gBAC7BvD,OAAA;UAAQmD,KAAK,EAAEC,MAAM,CAACiB,YAAa;UAAAd,QAAA,eACjCvD,OAAA,CAACH,WAAW;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACTjE,OAAA;UAAQmD,KAAK,EAAEC,MAAM,CAACiB,YAAa;UAAAd,QAAA,eACjCvD,OAAA,CAACF,OAAO;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA;MAAKmD,KAAK,EAAEC,MAAM,CAACkB,UAAW;MAAAf,QAAA,GAC3B7C,IAAI,CAACmB,GAAG,CAAC,CAACC,GAAG,EAAEyC,KAAK,kBACnBvE,OAAA;QAEEmD,KAAK,EAAE;UACL,GAAGC,MAAM,CAACoB,gBAAgB;UAC1BC,cAAc,EAAE3C,GAAG,CAACC,MAAM,GAAG,UAAU,GAAG;QAC5C,CAAE;QAAAwB,QAAA,GAED,CAACzB,GAAG,CAACC,MAAM,iBACV/B,OAAA;UAAK0D,GAAG,EAAE5B,GAAG,CAACE,MAAM,CAAC2B,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE,eACDjE,OAAA;UACEmD,KAAK,EAAE;YACL,GAAGC,MAAM,CAAC5C,OAAO;YACjBkE,eAAe,EAAE5C,GAAG,CAACC,MAAM,GAAG,SAAS,GAAG;UAC5C,CAAE;UAAAwB,QAAA,EAEDzB,GAAG,CAACmB;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACLnC,GAAG,CAACC,MAAM,iBACT/B,OAAA;UAAK0D,GAAG,EAAE5B,GAAG,CAACE,MAAM,CAAC2B,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE;MAAA,GAnBIM,KAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN,CAAC,eACFjE,OAAA;QAAK2E,GAAG,EAAEpE;MAAW;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAENjE,OAAA;MAAKmD,KAAK,EAAEC,MAAM,CAACwB,cAAe;MAAArB,QAAA,gBAChCvD,OAAA;QACE6E,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEtE,OAAQ;QACfuE,QAAQ,EAAGC,CAAC,IAAKvE,UAAU,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC5CI,WAAW,EAAC,mBAAmB;QAC/B/B,KAAK,EAAEC,MAAM,CAAC+B;MAAM;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACFjE,OAAA;QAAQoF,OAAO,EAAEvC,WAAY;QAACM,KAAK,EAAEC,MAAM,CAACiC,MAAO;QAAA9B,QAAA,EAAC;MAEpD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7D,EAAA,CA9KQH,IAAI;EAAA,QACeL,OAAO;AAAA;AAAA0F,EAAA,GAD1BrF,IAAI;AAgLb,MAAMmD,MAAM,GAAG;EACbC,SAAS,EAAE;IACTkC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACV,CAAC;EACD1B,UAAU,EAAE;IACV2B,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE;EACnB,CAAC;EACDF,gBAAgB,EAAE;IAChBlB,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACD1C,MAAM,EAAE;IACN4B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBF,MAAM,EAAE;EACV,CAAC;EACDhF,OAAO,EAAE;IACP2F,OAAO,EAAE,UAAU;IACnBT,YAAY,EAAE,MAAM;IACpBY,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACD3B,cAAc,EAAE;IACdtB,OAAO,EAAE,MAAM;IACfkD,SAAS,EAAE,gBAAgB;IAC3BL,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE;EACnB,CAAC;EACDS,KAAK,EAAE;IACLsB,IAAI,EAAE,CAAC;IACPN,OAAO,EAAE,KAAK;IACdI,QAAQ,EAAE,MAAM;IAChBd,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBgB,OAAO,EAAE;EACX,CAAC;EACDrB,MAAM,EAAE;IACNc,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChB7B,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE;EACd,CAAC;EACDrD,MAAM,EAAE;IACNF,OAAO,EAAE,MAAM;IACfmB,cAAc,EAAE,eAAe;IAC/B2B,UAAU,EAAE,QAAQ;IACpBD,OAAO,EAAE,MAAM;IACfzB,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE;EAChB,CAAC;EACDrD,UAAU,EAAE;IACVH,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE;EACd,CAAC;EACDvC,YAAY,EAAE;IACZ0B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBqB,WAAW,EAAE;EACf,CAAC;EACD7C,UAAU,EAAE;IACVqC,QAAQ,EAAE,MAAM;IAChBS,UAAU,EAAE;EACd,CAAC;EACD5C,WAAW,EAAE;IACXd,OAAO,EAAE,MAAM;IACf2D,GAAG,EAAE;EACP,CAAC;EACD5C,YAAY,EAAE;IACZ8B,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChB7B,eAAe,EAAE,SAAS;IAC1BiC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE;;IAER;EACF;AACF,CAAC;AAED,eAAe3G,IAAI;AAAC,IAAAqF,EAAA;AAAA4B,YAAA,CAAA5B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}