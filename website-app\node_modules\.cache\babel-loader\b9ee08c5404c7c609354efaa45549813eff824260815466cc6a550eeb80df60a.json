{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\test\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useContext, useImperativeHandle, forwardRef } from \"react\";\nimport \"../test/index.css\";\nimport { AuthContext } from \"../../components/introduce/AuthContext\";\nimport debounce from \"lodash.debounce\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { faCircleInfo } from \"@fortawesome/free-solid-svg-icons\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet apiFetchOrderHistory;\nconst OrderManagement = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  onCreateOrder,\n  onHistory,\n  openModalDetail,\n  setIdOrder,\n  refOrder,\n  setView,\n  setLoadOrder,\n  setLoadLog,\n  loadOrder\n}) => {\n  _s();\n  const [orders, setOrders] = useState([]);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editedOrder, setEditedOrder] = useState(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [noteDetail, setNoteDetail] = useState(null); // Thay đổi state để theo dõi chỉ số đơn hàng đang chỉnh sửa\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading;\n  // const {user} = useContext(AuthContext)\n  const {\n    user,\n    loading\n  } = useAuth();\n  const createorder = order => {\n    return {\n      id: order._id,\n      tax: order.tax,\n      client: order.nameSupplier,\n      email: order.emailSupplier,\n      status: order.generalStatus,\n      date: order.updatedAt,\n      country: \"vn\",\n      total: order.amount,\n      notes: order.notes || \"\" // Giả sử có trường \"notes\" trong dữ liệu đơn hàng\n    };\n  };\n  const fetchOrder = async keyword => {\n    try {\n      const apiUrl = `http://localhost:8080/api/import/orderHistory/getOrder?search=${keyword}&ownerId=${user === null || user === void 0 ? void 0 : user.id_owner}&userId=${user._id}`;\n      const response = await fetch(apiUrl);\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch the order\");\n      }\n      const data = await response.json();\n      console.log(data, \"new data\");\n      const regurlizationData = data.map(item => createorder(item));\n      setOrders(prev => {\n        const newData = [...regurlizationData];\n        return newData;\n      });\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  useImperativeHandle(refOrder, () => ({\n    fetchOrder\n  }));\n  const updateData = async newData => {\n    try {\n      newData.ownerId = user.id_owner;\n      newData.user = user;\n      const response = await fetch(`http://localhost:8080/api/import/orderHistory/updateOrderhistory`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(newData)\n      });\n      if (!response.ok) {\n        throw new Error(\"Network response was not ok\");\n        return 0;\n      }\n      const data = await response.json(); // Đọc phản hồi trả về\n      console.log(\"Update successful:\", data);\n      return 1;\n    } catch (error) {\n      console.error(\"Error during update:\", error);\n      return 0;\n    }\n  };\n  apiFetchOrderHistory = fetchOrder;\n  const debouncedFetchSuggestions = useCallback(debounce(keyword => {\n    fetchOrder(keyword);\n  }, 500), [user, loading]);\n  useEffect(() => {\n    if (loading) return;\n    console.log(\"debub\");\n    debouncedFetchSuggestions(searchTerm.trim());\n  }, [loading, loadOrder, user]);\n  useEffect(() => {\n    debouncedFetchSuggestions(searchTerm.trim());\n  }, [searchTerm]);\n  const handleSaveClick = async () => {\n    try {\n      const updatedOrders = [...orders];\n      const newOrder = {\n        ...editedOrder,\n        userid: user._id,\n        userName: user.name\n      };\n      newOrder.date = new Date().toISOString();\n      console.log(loadOrder);\n      console.log(\"Đơn hàng mới:\", newOrder);\n      let i = await updateData(newOrder);\n      updatedOrders[editingIndex] = editedOrder;\n      setOrders(updatedOrders);\n      setEditingIndex(null);\n      setNoteDetail(null);\n      if (i) {\n        notify(1, \"you've updated importing goods\", \"Successfully!\");\n      } else {\n        notify(2, \"Error updating data\", \"Failed to update!\");\n      }\n      setLoadOrder(prev => !prev);\n      setLoadLog(prev => !prev);\n    } catch (error) {\n      console.error(\"Error in handleSaveClick:\", error);\n      notify(0, \"Error updating data\", \"Failed to update!\");\n    }\n  };\n  const handleCancelClick = () => {\n    setEditingIndex(null); // Hủy chế độ chỉnh sửa\n    setNoteDetail(null); // Ẩn ghi chú khi hủy\n  };\n  const handleEditClick = (index, order) => {\n    setEditingIndex(index);\n    setEditedOrder({\n      ...order\n    });\n    setNoteDetail(index); // Hiển thị ghi chú khi nhấn \"Edit\" vào đơn hàng cụ thể\n  };\n  const handleEditChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditedOrder(prevOrder => ({\n      ...prevOrder,\n      [name]: value\n    }));\n  };\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n  const transfer = date => {\n    const date2 = new Date(date);\n    if (isNaN(date2)) {\n      return \"Invalid date\";\n    }\n    return date2.toLocaleString(\"vi-VN\", {\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      hour12: false\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-mgmt-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-mgmt-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"order-mgmt-title\",\n        children: \"Order Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-mgmt-header-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"order-mgmt-search\",\n          placeholder: \"Search for...\",\n          value: searchTerm,\n          onChange: handleSearch\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"order-mgmt-create-btn\",\n          onClick: onCreateOrder,\n          children: \"Create Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"order-mgmt-history-btn\",\n          onClick: onHistory,\n          children: \"L\\u1ECBch s\\u1EED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"order-mgmt-table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Client\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Country\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: orders.map((order, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: [\"#\", order.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"client\",\n                  value: editedOrder.client,\n                  onChange: handleEditChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: editedOrder.email,\n                  onChange: handleEditChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [order.client, \" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 40\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: order.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                name: \"date\",\n                value: editedOrder.date,\n                onChange: handleEditChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 23\n              }, this) : transfer(order.date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"status\",\n                value: editedOrder.status,\n                onChange: handleEditChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"deliveried\",\n                  children: \"Deliveried\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Canceled\",\n                  children: \"Canceled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `order-mgmt-status ${order.status.toLowerCase()}`,\n                children: order.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"country\",\n                value: editedOrder.country,\n                onChange: handleEditChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 23\n              }, this) : order.country\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"total\",\n                style: {\n                  width: \"100%\"\n                },\n                value: editedOrder.total,\n                onChange: handleEditChange,\n                step: \"0.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 23\n              }, this) : `$${order.total}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: editingIndex === index ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"order-mgmt-button save\",\n                  onClick: handleSaveClick,\n                  children: \"Save\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"order-mgmt-button cancel\",\n                  onClick: handleCancelClick,\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"order-mgmt-button edit\",\n                    onClick: () => handleEditClick(index, order),\n                    style: {\n                      margin: 0\n                    },\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faCircleInfo,\n                    onClick: () => {\n                      setView(true);\n                      openModalDetail();\n                      setIdOrder(order.id);\n                    },\n                    style: {\n                      height: \"24px\",\n                      witdh: \"24px\",\n                      padding: \"8px\"\n                    },\n                    className: \"infoDetail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 17\n          }, this), editingIndex === index && noteDetail === index && /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: \"7\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                style: {\n                  outline: \"none\",\n                  border: \"none\"\n                },\n                type: \"text\",\n                name: \"notes\",\n                value: editedOrder.notes,\n                onChange: handleEditChange,\n                placeholder: \"Add your notes here\",\n                className: \"note-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 19\n          }, this)]\n        }, order.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 7\n  }, this);\n}, \"m5zwyrbbo7itUYNx3BZrd4Afg2c=\", false, function () {\n  return [useAuth];\n})), \"m5zwyrbbo7itUYNx3BZrd4Afg2c=\", false, function () {\n  return [useAuth];\n});\n_c2 = OrderManagement;\nexport default OrderManagement;\nexport { apiFetchOrderHistory };\nvar _c, _c2;\n$RefreshReg$(_c, \"OrderManagement$forwardRef\");\n$RefreshReg$(_c2, \"OrderManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useContext", "useImperativeHandle", "forwardRef", "AuthContext", "debounce", "FontAwesomeIcon", "faCircleInfo", "useAuth", "useLoading", "notify", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "apiFetchOrderHistory", "OrderManagement", "_s", "_c", "onCreateOrder", "onHistory", "openModalDetail", "setIdOrder", "refOrder", "<PERSON><PERSON><PERSON><PERSON>", "setLoadOrder", "setLoadLog", "loadOrder", "orders", "setOrders", "editingIndex", "setEditingIndex", "editedOrder", "setEditedOrder", "searchTerm", "setSearchTerm", "noteDetail", "setNoteDetail", "startLoading", "stopLoading", "user", "loading", "createorder", "order", "id", "_id", "tax", "client", "nameSupplier", "email", "emailSupplier", "status", "general<PERSON><PERSON><PERSON>", "date", "updatedAt", "country", "total", "amount", "notes", "fetchOrder", "keyword", "apiUrl", "id_owner", "response", "fetch", "ok", "Error", "data", "json", "console", "log", "regurlizationData", "map", "item", "prev", "newData", "error", "updateData", "ownerId", "method", "headers", "body", "JSON", "stringify", "debouncedFetchSuggestions", "trim", "handleSaveClick", "updatedOrders", "newOrder", "userid", "userName", "name", "Date", "toISOString", "i", "handleCancelClick", "handleEditClick", "index", "handleEditChange", "e", "value", "target", "prevOrder", "handleSearch", "transfer", "date2", "isNaN", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "toLowerCase", "style", "width", "step", "display", "flexDirection", "alignItems", "margin", "icon", "height", "wit<PERSON>", "padding", "colSpan", "outline", "border", "_c2", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/test/index.js"], "sourcesContent": ["import React, {\r\n  useState,\r\n  useEffect,\r\n  useCallback,\r\n  useContext,\r\n  useImperativeHandle,\r\n  forwardRef,\r\n} from \"react\";\r\nimport \"../test/index.css\";\r\nimport { AuthContext } from \"../../components/introduce/AuthContext\";\r\nimport debounce from \"lodash.debounce\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faCircleInfo } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nlet apiFetchOrderHistory;\r\n\r\nconst OrderManagement = forwardRef(\r\n  ({\r\n    onCreateOrder,\r\n    onHistory,\r\n    openModalDetail,\r\n    setIdOrder,\r\n    refOrder,\r\n    setView,\r\n    setLoadOrder,\r\n    setLoadLog,\r\n    loadOrder,\r\n  }) => {\r\n    const [orders, setOrders] = useState([]);\r\n    const [editingIndex, setEditingIndex] = useState(null);\r\n    const [editedOrder, setEditedOrder] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState(\"\");\r\n    const [noteDetail, setNoteDetail] = useState(null); // Thay đổi state để theo dõi chỉ số đơn hàng đang chỉnh sửa\r\n    const { startLoading, stopLoading } = useLoading;\r\n    // const {user} = useContext(AuthContext)\r\n    const { user, loading } = useAuth();\r\n\r\n    const createorder = (order) => {\r\n      return {\r\n        id: order._id,\r\n        tax: order.tax,\r\n        client: order.nameSupplier,\r\n        email: order.emailSupplier,\r\n        status: order.generalStatus,\r\n        date: order.updatedAt,\r\n        country: \"vn\",\r\n        total: order.amount,\r\n        notes: order.notes || \"\", // Giả sử có trường \"notes\" trong dữ liệu đơn hàng\r\n      };\r\n    };\r\n\r\n    const fetchOrder = async (keyword) => {\r\n      try {\r\n        const apiUrl = `http://localhost:8080/api/import/orderHistory/getOrder?search=${keyword}&ownerId=${user?.id_owner}&userId=${user._id}`;\r\n        const response = await fetch(apiUrl);\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch the order\");\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log(data, \"new data\");\r\n        const regurlizationData = data.map((item) => createorder(item));\r\n        setOrders((prev) => {\r\n          const newData = [...regurlizationData];\r\n\r\n          return newData;\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Error:\", error);\r\n      }\r\n    };\r\n    useImperativeHandle(refOrder, () => ({\r\n      fetchOrder,\r\n    }));\r\n    const updateData = async (newData) => {\r\n      try {\r\n        newData.ownerId = user.id_owner;\r\n        newData.user = user;\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/import/orderHistory/updateOrderhistory`,\r\n          {\r\n            method: \"PUT\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify(newData),\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Network response was not ok\");\r\n          return 0;\r\n        }\r\n\r\n        const data = await response.json(); // Đọc phản hồi trả về\r\n        console.log(\"Update successful:\", data);\r\n        return 1;\r\n      } catch (error) {\r\n        console.error(\"Error during update:\", error);\r\n        return 0;\r\n      }\r\n    };\r\n    apiFetchOrderHistory = fetchOrder;\r\n    const debouncedFetchSuggestions = useCallback(\r\n      debounce((keyword) => {\r\n        fetchOrder(keyword);\r\n      }, 500),\r\n      [user, loading]\r\n    );\r\n    useEffect(() => {\r\n      if (loading) return;\r\n      console.log(\"debub\");\r\n      debouncedFetchSuggestions(searchTerm.trim());\r\n    }, [loading, loadOrder, user]);\r\n    useEffect(() => {\r\n      debouncedFetchSuggestions(searchTerm.trim());\r\n    }, [searchTerm]);\r\n\r\n    const handleSaveClick = async () => {\r\n      try {\r\n        const updatedOrders = [...orders];\r\n        const newOrder = {\r\n          ...editedOrder,\r\n          userid: user._id,\r\n          userName: user.name,\r\n        };\r\n        newOrder.date = new Date().toISOString();\r\n\r\n        console.log(loadOrder);\r\n        console.log(\"Đơn hàng mới:\", newOrder);\r\n\r\n        let i = await updateData(newOrder);\r\n\r\n        updatedOrders[editingIndex] = editedOrder;\r\n        setOrders(updatedOrders);\r\n        setEditingIndex(null);\r\n        setNoteDetail(null);\r\n        if (i) {\r\n          notify(1, \"you've updated importing goods\", \"Successfully!\");\r\n        } else {\r\n          notify(2, \"Error updating data\", \"Failed to update!\");\r\n        }\r\n        setLoadOrder((prev) => !prev);\r\n        setLoadLog((prev) => !prev);\r\n      } catch (error) {\r\n        console.error(\"Error in handleSaveClick:\", error);\r\n        notify(0, \"Error updating data\", \"Failed to update!\");\r\n      }\r\n    };\r\n\r\n    const handleCancelClick = () => {\r\n      setEditingIndex(null); // Hủy chế độ chỉnh sửa\r\n      setNoteDetail(null); // Ẩn ghi chú khi hủy\r\n    };\r\n\r\n    const handleEditClick = (index, order) => {\r\n      setEditingIndex(index);\r\n      setEditedOrder({ ...order });\r\n      setNoteDetail(index); // Hiển thị ghi chú khi nhấn \"Edit\" vào đơn hàng cụ thể\r\n    };\r\n\r\n    const handleEditChange = (e) => {\r\n      const { name, value } = e.target;\r\n      setEditedOrder((prevOrder) => ({ ...prevOrder, [name]: value }));\r\n    };\r\n\r\n    const handleSearch = (e) => {\r\n      setSearchTerm(e.target.value);\r\n    };\r\n    const transfer = (date) => {\r\n      const date2 = new Date(date);\r\n      if (isNaN(date2)) {\r\n        return \"Invalid date\";\r\n      }\r\n      return date2.toLocaleString(\"vi-VN\", {\r\n        year: \"numeric\",\r\n        month: \"2-digit\",\r\n        day: \"2-digit\",\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    };\r\n    return (\r\n      <div className=\"order-mgmt-container\">\r\n        <div className=\"order-mgmt-header\">\r\n          <h2 className=\"order-mgmt-title\">Order Status</h2>\r\n          <div className=\"order-mgmt-header-controls\">\r\n            <input\r\n              type=\"text\"\r\n              className=\"order-mgmt-search\"\r\n              placeholder=\"Search for...\"\r\n              value={searchTerm}\r\n              onChange={handleSearch}\r\n            />\r\n            <button className=\"order-mgmt-create-btn\" onClick={onCreateOrder}>\r\n              Create Order\r\n            </button>\r\n            <button className=\"order-mgmt-history-btn\" onClick={onHistory}>\r\n              Lịch sử\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <table className=\"order-mgmt-table\">\r\n          <thead>\r\n            <tr>\r\n              <th>Order</th>\r\n              <th>Client</th>\r\n              <th>Date</th>\r\n              <th>Status</th>\r\n              <th>Country</th>\r\n              <th>Total</th>\r\n              <th>Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {orders.map((order, index) => (\r\n              <React.Fragment key={order.id}>\r\n                <tr>\r\n                  <td>#{order.id}</td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <div>\r\n                        <input\r\n                          type=\"text\"\r\n                          name=\"client\"\r\n                          value={editedOrder.client}\r\n                          onChange={handleEditChange}\r\n                        />\r\n                        <input\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          value={editedOrder.email}\r\n                          onChange={handleEditChange}\r\n                        />\r\n                      </div>\r\n                    ) : (\r\n                      <div>\r\n                        {order.client} <br />\r\n                        <small>{order.email}</small>\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <input\r\n                        type=\"date\"\r\n                        name=\"date\"\r\n                        value={editedOrder.date}\r\n                        onChange={handleEditChange}\r\n                      />\r\n                    ) : (\r\n                      transfer(order.date)\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <select\r\n                        name=\"status\"\r\n                        value={editedOrder.status}\r\n                        onChange={handleEditChange}\r\n                      >\r\n                        <option value=\"Pending\">Pending</option>\r\n                        <option value=\"deliveried\">Deliveried</option>\r\n                        <option value=\"Canceled\">Canceled</option>\r\n                      </select>\r\n                    ) : (\r\n                      <span\r\n                        className={`order-mgmt-status ${order.status.toLowerCase()}`}\r\n                      >\r\n                        {order.status}\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"country\"\r\n                        value={editedOrder.country}\r\n                        onChange={handleEditChange}\r\n                      />\r\n                    ) : (\r\n                      order.country\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <input\r\n                        type=\"number\"\r\n                        name=\"total\"\r\n                        style={{ width: \"100%\" }}\r\n                        value={editedOrder.total}\r\n                        onChange={handleEditChange}\r\n                        step=\"0.01\"\r\n                      />\r\n                    ) : (\r\n                      `$${order.total}`\r\n                    )}\r\n                  </td>\r\n                  <td>\r\n                    {editingIndex === index ? (\r\n                      <>\r\n                        <button\r\n                          className=\"order-mgmt-button save\"\r\n                          onClick={handleSaveClick}\r\n                        >\r\n                          Save\r\n                        </button>\r\n                        <button\r\n                          className=\"order-mgmt-button cancel\"\r\n                          onClick={handleCancelClick}\r\n                        >\r\n                          Cancel\r\n                        </button>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <div\r\n                          style={{\r\n                            display: \"flex\",\r\n                            flexDirection: \"column\",\r\n                            alignItems: \"center\",\r\n                          }}\r\n                        >\r\n                          <button\r\n                            className=\"order-mgmt-button edit\"\r\n                            onClick={() => handleEditClick(index, order)}\r\n                            style={{ margin: 0 }}\r\n                          >\r\n                            ✏️\r\n                          </button>\r\n                          <FontAwesomeIcon\r\n                            icon={faCircleInfo}\r\n                            onClick={() => {\r\n                              setView(true);\r\n                              openModalDetail();\r\n                              setIdOrder(order.id);\r\n                            }}\r\n                            style={{\r\n                              height: \"24px\",\r\n                              witdh: \"24px\",\r\n                              padding: \"8px\",\r\n                            }}\r\n                            className=\"infoDetail\"\r\n                          />\r\n                        </div>\r\n                      </>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n\r\n                {/* Render dòng ghi chú dưới mỗi đơn hàng khi nhấn Edit */}\r\n                {editingIndex === index && noteDetail === index && (\r\n                  <tr>\r\n                    <td colSpan=\"7\">\r\n                      <input\r\n                        style={{ outline: \"none\", border: \"none\" }}\r\n                        type=\"text\"\r\n                        name=\"notes\"\r\n                        value={editedOrder.notes}\r\n                        onChange={handleEditChange}\r\n                        placeholder=\"Add your notes here\"\r\n                        className=\"note-input\"\r\n                      />\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </React.Fragment>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nexport default OrderManagement;\r\n\r\nexport { apiFetchOrderHistory };\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,mBAAmB,EACnBC,UAAU,QACL,OAAO;AACd,OAAO,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,wCAAwC;AACpE,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACpE,IAAIC,oBAAoB;AAExB,MAAMC,eAAe,gBAAAC,EAAA,cAAGd,UAAU,CAAAe,EAAA,GAAAD,EAAA,CAChC,CAAC;EACCE,aAAa;EACbC,SAAS;EACTC,eAAe;EACfC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,YAAY;EACZC,UAAU;EACVC;AACF,CAAC,KAAK;EAAAV,EAAA;EACJ,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM;IAAEwC,YAAY;IAAEC;EAAY,CAAC,GAAG9B,UAAU;EAChD;EACA,MAAM;IAAE+B,IAAI;IAAEC;EAAQ,CAAC,GAAGjC,OAAO,CAAC,CAAC;EAEnC,MAAMkC,WAAW,GAAIC,KAAK,IAAK;IAC7B,OAAO;MACLC,EAAE,EAAED,KAAK,CAACE,GAAG;MACbC,GAAG,EAAEH,KAAK,CAACG,GAAG;MACdC,MAAM,EAAEJ,KAAK,CAACK,YAAY;MAC1BC,KAAK,EAAEN,KAAK,CAACO,aAAa;MAC1BC,MAAM,EAAER,KAAK,CAACS,aAAa;MAC3BC,IAAI,EAAEV,KAAK,CAACW,SAAS;MACrBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAEb,KAAK,CAACc,MAAM;MACnBC,KAAK,EAAEf,KAAK,CAACe,KAAK,IAAI,EAAE,CAAE;IAC5B,CAAC;EACH,CAAC;EAED,MAAMC,UAAU,GAAG,MAAOC,OAAO,IAAK;IACpC,IAAI;MACF,MAAMC,MAAM,GAAG,iEAAiED,OAAO,YAAYpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,WAAWtB,IAAI,CAACK,GAAG,EAAE;MACtI,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAACH,MAAM,CAAC;MAEpC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAACH,IAAI,EAAE,UAAU,CAAC;MAC7B,MAAMI,iBAAiB,GAAGJ,IAAI,CAACK,GAAG,CAAEC,IAAI,IAAK/B,WAAW,CAAC+B,IAAI,CAAC,CAAC;MAC/D5C,SAAS,CAAE6C,IAAI,IAAK;QAClB,MAAMC,OAAO,GAAG,CAAC,GAAGJ,iBAAiB,CAAC;QAEtC,OAAOI,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EACD1E,mBAAmB,CAACqB,QAAQ,EAAE,OAAO;IACnCoC;EACF,CAAC,CAAC,CAAC;EACH,MAAMkB,UAAU,GAAG,MAAOF,OAAO,IAAK;IACpC,IAAI;MACFA,OAAO,CAACG,OAAO,GAAGtC,IAAI,CAACsB,QAAQ;MAC/Ba,OAAO,CAACnC,IAAI,GAAGA,IAAI;MACnB,MAAMuB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kEAAkE,EAClE;QACEe,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,OAAO;MAC9B,CACF,CAAC;MAED,IAAI,CAACZ,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAC9C,OAAO,CAAC;MACV;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;MACpCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEH,IAAI,CAAC;MACvC,OAAO,CAAC;IACV,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,CAAC;IACV;EACF,CAAC;EACD7D,oBAAoB,GAAG4C,UAAU;EACjC,MAAMyB,yBAAyB,GAAGpF,WAAW,CAC3CK,QAAQ,CAAEuD,OAAO,IAAK;IACpBD,UAAU,CAACC,OAAO,CAAC;EACrB,CAAC,EAAE,GAAG,CAAC,EACP,CAACpB,IAAI,EAAEC,OAAO,CAChB,CAAC;EACD1C,SAAS,CAAC,MAAM;IACd,IAAI0C,OAAO,EAAE;IACb4B,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;IACpBc,yBAAyB,CAAClD,UAAU,CAACmD,IAAI,CAAC,CAAC,CAAC;EAC9C,CAAC,EAAE,CAAC5C,OAAO,EAAEd,SAAS,EAAEa,IAAI,CAAC,CAAC;EAC9BzC,SAAS,CAAC,MAAM;IACdqF,yBAAyB,CAAClD,UAAU,CAACmD,IAAI,CAAC,CAAC,CAAC;EAC9C,CAAC,EAAE,CAACnD,UAAU,CAAC,CAAC;EAEhB,MAAMoD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,aAAa,GAAG,CAAC,GAAG3D,MAAM,CAAC;MACjC,MAAM4D,QAAQ,GAAG;QACf,GAAGxD,WAAW;QACdyD,MAAM,EAAEjD,IAAI,CAACK,GAAG;QAChB6C,QAAQ,EAAElD,IAAI,CAACmD;MACjB,CAAC;MACDH,QAAQ,CAACnC,IAAI,GAAG,IAAIuC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAExCxB,OAAO,CAACC,GAAG,CAAC3C,SAAS,CAAC;MACtB0C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkB,QAAQ,CAAC;MAEtC,IAAIM,CAAC,GAAG,MAAMjB,UAAU,CAACW,QAAQ,CAAC;MAElCD,aAAa,CAACzD,YAAY,CAAC,GAAGE,WAAW;MACzCH,SAAS,CAAC0D,aAAa,CAAC;MACxBxD,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC,IAAI,CAAC;MACnB,IAAIyD,CAAC,EAAE;QACLpF,MAAM,CAAC,CAAC,EAAE,gCAAgC,EAAE,eAAe,CAAC;MAC9D,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;MACvD;MACAe,YAAY,CAAEiD,IAAI,IAAK,CAACA,IAAI,CAAC;MAC7BhD,UAAU,CAAEgD,IAAI,IAAK,CAACA,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlE,MAAM,CAAC,CAAC,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;IACvD;EACF,CAAC;EAED,MAAMqF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACvBM,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;EACvB,CAAC;EAED,MAAM2D,eAAe,GAAGA,CAACC,KAAK,EAAEtD,KAAK,KAAK;IACxCZ,eAAe,CAACkE,KAAK,CAAC;IACtBhE,cAAc,CAAC;MAAE,GAAGU;IAAM,CAAC,CAAC;IAC5BN,aAAa,CAAC4D,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAER,IAAI;MAAES;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCpE,cAAc,CAAEqE,SAAS,KAAM;MAAE,GAAGA,SAAS;MAAE,CAACX,IAAI,GAAGS;IAAM,CAAC,CAAC,CAAC;EAClE,CAAC;EAED,MAAMG,YAAY,GAAIJ,CAAC,IAAK;IAC1BhE,aAAa,CAACgE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAC;EAC/B,CAAC;EACD,MAAMI,QAAQ,GAAInD,IAAI,IAAK;IACzB,MAAMoD,KAAK,GAAG,IAAIb,IAAI,CAACvC,IAAI,CAAC;IAC5B,IAAIqD,KAAK,CAACD,KAAK,CAAC,EAAE;MAChB,OAAO,cAAc;IACvB;IACA,OAAOA,KAAK,CAACE,cAAc,CAAC,OAAO,EAAE;MACnCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACD,oBACEtG,OAAA;IAAKuG,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCxG,OAAA;MAAKuG,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxG,OAAA;QAAIuG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClD5G,OAAA;QAAKuG,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCxG,OAAA;UACE6G,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,mBAAmB;UAC7BO,WAAW,EAAC,eAAe;UAC3BtB,KAAK,EAAElE,UAAW;UAClByF,QAAQ,EAAEpB;QAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACF5G,OAAA;UAAQuG,SAAS,EAAC,uBAAuB;UAACS,OAAO,EAAEzG,aAAc;UAAAiG,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5G,OAAA;UAAQuG,SAAS,EAAC,wBAAwB;UAACS,OAAO,EAAExG,SAAU;UAAAgG,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5G,OAAA;MAAOuG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBACjCxG,OAAA;QAAAwG,QAAA,eACExG,OAAA;UAAAwG,QAAA,gBACExG,OAAA;YAAAwG,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5G,OAAA;YAAAwG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5G,OAAA;YAAAwG,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACb5G,OAAA;YAAAwG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5G,OAAA;YAAAwG,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB5G,OAAA;YAAAwG,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5G,OAAA;YAAAwG,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5G,OAAA;QAAAwG,QAAA,EACGxF,MAAM,CAAC4C,GAAG,CAAC,CAAC7B,KAAK,EAAEsD,KAAK,kBACvBrF,OAAA,CAACf,KAAK,CAACgB,QAAQ;UAAAuG,QAAA,gBACbxG,OAAA;YAAAwG,QAAA,gBACExG,OAAA;cAAAwG,QAAA,GAAI,GAAC,EAACzE,KAAK,CAACC,EAAE;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpB5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACX9B,IAAI,EAAC,QAAQ;kBACbS,KAAK,EAAEpE,WAAW,CAACe,MAAO;kBAC1B4E,QAAQ,EAAEzB;gBAAiB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACF5G,OAAA;kBACE6G,IAAI,EAAC,OAAO;kBACZ9B,IAAI,EAAC,OAAO;kBACZS,KAAK,EAAEpE,WAAW,CAACiB,KAAM;kBACzB0E,QAAQ,EAAEzB;gBAAiB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN5G,OAAA;gBAAAwG,QAAA,GACGzE,KAAK,CAACI,MAAM,EAAC,GAAC,eAAAnC,OAAA;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrB5G,OAAA;kBAAAwG,QAAA,EAAQzE,KAAK,CAACM;gBAAK;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA;gBACE6G,IAAI,EAAC,MAAM;gBACX9B,IAAI,EAAC,MAAM;gBACXS,KAAK,EAAEpE,WAAW,CAACqB,IAAK;gBACxBsE,QAAQ,EAAEzB;cAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,GAEFhB,QAAQ,CAAC7D,KAAK,CAACU,IAAI;YACpB;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA;gBACE+E,IAAI,EAAC,QAAQ;gBACbS,KAAK,EAAEpE,WAAW,CAACmB,MAAO;gBAC1BwE,QAAQ,EAAEzB,gBAAiB;gBAAAkB,QAAA,gBAE3BxG,OAAA;kBAAQwF,KAAK,EAAC,SAAS;kBAAAgB,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC5G,OAAA;kBAAQwF,KAAK,EAAC,YAAY;kBAAAgB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9C5G,OAAA;kBAAQwF,KAAK,EAAC,UAAU;kBAAAgB,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,gBAET5G,OAAA;gBACEuG,SAAS,EAAE,qBAAqBxE,KAAK,CAACQ,MAAM,CAAC0E,WAAW,CAAC,CAAC,EAAG;gBAAAT,QAAA,EAE5DzE,KAAK,CAACQ;cAAM;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA;gBACE6G,IAAI,EAAC,MAAM;gBACX9B,IAAI,EAAC,SAAS;gBACdS,KAAK,EAAEpE,WAAW,CAACuB,OAAQ;gBAC3BoE,QAAQ,EAAEzB;cAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,GAEF7E,KAAK,CAACY;YACP;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA;gBACE6G,IAAI,EAAC,QAAQ;gBACb9B,IAAI,EAAC,OAAO;gBACZmC,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBACzB3B,KAAK,EAAEpE,WAAW,CAACwB,KAAM;gBACzBmE,QAAQ,EAAEzB,gBAAiB;gBAC3B8B,IAAI,EAAC;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,GAEF,IAAI7E,KAAK,CAACa,KAAK;YAChB;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACL5G,OAAA;cAAAwG,QAAA,EACGtF,YAAY,KAAKmE,KAAK,gBACrBrF,OAAA,CAAAE,SAAA;gBAAAsG,QAAA,gBACExG,OAAA;kBACEuG,SAAS,EAAC,wBAAwB;kBAClCS,OAAO,EAAEtC,eAAgB;kBAAA8B,QAAA,EAC1B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5G,OAAA;kBACEuG,SAAS,EAAC,0BAA0B;kBACpCS,OAAO,EAAE7B,iBAAkB;kBAAAqB,QAAA,EAC5B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CAAC,gBAEH5G,OAAA,CAAAE,SAAA;gBAAAsG,QAAA,eACExG,OAAA;kBACEkH,KAAK,EAAE;oBACLG,OAAO,EAAE,MAAM;oBACfC,aAAa,EAAE,QAAQ;oBACvBC,UAAU,EAAE;kBACd,CAAE;kBAAAf,QAAA,gBAEFxG,OAAA;oBACEuG,SAAS,EAAC,wBAAwB;oBAClCS,OAAO,EAAEA,CAAA,KAAM5B,eAAe,CAACC,KAAK,EAAEtD,KAAK,CAAE;oBAC7CmF,KAAK,EAAE;sBAAEM,MAAM,EAAE;oBAAE,CAAE;oBAAAhB,QAAA,EACtB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA,CAACN,eAAe;oBACd+H,IAAI,EAAE9H,YAAa;oBACnBqH,OAAO,EAAEA,CAAA,KAAM;sBACbpG,OAAO,CAAC,IAAI,CAAC;sBACbH,eAAe,CAAC,CAAC;sBACjBC,UAAU,CAACqB,KAAK,CAACC,EAAE,CAAC;oBACtB,CAAE;oBACFkF,KAAK,EAAE;sBACLQ,MAAM,EAAE,MAAM;sBACdC,KAAK,EAAE,MAAM;sBACbC,OAAO,EAAE;oBACX,CAAE;oBACFrB,SAAS,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC,gBACN;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGJ1F,YAAY,KAAKmE,KAAK,IAAI7D,UAAU,KAAK6D,KAAK,iBAC7CrF,OAAA;YAAAwG,QAAA,eACExG,OAAA;cAAI6H,OAAO,EAAC,GAAG;cAAArB,QAAA,eACbxG,OAAA;gBACEkH,KAAK,EAAE;kBAAEY,OAAO,EAAE,MAAM;kBAAEC,MAAM,EAAE;gBAAO,CAAE;gBAC3ClB,IAAI,EAAC,MAAM;gBACX9B,IAAI,EAAC,OAAO;gBACZS,KAAK,EAAEpE,WAAW,CAAC0B,KAAM;gBACzBiE,QAAQ,EAAEzB,gBAAiB;gBAC3BwB,WAAW,EAAC,qBAAqB;gBACjCP,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACL;QAAA,GAtJkB7E,KAAK,CAACC,EAAE;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuJb,CACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;EAAA,QAtV2BhH,OAAO;AAAA,EAuVrC,CAAC;EAAA,QAvV6BA,OAAO;AAAA,EAuVpC;AAACoI,GAAA,GA1WI5H,eAAe;AA4WrB,eAAeA,eAAe;AAE9B,SAASD,oBAAoB;AAAG,IAAAG,EAAA,EAAA0H,GAAA;AAAAC,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}