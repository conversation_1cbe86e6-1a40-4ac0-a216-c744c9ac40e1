{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\Header\\\\noti.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport { useLoading } from \"../introduce/Loading\";\nimport { FaRegBell } from \"react-icons/fa\"; // Import đúng biểu tượng\nimport \"./noti.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Notification = () => {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [products, setProducts] = useState([]);\n  const [isVisible, setIsVisible] = useState(false);\n  const [isFetching, setIsFetching] = useState(false);\n  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(false);\n\n  // Hàm để lấy sản phẩm\n  const fetchProducts = async () => {\n    if (loading || !user) return;\n    setIsFetching(true);\n    startLoading();\n    try {\n      const response = await fetch(\"http://localhost:8080/api/products/show\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          user\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\"Network response was not ok\");\n      }\n      const data = await response.json();\n      const filteredProducts = data.filter(product => product.stock_in_Warehouse < product.reorderLevel);\n      setProducts(filteredProducts);\n      setHasUnreadNotifications(filteredProducts.length > 0);\n    } catch (error) {\n      console.error(\"Lỗi khi gọi API:\", error);\n    } finally {\n      stopLoading();\n      setIsFetching(false);\n    }\n  };\n\n  // Cập nhật sản phẩm mỗi khi có thay đổi hoặc theo chu kỳ\n  useEffect(() => {\n    if (user && !loading) {\n      fetchProducts(); // Lần đầu tiên gọi hàm để lấy dữ liệu sản phẩm\n      const interval = setInterval(fetchProducts, 6000000); // Cập nhật mỗi phút\n\n      // Cleanup interval khi component bị unmount\n      return () => clearInterval(interval);\n    }\n  }, [user, loading]);\n\n  // Hiển thị thông báo khi click vào chuông\n  const handleBellClick = () => {\n    if (isFetching) return; // Tránh gọi API nếu đang tải dữ liệu\n    setIsVisible(prev => !prev);\n    if (!isVisible) {\n      fetchProducts();\n      setHasUnreadNotifications(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"notification-container\",\n    children: [/*#__PURE__*/_jsxDEV(FaRegBell, {\n      className: \"notification-bell\",\n      onClick: handleBellClick,\n      style: {\n        cursor: \"pointer\",\n        color: hasUnreadNotifications ? \"black\" : \"gray\",\n        fontSize: \"24px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), hasUnreadNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notification-badge\",\n      children: products.length\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `notification-popup ${isVisible ? \"show\" : \"\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notification-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"B\\u1EA1n c\\xF3 tin nh\\u1EAFn m\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notification-content\",\n        children: isFetching ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0110ang t\\u1EA3i...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this) : products.length > 0 ? products.map((product, index) => {\n          var _product$image;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: ((_product$image = product.image) === null || _product$image === void 0 ? void 0 : _product$image.secure_url) || \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n              alt: \"Product\",\n              style: {\n                width: \"30px\",\n                height: \"30px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notification-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"notification-name\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"notification-stock\",\n                children: `Số lượng trong kho: ${product.stock_in_Warehouse}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Kh\\xF4ng c\\xF3 th\\xF4ng b\\xE1o n\\xE0o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(Notification, \"mXKKefV/c6DP5DYZFpv3/P6Ru/0=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = Notification;\nexport default Notification;\nvar _c;\n$RefreshReg$(_c, \"Notification\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useLoading", "FaRegBell", "jsxDEV", "_jsxDEV", "Notification", "_s", "startLoading", "stopLoading", "user", "loading", "products", "setProducts", "isVisible", "setIsVisible", "isFetching", "setIsFetching", "hasUnreadNotifications", "setHasUnreadNotifications", "fetchProducts", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "data", "json", "filteredProducts", "filter", "product", "stock_in_Warehouse", "reorderLevel", "length", "error", "console", "interval", "setInterval", "clearInterval", "handleBellClick", "prev", "className", "children", "onClick", "style", "cursor", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "_product$image", "src", "image", "secure_url", "alt", "width", "height", "name", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/Header/noti.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport { FaRegBell } from \"react-icons/fa\"; // Import đúng biểu tượng\r\nimport \"./noti.css\";\r\n\r\nconst Notification = () => {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, loading } = useAuth();\r\n  const [products, setProducts] = useState([]);\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [isFetching, setIsFetching] = useState(false);\r\n  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(false);\r\n\r\n  // Hàm để lấy sản phẩm\r\n  const fetchProducts = async () => {\r\n    if (loading || !user) return;\r\n\r\n    setIsFetching(true);\r\n    startLoading();\r\n\r\n    try {\r\n      const response = await fetch(\"http://localhost:8080/api/products/show\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ user }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Network response was not ok\");\r\n      }\r\n\r\n      const data = await response.json();\r\n      const filteredProducts = data.filter(\r\n        (product) => product.stock_in_Warehouse < product.reorderLevel\r\n      );\r\n\r\n      setProducts(filteredProducts);\r\n      setHasUnreadNotifications(filteredProducts.length > 0);\r\n    } catch (error) {\r\n      console.error(\"Lỗi khi gọi API:\", error);\r\n    } finally {\r\n      stopLoading();\r\n      setIsFetching(false);\r\n    }\r\n  };\r\n\r\n  // Cập nhật sản phẩm mỗi khi có thay đổi hoặc theo chu kỳ\r\n  useEffect(() => {\r\n    if (user && !loading) {\r\n      fetchProducts(); // Lần đầu tiên gọi hàm để lấy dữ liệu sản phẩm\r\n      const interval = setInterval(fetchProducts, 6000000); // Cập nhật mỗi phút\r\n\r\n      // Cleanup interval khi component bị unmount\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [user, loading]);\r\n\r\n  // Hiển thị thông báo khi click vào chuông\r\n  const handleBellClick = () => {\r\n    if (isFetching) return; // Tránh gọi API nếu đang tải dữ liệu\r\n    setIsVisible((prev) => !prev);\r\n    if (!isVisible) {\r\n      fetchProducts();\r\n      setHasUnreadNotifications(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"notification-container\">\r\n      <FaRegBell\r\n        className=\"notification-bell\"\r\n        onClick={handleBellClick}\r\n        style={{\r\n          cursor: \"pointer\",\r\n          color: hasUnreadNotifications ? \"black\" : \"gray\",\r\n          fontSize: \"24px\",\r\n        }}\r\n      />\r\n\r\n      {/* Hiển thị hình tròn đỏ nếu có thông báo */}\r\n      {hasUnreadNotifications && (\r\n        <div className=\"notification-badge\">{products.length}</div>\r\n      )}\r\n\r\n      {/* Hiển thị popup thông báo */}\r\n      <div className={`notification-popup ${isVisible ? \"show\" : \"\"}`}>\r\n        <div className=\"notification-header\">\r\n          <h4>Bạn có tin nhắn mới</h4>\r\n        </div>\r\n        <div className=\"notification-content\">\r\n          {isFetching ? (\r\n            <p>Đang tải...</p>\r\n          ) : products.length > 0 ? (\r\n            products.map((product, index) => (\r\n              <div className=\"notification-item\" key={index}>\r\n                <img\r\n                  src={\r\n                    product.image?.secure_url ||\r\n                    \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n                  }\r\n                  alt=\"Product\"\r\n                  style={{ width: \"30px\", height: \"30px\" }}\r\n                />\r\n                <div className=\"notification-details\">\r\n                  <p className=\"notification-name\">{product.name}</p>\r\n                  <p className=\"notification-stock\">\r\n                    {`Số lượng trong kho: ${product.stock_in_Warehouse}`}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <p>Không có thông báo nào</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Notification;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,SAAS,QAAQ,gBAAgB,CAAC,CAAC;AAC5C,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGP,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEQ,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAMqB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAIT,OAAO,IAAI,CAACD,IAAI,EAAE;IAEtBO,aAAa,CAAC,IAAI,CAAC;IACnBT,YAAY,CAAC,CAAC;IAEd,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,EAAE;QACtEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEjB;QAAK,CAAC;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACW,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClC,MAAMC,gBAAgB,GAAGF,IAAI,CAACG,MAAM,CACjCC,OAAO,IAAKA,OAAO,CAACC,kBAAkB,GAAGD,OAAO,CAACE,YACpD,CAAC;MAEDvB,WAAW,CAACmB,gBAAgB,CAAC;MAC7Bb,yBAAyB,CAACa,gBAAgB,CAACK,MAAM,GAAG,CAAC,CAAC;IACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C,CAAC,SAAS;MACR7B,WAAW,CAAC,CAAC;MACbQ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,IAAI,CAACC,OAAO,EAAE;MACpBS,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB,MAAMoB,QAAQ,GAAGC,WAAW,CAACrB,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;;MAEtD;MACA,OAAO,MAAMsB,aAAa,CAACF,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAAC9B,IAAI,EAAEC,OAAO,CAAC,CAAC;;EAEnB;EACA,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI3B,UAAU,EAAE,OAAO,CAAC;IACxBD,YAAY,CAAE6B,IAAI,IAAK,CAACA,IAAI,CAAC;IAC7B,IAAI,CAAC9B,SAAS,EAAE;MACdM,aAAa,CAAC,CAAC;MACfD,yBAAyB,CAAC,KAAK,CAAC;IAClC;EACF,CAAC;EAED,oBACEd,OAAA;IAAKwC,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCzC,OAAA,CAACF,SAAS;MACR0C,SAAS,EAAC,mBAAmB;MAC7BE,OAAO,EAAEJ,eAAgB;MACzBK,KAAK,EAAE;QACLC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAEhC,sBAAsB,GAAG,OAAO,GAAG,MAAM;QAChDiC,QAAQ,EAAE;MACZ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGDrC,sBAAsB,iBACrBb,OAAA;MAAKwC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAElC,QAAQ,CAACyB;IAAM;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC3D,eAGDlD,OAAA;MAAKwC,SAAS,EAAE,sBAAsB/B,SAAS,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAgC,QAAA,gBAC9DzC,OAAA;QAAKwC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCzC,OAAA;UAAAyC,QAAA,EAAI;QAAmB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNlD,OAAA;QAAKwC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClC9B,UAAU,gBACTX,OAAA;UAAAyC,QAAA,EAAG;QAAW;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GAChB3C,QAAQ,CAACyB,MAAM,GAAG,CAAC,GACrBzB,QAAQ,CAAC4C,GAAG,CAAC,CAACtB,OAAO,EAAEuB,KAAK;UAAA,IAAAC,cAAA;UAAA,oBAC1BrD,OAAA;YAAKwC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzC,OAAA;cACEsD,GAAG,EACD,EAAAD,cAAA,GAAAxB,OAAO,CAAC0B,KAAK,cAAAF,cAAA,uBAAbA,cAAA,CAAeG,UAAU,KACzB,+KACD;cACDC,GAAG,EAAC,SAAS;cACbd,KAAK,EAAE;gBAAEe,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACFlD,OAAA;cAAKwC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCzC,OAAA;gBAAGwC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEZ,OAAO,CAAC+B;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDlD,OAAA;gBAAGwC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAC9B,uBAAuBZ,OAAO,CAACC,kBAAkB;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAdgCE,KAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAexC,CAAC;QAAA,CACP,CAAC,gBAEFlD,OAAA;UAAAyC,QAAA,EAAG;QAAsB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAC7B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAjHID,YAAY;EAAA,QACsBJ,UAAU,EACtBD,OAAO;AAAA;AAAAiE,EAAA,GAF7B5D,YAAY;AAmHlB,eAAeA,YAAY;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}