{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\sale_daily.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Button, Menu, MenuItem } from \"@mui/material\";\nimport { Line } from \"react-chartjs-2\";\nimport \"chart.js/auto\"; // Tự động đăng ký các thành phần biểu đồ\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Sales_daily() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const open = Boolean(anchorEl);\n  const [dt, Setdt] = useState({\n    date: [0, 0, 0, 0, 0, 0, 0, 0],\n    report: [0, 0, 0, 0, 0, 0, 0, 0]\n  });\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const data = {\n    labels: dt.date,\n    datasets: [{\n      data: dt.report,\n      borderColor: \"#fff\",\n      backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n      fill: true,\n      tension: 0.4\n    }]\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      if (loading) return;\n      try {\n        const response = await fetch(\"http://localhost:8080/api/home/<USER>\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            user: user\n          })\n        });\n        if (!response.ok) {\n          throw new Error(\"Network response was not ok\");\n        }\n        const data = await response.json();\n        console.log(\"generatedailySale:\", data);\n        Setdt(data);\n      } catch (error) {\n        console.error(\"Error fetching revenue:\", error);\n      }\n    };\n    fetchData();\n  }, [loading]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: \"#1e88e5\",\n      color: \"#fff\",\n      borderRadius: 2,\n      padding: 3,\n      textAlign: \"center\",\n      width: \"100%\",\n      // Cho phép mở rộng toàn bộ chiều rộng của phần tử cha\n      position: \"relative\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      children: \"Daily Sales\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      children: `${dt.date[0]}   ---   ${dt.date[7]}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        fontWeight: \"bold\",\n        marginTop: 1\n      },\n      children: `${Math.max(...dt.report).toLocaleString(\"vn-Vi\")} đ`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleClick,\n      sx: {\n        position: \"absolute\",\n        top: 16,\n        right: 16,\n        backgroundColor: \"#1976d2\",\n        color: \"#fff\",\n        padding: \"4px 8px\",\n        minWidth: \"auto\",\n        fontSize: \"0.75rem\",\n        \"&:hover\": {\n          backgroundColor: \"#1565c0\"\n        }\n      },\n      children: \"Export\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: open,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"left\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleClose,\n        children: \"Export as CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleClose,\n        children: \"Export as PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginTop: 3,\n        width: \"100%\"\n      },\n      children: [\" \", /*#__PURE__*/_jsxDEV(Line, {\n        data: data,\n        options: {\n          plugins: {\n            legend: {\n              display: false\n            }\n          },\n          scales: {\n            x: {\n              display: false\n            },\n            y: {\n              display: false\n            }\n          },\n          responsive: true,\n          maintainAspectRatio: false\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(Sales_daily, \"tvv6piSs/b9lqskQ+pGhOEzYn2I=\", false, function () {\n  return [useAuth];\n});\n_c = Sales_daily;\nexport default Sales_daily;\nvar _c;\n$RefreshReg$(_c, \"Sales_daily\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Line", "useAuth", "jsxDEV", "_jsxDEV", "Sales_daily", "_s", "user", "loading", "anchorEl", "setAnchorEl", "open", "Boolean", "dt", "<PERSON>dt", "date", "report", "handleClick", "event", "currentTarget", "handleClose", "data", "labels", "datasets", "borderColor", "backgroundColor", "fill", "tension", "fetchData", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "console", "log", "error", "sx", "color", "borderRadius", "padding", "textAlign", "width", "position", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "marginTop", "Math", "max", "toLocaleString", "onClick", "top", "right", "min<PERSON><PERSON><PERSON>", "fontSize", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "options", "plugins", "legend", "display", "scales", "x", "y", "responsive", "maintainAspectRatio", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, Button, Menu, MenuItem } from \"@mui/material\";\r\nimport { Line } from \"react-chartjs-2\";\r\nimport \"chart.js/auto\"; // Tự động đăng ký các thành phần biểu đồ\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nfunction Sales_daily() {\r\n  const { user, loading } = useAuth();\r\n  const [anchorEl, setAnchorEl] = React.useState(null);\r\n  const open = Boolean(anchorEl);\r\n  const [dt, Setdt] = useState({\r\n    date: [0, 0, 0, 0, 0, 0, 0, 0],\r\n    report: [0, 0, 0, 0, 0, 0, 0, 0],\r\n  });\r\n  const handleClick = (event) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const data = {\r\n    labels: dt.date,\r\n    datasets: [\r\n      {\r\n        data: dt.report,\r\n        borderColor: \"#fff\",\r\n        backgroundColor: \"rgba(255, 255, 255, 0.2)\",\r\n        fill: true,\r\n        tension: 0.4,\r\n      },\r\n    ],\r\n  };\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (loading) return;\r\n      try {\r\n        const response = await fetch(\r\n          \"http://localhost:8080/api/home/<USER>\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({\r\n              user: user,\r\n            }),\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Network response was not ok\");\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log(\"generatedailySale:\", data);\r\n        Setdt(data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching revenue:\", error);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [loading]);\r\n  return (\r\n    <Box\r\n      sx={{\r\n        backgroundColor: \"#1e88e5\",\r\n        color: \"#fff\",\r\n        borderRadius: 2,\r\n        padding: 3,\r\n        textAlign: \"center\",\r\n        width: \"100%\", // Cho phép mở rộng toàn bộ chiều rộng của phần tử cha\r\n        position: \"relative\",\r\n      }}\r\n    >\r\n      <Typography variant=\"h6\">Daily Sales</Typography>\r\n      <Typography variant=\"body2\">{`${dt.date[0]}   ---   ${dt.date[7]}`}</Typography>\r\n      <Typography\r\n        variant=\"h4\"\r\n        sx={{ fontWeight: \"bold\", marginTop: 1 }}\r\n      >{`${Math.max(...dt.report).toLocaleString(\"vn-Vi\")} đ`}</Typography>\r\n\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleClick}\r\n        sx={{\r\n          position: \"absolute\",\r\n          top: 16,\r\n          right: 16,\r\n          backgroundColor: \"#1976d2\",\r\n          color: \"#fff\",\r\n          padding: \"4px 8px\",\r\n          minWidth: \"auto\",\r\n          fontSize: \"0.75rem\",\r\n          \"&:hover\": { backgroundColor: \"#1565c0\" },\r\n        }}\r\n      >\r\n        Export\r\n      </Button>\r\n      <Menu\r\n        anchorEl={anchorEl}\r\n        open={open}\r\n        onClose={handleClose}\r\n        anchorOrigin={{ vertical: \"bottom\", horizontal: \"left\" }}\r\n      >\r\n        <MenuItem onClick={handleClose}>Export as CSV</MenuItem>\r\n        <MenuItem onClick={handleClose}>Export as PDF</MenuItem>\r\n      </Menu>\r\n\r\n      <Box sx={{ marginTop: 3, width: \"100%\" }}>\r\n        {\" \"}\r\n        {/* Đặt width=100% cho biểu đồ */}\r\n        <Line\r\n          data={data}\r\n          options={{\r\n            plugins: { legend: { display: false } },\r\n            scales: { x: { display: false }, y: { display: false } },\r\n            responsive: true,\r\n            maintainAspectRatio: false,\r\n          }}\r\n        />\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Sales_daily;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AACvE,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,eAAe,CAAC,CAAC;AACxB,SAASC,OAAO,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC7D,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGN,OAAO,CAAC,CAAC;EACnC,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,KAAK,CAACE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMgB,IAAI,GAAGC,OAAO,CAACH,QAAQ,CAAC;EAC9B,MAAM,CAACI,EAAE,EAAEC,KAAK,CAAC,GAAGnB,QAAQ,CAAC;IAC3BoB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9BC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EACjC,CAAC,CAAC;EACF,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7BR,WAAW,CAACQ,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBV,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMW,IAAI,GAAG;IACXC,MAAM,EAAET,EAAE,CAACE,IAAI;IACfQ,QAAQ,EAAE,CACR;MACEF,IAAI,EAAER,EAAE,CAACG,MAAM;MACfQ,WAAW,EAAE,MAAM;MACnBC,eAAe,EAAE,0BAA0B;MAC3CC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EACDjC,SAAS,CAAC,MAAM;IACd,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIpB,OAAO,EAAE;MACb,IAAI;QACF,MAAMqB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kDAAkD,EAClD;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB5B,IAAI,EAAEA;UACR,CAAC;QACH,CACF,CAAC;QAED,IAAI,CAACsB,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QAEA,MAAMhB,IAAI,GAAG,MAAMQ,QAAQ,CAACS,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEnB,IAAI,CAAC;QACvCP,KAAK,CAACO,IAAI,CAAC;MACb,CAAC,CAAC,OAAOoB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF,CAAC;IAEDb,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACpB,OAAO,CAAC,CAAC;EACb,oBACEJ,OAAA,CAACR,GAAG;IACF8C,EAAE,EAAE;MACFjB,eAAe,EAAE,SAAS;MAC1BkB,KAAK,EAAE,MAAM;MACbC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,MAAM;MAAE;MACfC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEF7C,OAAA,CAACP,UAAU;MAACqD,OAAO,EAAC,IAAI;MAAAD,QAAA,EAAC;IAAW;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACjDlD,OAAA,CAACP,UAAU;MAACqD,OAAO,EAAC,OAAO;MAAAD,QAAA,EAAE,GAAGpC,EAAE,CAACE,IAAI,CAAC,CAAC,CAAC,YAAYF,EAAE,CAACE,IAAI,CAAC,CAAC,CAAC;IAAE;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAChFlD,OAAA,CAACP,UAAU;MACTqD,OAAO,EAAC,IAAI;MACZR,EAAE,EAAE;QAAEa,UAAU,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAP,QAAA,EACzC,GAAGQ,IAAI,CAACC,GAAG,CAAC,GAAG7C,EAAE,CAACG,MAAM,CAAC,CAAC2C,cAAc,CAAC,OAAO,CAAC;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAErElD,OAAA,CAACN,MAAM;MACLoD,OAAO,EAAC,WAAW;MACnBU,OAAO,EAAE3C,WAAY;MACrByB,EAAE,EAAE;QACFM,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTrC,eAAe,EAAE,SAAS;QAC1BkB,KAAK,EAAE,MAAM;QACbE,OAAO,EAAE,SAAS;QAClBkB,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,SAAS;QACnB,SAAS,EAAE;UAAEvC,eAAe,EAAE;QAAU;MAC1C,CAAE;MAAAwB,QAAA,EACH;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTlD,OAAA,CAACL,IAAI;MACHU,QAAQ,EAAEA,QAAS;MACnBE,IAAI,EAAEA,IAAK;MACXsD,OAAO,EAAE7C,WAAY;MACrB8C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAnB,QAAA,gBAEzD7C,OAAA,CAACJ,QAAQ;QAAC4D,OAAO,EAAExC,WAAY;QAAA6B,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACxDlD,OAAA,CAACJ,QAAQ;QAAC4D,OAAO,EAAExC,WAAY;QAAA6B,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAEPlD,OAAA,CAACR,GAAG;MAAC8C,EAAE,EAAE;QAAEc,SAAS,EAAE,CAAC;QAAET,KAAK,EAAE;MAAO,CAAE;MAAAE,QAAA,GACtC,GAAG,eAEJ7C,OAAA,CAACH,IAAI;QACHoB,IAAI,EAAEA,IAAK;QACXgD,OAAO,EAAE;UACPC,OAAO,EAAE;YAAEC,MAAM,EAAE;cAAEC,OAAO,EAAE;YAAM;UAAE,CAAC;UACvCC,MAAM,EAAE;YAAEC,CAAC,EAAE;cAAEF,OAAO,EAAE;YAAM,CAAC;YAAEG,CAAC,EAAE;cAAEH,OAAO,EAAE;YAAM;UAAE,CAAC;UACxDI,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE;QACvB;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChD,EAAA,CAxHQD,WAAW;EAAA,QACQH,OAAO;AAAA;AAAA4E,EAAA,GAD1BzE,WAAW;AA0HpB,eAAeA,WAAW;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}