{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\Import\\\\index2.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// import ImageUpload from \"../../components/Manage_product/image\"\n// import Change_password from\"../../components/introduce/resetpassword.js\"\nimport OrderManagement from \"../../components/test/index\";\nimport ModalHistory from \"./ModalHistory\";\nimport React, { useState, useRef, useEffect, useCallback, useContext } from \"react\";\nimport axios from \"axios\";\nimport debounce from \"lodash.debounce\";\nimport Modal from \"./../../components/ComponentExport/Modal\";\nimport \"./import.css\";\nimport ModalDetail from \"./ModalDetail\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Import2() {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [openHistory, setOpenHistory] = useState(false);\n  const [openDetail, setOpenDetail] = useState(false);\n  const [suggestions, setSuggestions] = useState([]);\n  const [suppOrPro, setSuppOrPro] = useState(false);\n  const [idProductAdded, setIdProductAdded] = useState([]);\n  const [idOrder, setIdOrder] = useState(null);\n  const [dataTop, setDataTop] = useState([]);\n  const {\n    user,\n    loading\n  } = useAuth();\n  const apiGetOrder = useRef();\n  const apiGetHistory = useRef();\n  const [view, setView] = useState(true);\n  const [loadLog, setLoadLog] = useState(false);\n  const [loadOrder, setLoadOrder] = useState(false);\n  // const id_owner = user.id_owner;\n  const openModal = () => setIsOpen(true);\n  const closeModal = () => setIsOpen(false);\n  const openModalHistory = () => setOpenHistory(true);\n  const closeModalHistory = () => setOpenHistory(false);\n  const closeModalDetail = () => setOpenDetail(false);\n  const openModalDetail = () => setOpenDetail(true);\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        if (loading) return;\n        const res = await fetch(`http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`);\n        const dataRes = await res.json();\n        setDataTop(dataRes);\n      } catch (err) {\n        console.error(err);\n      }\n    };\n    fetchData();\n  }, [loading]);\n  const handleSearch = event => {\n    const term = event.target.value;\n    let keyword = term.trim();\n    setSearchTerm(term);\n    if (keyword.startsWith(\"@All\")) {\n      keyword = keyword.substr(4).trim();\n      setSuppOrPro(false);\n      if (keyword.length > 0) {\n        debouncedFetchSuggestions(keyword, `http://localhost:8080/api/import/supplier/search`);\n      } else {\n        setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\n      }\n    } else {\n      setSuppOrPro(true);\n      if (keyword.length > 0) {\n        const topData = dataTop.filter(item => item.name.toLowerCase().includes(keyword.toLowerCase())).slice(0, 5);\n        if (topData.length) {\n          setResults(topData.map(item => item.name));\n          setSuggestions(topData);\n        } else {\n          console.log(\"jellooo\");\n          debouncedFetchSuggestions(keyword, `http://localhost:8080/api/import/products/exhibitProN`);\n        }\n      } else {\n        setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\n      }\n    }\n  };\n  // database\n  const fetchProductSuggestions = async (keyword, hrefLink) => {\n    try {\n      const response = await axios.get(hrefLink, {\n        params: {\n          query: keyword,\n          ownerId: user.id_owner\n        }\n      });\n      const sugg = response.data.map(s => s.name);\n      setResults(sugg);\n      setDataTop(prev => {\n        const newData = [...prev, ...response.data];\n        return newData;\n      });\n      setSuggestions(response.data);\n    } catch (error) {\n      console.error(\"Error fetching suggestions:\", error);\n    }\n  };\n  const debouncedFetchSuggestions = useCallback(debounce((keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink), 500), [user] // Chỉ tạo ra một lần\n  );\n  const handleAddToOrder = async () => {\n    const idPro = suggestions.filter(sugg => sugg.name == searchTerm);\n    setSuggestions([]);\n    const suppliersId = idPro ? idPro[0] : null;\n    try {\n      // Gửi request GET với query string chứa productId\n      if (suppliersId) {\n        let response;\n        if (!suppOrPro) {\n          response = await fetch(`http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            }\n          });\n        }\n\n        // Kiểm tra nếu request thành công\n        if (!suppOrPro && response.ok) {\n          const data = await response.json(); // Dữ liệu trả về từ server (có thể là chi tiết sản phẩm)\n          setIdProductAdded(data);\n          // Xử lý dữ liệu từ server (Hiển thị thông tin đơn hàng, ví dụ...)\n          setSearchTerm(\"\");\n          setResults([]);\n        } else if (suppOrPro) {\n          setIdProductAdded(idPro);\n          setSearchTerm(\"\");\n          setResults([]);\n        } else {\n          console.error(\"Error adding to order\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Request failed\", error);\n    }\n  };\n  const handleBlur = () => {\n    setTimeout(() => {\n      setShowDropdown(false);\n    }, 700);\n  };\n  const handleSelectLiResult = result => {\n    setSearchTerm(result); // Cập nhật giá trị input với kết quả đã chọn\n    setShowDropdown(false); // Ẩn dropdown sau khi chọn\n  };\n  //  console.log(apiGetHistory.current)\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(OrderManagement, {\n      onCreateOrder: openModal,\n      onHistory: openModalHistory,\n      openModalDetail: openModalDetail,\n      setIdOrder: setIdOrder,\n      refOrder: apiGetOrder,\n      setView: setView,\n      loadOrder: loadOrder,\n      setLoadLog: setLoadLog,\n      setLoadOrder: setLoadOrder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: isOpen,\n      onClose: closeModal,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"Modal-title\",\n        children: \"Create your order opening\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-order\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              flex: 1,\n              marginLeft: 10\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"block\",\n                paddingTop: \"10px\"\n              },\n              children: [\"T\\xECm ki\\u1EBFm:\", \" \"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-result-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                style: {\n                  flex: 1\n                },\n                className: \"order-mgmt-search\",\n                placeholder: \"Search by code or product name\",\n                value: searchTerm,\n                onChange: handleSearch,\n                onBlur: handleBlur // Thêm onBlur để ẩn dropdown\n                ,\n                onFocus: () => setShowDropdown(true) // Hiển thị dropdown khi focus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), showDropdown && results.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"dropdown\",\n                children: results.map((result, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"search-item\",\n                  onClick: () => handleSelectLiResult(result),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"search-container-item\",\n                    children: [result, suppOrPro && suggestions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"search-container-img\",\n                      style: {\n                        backgroundImage: `url(${suggestions[index].image.secure_url})`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"divide\",\n                    style: {\n                      margin: \"8px 2px 0\",\n                      background: \"white\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-add-order\",\n          onClick: handleAddToOrder,\n          children: \"Add to order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"body-modal\",\n        children: /*#__PURE__*/_jsxDEV(ContentOrder, {\n          dataHis: idProductAdded,\n          setIdProductAdded: setIdProductAdded,\n          apiFetchOrderHistory: apiGetOrder,\n          apiGetHistory: apiGetHistory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalHistory, {\n      isOpen: openHistory,\n      onClose: closeModalHistory,\n      openModalDetail: openModalDetail,\n      setIdOrder: setIdOrder,\n      apiGetHistory: apiGetHistory,\n      setView: setView,\n      loadLog: loadLog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalDetail, {\n      isOpen: openDetail,\n      onClose: closeModalDetail,\n      idOrder: idOrder,\n      view: view,\n      setLoadLog: setLoadLog,\n      setLoadOrder: setLoadOrder,\n      children: \" \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true)\n  // <div style={{ textAlign: 'center', margin: '20px' }}>\n  //   <input\n  //     type=\"file\"\n  //     accept=\"image/*\"\n  //     onChange={handleImageChange}\n  //   />\n  //   {selectedImage && (\n  //     <div style={{ marginTop: '20px' }}>\n  //       <h3>Ảnh đã tải lên:</h3>\n  //       <img\n  //         src={selectedImage}\n  //         alt=\"Uploaded\"\n  //         style={{ maxWidth: '300px', maxHeight: '300px' }}\n  //       />\n  //     </div>\n  //   )}\n  // </div>\n  ;\n}\n_s(Import2, \"tdsIUk2IzEuwOANMAA1w7ZhqJJQ=\", false, function () {\n  return [useAuth, useLoading];\n});\n_c = Import2;\nconst ContentOrder = ({\n  dataHis,\n  setIdProductAdded,\n  apiFetchOrderHistory,\n  apiGetHistory\n}) => {\n  _s2();\n  const initItem = item => {\n    return {\n      name: item.name,\n      description: item.description,\n      supplier: item.supplierDetails.name,\n      price: item.purchasePrice.replace(/\\./g, \"\"),\n      imageUrl: item.image.secure_url,\n      supplierId: item.supplierDetails._id,\n      quantity: 1,\n      status: \"pending\",\n      email: true,\n      isChecked: true,\n      emailName: item.supplierDetails.email,\n      productId: item._id\n    };\n  };\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [listProductWereAdded, setListProductWereAdded] = useState([]);\n  const listItem = dataHis.map(item => initItem(item));\n  const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\n  const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(Array(listProductWereAdded.length).fill(false));\n  const [selectedSupplier, setSelectedSupplier] = useState(Array(listProductWereAdded.length).fill(\"\"));\n  const [quantities, setQuantities] = useState(listProductWereAdded.map(product => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\n  );\n  const [isOpen, setIsOpen] = useState(new Array(listProductWereAdded.length).fill(false)); // Khởi tạo mảng isOpen\n  const [myTax, setMyTax] = useState(10);\n  useEffect(() => {\n    if (dataHis && dataHis.length > 0) {\n      const newItems = dataHis.map(initItem);\n      console.log(dataHis, listProductWereAdded);\n      if (!listProductWereAdded.some(item => dataHis.some(it => it._id === item.productId))) {\n        setListProductWereAdded(prevList => [...newItems, ...prevList]);\n      }\n      setIdProductAdded([]);\n    }\n  }, [dataHis]);\n  const handleSupplierChange = (supplier, index) => {\n    setListProductWereAdded(prev => {\n      const newList = [...prev];\n      newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\n      return newList;\n    });\n\n    // Cập nhật selectedSupplier\n    setSelectedSupplier(prev => {\n      const newSelectedSuppliers = [...prev];\n      newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\n      return newSelectedSuppliers;\n    });\n\n    // Ẩn dropdown sau khi chọn\n    setIsDropdownOpenSupplier(prev => {\n      const newDropdownState = [...prev];\n      newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\n      return newDropdownState;\n    });\n  };\n  const handleSupplierClick = index => {\n    setIsDropdownOpenSupplier(prev => {\n      const newDropdownState = [...prev];\n      newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\n      return newDropdownState;\n    });\n  };\n  const amountBill = () => {\n    let sum = 0;\n    listProductWereAdded.forEach(product => {\n      sum += product.price.replace(/\\./g, \"\") * product.quantity;\n    });\n    return sum;\n  };\n  const toggleDropdown = index => {\n    setIsOpen(prev => {\n      const newOpen = [...prev];\n      newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\n      return newOpen;\n    });\n  };\n  const dropdownRef = useRef(null);\n  const dropdownRefSupplier = useRef(null);\n  const handleStatusClick = index => {\n    setDropdownOpenIndex(prev => prev === index ? null : index);\n  };\n  const handleStatusChange = (index, newStatus) => {\n    setListProductWereAdded(prev => {\n      const updatedProducts = [...prev];\n      updatedProducts[index].status = newStatus;\n      setDropdownOpenIndex(null);\n      return updatedProducts;\n    });\n    // Ẩn dropdown sau khi chọn\n  };\n  const handleClickOutside = event => {\n    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n      setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\n    }\n  };\n  useEffect(() => {\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  const increase = index => {\n    setListProductWereAdded(prev => {\n      const newQuantities = [...prev];\n      newQuantities[index].quantity += 1; // Tăng giá trị\n      return newQuantities;\n    });\n  };\n  const decrease = index => {\n    setListProductWereAdded(prev => {\n      const newQuantities = [...prev];\n      if (newQuantities[index].quantity > 0) {\n        newQuantities[index].quantity -= 1; // Tăng giá trị\n      }\n      return newQuantities;\n    });\n  };\n  const handleRemove = index => {\n    setListProductWereAdded(prev => {\n      const newList = [...prev];\n      newList.splice(index, 1); // Xoá phần tử\n      return newList;\n    });\n    setIsOpen(prev => {\n      const newOpen = [...prev];\n      newOpen.splice(index, 1); // Cập nhật mảng isOpen\n      return newOpen;\n    });\n  };\n  const handleInputQuantitty = (index, e) => {\n    const newQuantity = e.target.value; // Lấy giá trị mới từ input\n    setListProductWereAdded(prev => {\n      // Tạo bản sao của danh sách hiện tại\n      const updatedList = [...prev];\n      // Cập nhật số lượng sản phẩm tại chỉ số index\n      updatedList[index] = {\n        ...updatedList[index],\n        quantity: newQuantity\n      };\n      return updatedList; // Trả về danh sách đã cập nhật\n    });\n  };\n  const handleCheckboxChange = index => {\n    setListProductWereAdded(prev => {\n      const updatedProducts = [...listProductWereAdded];\n      updatedProducts[index].email = !updatedProducts[index].email;\n      return updatedProducts;\n    });\n  };\n  const handleSubmit = async () => {\n    const groupBySupplier = listProductWereAdded.reduce((acc, item) => {\n      // Kiểm tra xem đã có supplier này trong nhóm chưa\n      if (!acc.dataForm[item.supplier]) {\n        acc.dataForm[item.supplier] = [];\n      }\n      acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\n      return acc;\n    }, {\n      user: {},\n      dataForm: {}\n    });\n    groupBySupplier.user = {\n      id: user._id,\n      name: user.name,\n      email: user.email,\n      ownerId: user.id_owner,\n      id_owner: user.id_owner,\n      role: user.role\n    };\n    groupBySupplier.tax = myTax;\n    const url = \"http://localhost:8080/api/import/orderHistory/save\";\n    try {\n      const response = await fetch(url, {\n        method: \"POST\",\n        // Phương thức POST\n        headers: {\n          \"Content-Type\": \"application/json\" // Xác định kiểu dữ liệu là JSON\n        },\n        body: JSON.stringify(groupBySupplier) // Chuyển đổi dữ liệu thành chuỗi JSON\n      });\n      if (response.ok) {\n        // Nếu thành công, xử lý kết quả\n        notify(1, \"you've completed importing goods\", \"Successfully!\");\n        const responseData = await response.json();\n        console.log(\"Dữ liệu đã được gửi thành công\", responseData);\n        await apiFetchOrderHistory.current.fetchOrder(\"\");\n        await apiGetHistory.current.debouncedFetchSuggestions(\" \", \"http://localhost:8080/api/import/loggingOrder/listOrder\", 1, 10);\n        setIdProductAdded([]);\n        setListProductWereAdded([]);\n      } else {\n        notify(2, \"you don't have the role to do this\", \"Fail!\");\n        // Nếu có lỗi từ server\n        console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\n      }\n    } catch (error) {\n      console.error(\"Lỗi kết nối:\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"list-product-title\",\n      children: \"List product \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"list-product-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"list-product-detail\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"STT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u1EA2nh M\\xF4 T\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1EA3n Ph\\u1EA9m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Nh\\xE0 Cung C\\u1EA5p\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"S\\u1ED1 L\\u01B0\\u1EE3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Th\\xE0nh Ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Mail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: listProductWereAdded.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"body-container-img-description\",\n                    style: {\n                      backgroundImage: `url(${product.imageUrl})`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"modal-body-product-name\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"modal-body-product-description\",\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: \"relative\"\n                  },\n                  children: product.supplier\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"Quantity\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"Quantity-button\",\n                    onClick: () => decrease(index),\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    value: listProductWereAdded[index].quantity,\n                    className: \"Quantity-input\",\n                    onChange: e => handleInputQuantitty(index, e)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"Quantity-button\",\n                    onClick: () => increase(index),\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [(product.price.replace(/\\./g, \"\") * listProductWereAdded[index].quantity).toLocaleString(), \" \", \"VND\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `product-status ${listProductWereAdded[index].status}`,\n                  onClick: () => handleStatusClick(index),\n                  style: {\n                    position: \"relative\",\n                    cursor: \"pointer\"\n                  },\n                  children: [product.status, dropdownOpenIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n                    ref: dropdownRef,\n                    className: \"dropdown\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item\",\n                      onClick: () => handleStatusChange(index, \"pending\"),\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item \",\n                      onClick: () => handleStatusChange(index, \"deliveried\"),\n                      children: \"Delivered\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dropdown-item \",\n                      onClick: () => handleStatusChange(index, \"canceled\"),\n                      children: \"Canceled\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: product.isChecked,\n                  onChange: () => handleRemove(index) // Call handler on change\n                  ,\n                  id: `checkbox-${index}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: listProductWereAdded[index].email,\n                  onChange: () => handleCheckboxChange(index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-tax\",\n        children: [\"TAX :\", \" \", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          style: {\n            borderRadius: \"8px\",\n            maxWidth: \"60px\",\n            border: \"1px solid #333\",\n            fontSize: \"16px\",\n            color: \"#333\",\n            textAlign: \"right\",\n            lineHeight: \"24px\",\n            paddingRight: \"8px\"\n          },\n          value: myTax,\n          name: \"tax\",\n          onChange: e => {\n            if (/^\\d*$/.test(e.target.value)) {\n              setMyTax(e.target.value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16,\n            fontWeight: 300\n          },\n          children: [\"   \", \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-tax\",\n        children: [\"T\\u1ED5ng ti\\u1EC1n:\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 16,\n            fontWeight: 300\n          },\n          children: [(amountBill() * (myTax + 100) / 100).toFixed(0).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\"), \" \", \"VND\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"complete-order\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleSubmit(),\n          children: \"Complete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s2(ContentOrder, \"QfhfeXTDA1ENpiUi3DRshx2eVm4=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c2 = ContentOrder;\nexport default Import2;\nvar _c, _c2;\n$RefreshReg$(_c, \"Import2\");\n$RefreshReg$(_c2, \"ContentOrder\");", "map": {"version": 3, "names": ["OrderManagement", "ModalHistory", "React", "useState", "useRef", "useEffect", "useCallback", "useContext", "axios", "debounce", "Modal", "ModalDetail", "useAuth", "notify", "useLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Import2", "_s", "isOpen", "setIsOpen", "searchTerm", "setSearchTerm", "results", "setResults", "showDropdown", "setShowDropdown", "openHistory", "setOpenHistory", "openDetail", "setOpenDetail", "suggestions", "setSuggestions", "suppOrPro", "setSuppOrPro", "idProductAdded", "setIdProductAdded", "idOrder", "setIdOrder", "dataTop", "setDataTop", "user", "loading", "apiGetOrder", "apiGetHistory", "view", "<PERSON><PERSON><PERSON><PERSON>", "loadLog", "setLoadLog", "loadOrder", "setLoadOrder", "openModal", "closeModal", "openModalHistory", "closeModalHistory", "closeModalDetail", "openModalDetail", "startLoading", "stopLoading", "fetchData", "res", "fetch", "id_owner", "dataRes", "json", "err", "console", "error", "handleSearch", "event", "term", "target", "value", "keyword", "trim", "startsWith", "substr", "length", "debouncedFetchSuggestions", "topData", "filter", "item", "name", "toLowerCase", "includes", "slice", "map", "log", "fetchProductSuggestions", "hrefLink", "response", "get", "params", "query", "ownerId", "sugg", "data", "s", "prev", "newData", "handleAddToOrder", "idPro", "suppliersId", "_id", "method", "headers", "ok", "handleBlur", "setTimeout", "handleSelectLiResult", "result", "children", "onCreateOrder", "onHistory", "refOrder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClose", "className", "style", "display", "flex", "marginLeft", "paddingTop", "type", "placeholder", "onChange", "onBlur", "onFocus", "index", "onClick", "backgroundImage", "image", "secure_url", "margin", "background", "ContentOrder", "dataHis", "apiFetchOrderHistory", "_c", "_s2", "initItem", "description", "supplier", "supplierDetails", "price", "purchasePrice", "replace", "imageUrl", "supplierId", "quantity", "status", "email", "isChecked", "emailName", "productId", "listProductWereAdded", "setListProductWereAdded", "listItem", "dropdownOpenIndex", "setDropdownOpenIndex", "isDropdownOpenSupplier", "setIsDropdownOpenSupplier", "Array", "fill", "selectedSupplier", "setSelectedSupplier", "quantities", "setQuantities", "product", "myTax", "setMyTax", "newItems", "some", "it", "prevList", "handleSupplierChange", "newList", "newSelectedSuppliers", "newDropdownState", "handleSupplierClick", "amountBill", "sum", "for<PERSON>ach", "toggleDropdown", "newOpen", "dropdownRef", "dropdownRefSupplier", "handleStatusClick", "handleStatusChange", "newStatus", "updatedProducts", "handleClickOutside", "current", "contains", "document", "addEventListener", "removeEventListener", "increase", "newQuantities", "decrease", "handleRemove", "splice", "handleInputQuantitty", "e", "newQuantity", "updatedList", "handleCheckboxChange", "handleSubmit", "groupBySupplier", "reduce", "acc", "dataForm", "push", "id", "role", "tax", "url", "body", "JSON", "stringify", "responseData", "fetchOrder", "statusText", "justifyContent", "alignItems", "position", "toLocaleString", "cursor", "ref", "checked", "borderRadius", "max<PERSON><PERSON><PERSON>", "border", "fontSize", "color", "textAlign", "lineHeight", "paddingRight", "test", "fontWeight", "toFixed", "toString", "_c2", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/Import/index2.js"], "sourcesContent": ["// import ImageUpload from \"../../components/Manage_product/image\"\r\n// import Change_password from\"../../components/introduce/resetpassword.js\"\r\nimport OrderManagement from \"../../components/test/index\";\r\nimport ModalHistory from \"./ModalHistory\";\r\nimport React, {\r\n  useState,\r\n  useRef,\r\n  useEffect,\r\n  useCallback,\r\n  useContext,\r\n} from \"react\";\r\nimport axios from \"axios\";\r\nimport debounce from \"lodash.debounce\";\r\nimport Modal from \"./../../components/ComponentExport/Modal\";\r\nimport \"./import.css\";\r\nimport ModalDetail from \"./ModalDetail\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nfunction Import2() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [results, setResults] = useState([]);\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [openHistory, setOpenHistory] = useState(false);\r\n  const [openDetail, setOpenDetail] = useState(false);\r\n  const [suggestions, setSuggestions] = useState([]);\r\n  const [suppOrPro, setSuppOrPro] = useState(false);\r\n  const [idProductAdded, setIdProductAdded] = useState([]);\r\n  const [idOrder, setIdOrder] = useState(null);\r\n  const [dataTop, setDataTop] = useState([]);\r\n  const { user, loading } = useAuth();\r\n  const apiGetOrder = useRef();\r\n  const apiGetHistory = useRef();\r\n  const [view, setView] = useState(true);\r\n  const [loadLog, setLoadLog] = useState(false);\r\n  const [loadOrder, setLoadOrder] = useState(false);\r\n  // const id_owner = user.id_owner;\r\n  const openModal = () => setIsOpen(true);\r\n  const closeModal = () => setIsOpen(false);\r\n  const openModalHistory = () => setOpenHistory(true);\r\n  const closeModalHistory = () => setOpenHistory(false);\r\n  const closeModalDetail = () => setOpenDetail(false);\r\n  const openModalDetail = () => setOpenDetail(true);\r\n  const { startLoading, stopLoading } = useLoading();\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        if (loading) return;\r\n        const res = await fetch(\r\n          `http://localhost:8080/api/import/orderHistory/lastProductTop100?ownerId=${user.id_owner}`\r\n        );\r\n        const dataRes = await res.json();\r\n        setDataTop(dataRes);\r\n      } catch (err) {\r\n        console.error(err);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [loading]);\r\n  const handleSearch = (event) => {\r\n    const term = event.target.value;\r\n    let keyword = term.trim();\r\n    setSearchTerm(term);\r\n    if (keyword.startsWith(\"@All\")) {\r\n      keyword = keyword.substr(4).trim();\r\n      setSuppOrPro(false);\r\n      if (keyword.length > 0) {\r\n        debouncedFetchSuggestions(\r\n          keyword,\r\n          `http://localhost:8080/api/import/supplier/search`\r\n        );\r\n      } else {\r\n        setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\r\n      }\r\n    } else {\r\n      setSuppOrPro(true);\r\n      if (keyword.length > 0) {\r\n        const topData = dataTop\r\n          .filter((item) =>\r\n            item.name.toLowerCase().includes(keyword.toLowerCase())\r\n          )\r\n          .slice(0, 5);\r\n        if (topData.length) {\r\n          setResults(topData.map((item) => item.name));\r\n          setSuggestions(topData);\r\n        } else {\r\n          console.log(\"jellooo\");\r\n          debouncedFetchSuggestions(\r\n            keyword,\r\n            `http://localhost:8080/api/import/products/exhibitProN`\r\n          );\r\n        }\r\n      } else {\r\n        setSuggestions([]); // Nếu không có từ khóa, xóa kết quả gợi ý\r\n      }\r\n    }\r\n  };\r\n  // database\r\n  const fetchProductSuggestions = async (keyword, hrefLink) => {\r\n    try {\r\n      const response = await axios.get(hrefLink, {\r\n        params: {\r\n          query: keyword,\r\n          ownerId: user.id_owner,\r\n        },\r\n      });\r\n      const sugg = response.data.map((s) => s.name);\r\n      setResults(sugg);\r\n      setDataTop((prev) => {\r\n        const newData = [...prev, ...response.data];\r\n        return newData;\r\n      });\r\n      setSuggestions(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching suggestions:\", error);\r\n    }\r\n  };\r\n  const debouncedFetchSuggestions = useCallback(\r\n    debounce(\r\n      (keyword, hrefLink) => fetchProductSuggestions(keyword, hrefLink),\r\n      500\r\n    ),\r\n    [user] // Chỉ tạo ra một lần\r\n  );\r\n\r\n  const handleAddToOrder = async () => {\r\n    const idPro = suggestions.filter((sugg) => sugg.name == searchTerm);\r\n    setSuggestions([]);\r\n    const suppliersId = idPro ? idPro[0] : null;\r\n    try {\r\n      // Gửi request GET với query string chứa productId\r\n      if (suppliersId) {\r\n        let response;\r\n        if (!suppOrPro) {\r\n          response = await fetch(\r\n            `http://localhost:8080/api/import/products/exhibitPro?productId=${suppliersId._id}&ownerId=${user.id_owner}`,\r\n            {\r\n              method: \"GET\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n            }\r\n          );\r\n        }\r\n\r\n        // Kiểm tra nếu request thành công\r\n        if (!suppOrPro && response.ok) {\r\n          const data = await response.json(); // Dữ liệu trả về từ server (có thể là chi tiết sản phẩm)\r\n          setIdProductAdded(data);\r\n          // Xử lý dữ liệu từ server (Hiển thị thông tin đơn hàng, ví dụ...)\r\n          setSearchTerm(\"\");\r\n          setResults([]);\r\n        } else if (suppOrPro) {\r\n          setIdProductAdded(idPro);\r\n          setSearchTerm(\"\");\r\n          setResults([]);\r\n        } else {\r\n          console.error(\"Error adding to order\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Request failed\", error);\r\n    }\r\n  };\r\n  const handleBlur = () => {\r\n    setTimeout(() => {\r\n      setShowDropdown(false);\r\n    }, 700);\r\n  };\r\n  const handleSelectLiResult = (result) => {\r\n    setSearchTerm(result); // Cập nhật giá trị input với kết quả đã chọn\r\n    setShowDropdown(false); // Ẩn dropdown sau khi chọn\r\n  };\r\n  //  console.log(apiGetHistory.current)\r\n  return (\r\n    <>\r\n      <OrderManagement\r\n        onCreateOrder={openModal}\r\n        onHistory={openModalHistory}\r\n        openModalDetail={openModalDetail}\r\n        setIdOrder={setIdOrder}\r\n        refOrder={apiGetOrder}\r\n        setView={setView}\r\n        loadOrder={loadOrder}\r\n        setLoadLog={setLoadLog}\r\n        setLoadOrder={setLoadOrder}\r\n      />\r\n\r\n      <Modal isOpen={isOpen} onClose={closeModal}>\r\n        <div className=\"Modal-title\">Create your order opening</div>\r\n        <div className=\"divide\"></div>\r\n        <div className=\"header-order\">\r\n          <div className=\"search-container\">\r\n            <div style={{ display: \"flex\", flex: 1, marginLeft: 10 }}>\r\n              <span style={{ display: \"block\", paddingTop: \"10px\" }}>\r\n                Tìm kiếm:{\" \"}\r\n              </span>\r\n              <div className=\"search-result-container\">\r\n                <input\r\n                  type=\"text\"\r\n                  style={{ flex: 1 }}\r\n                  className=\"order-mgmt-search\"\r\n                  placeholder=\"Search by code or product name\"\r\n                  value={searchTerm}\r\n                  onChange={handleSearch}\r\n                  onBlur={handleBlur} // Thêm onBlur để ẩn dropdown\r\n                  onFocus={() => setShowDropdown(true)} // Hiển thị dropdown khi focus\r\n                />\r\n                {showDropdown && results.length > 0 && (\r\n                  <ul className=\"dropdown\">\r\n                    {results.map((result, index) => (\r\n                      <li\r\n                        key={index}\r\n                        className=\"search-item\"\r\n                        onClick={() => handleSelectLiResult(result)}\r\n                      >\r\n                        <div className=\"search-container-item\">\r\n                          {result}\r\n                          {suppOrPro && suggestions.length > 0 && (\r\n                            <div\r\n                              className=\"search-container-img\"\r\n                              style={{\r\n                                backgroundImage: `url(${suggestions[index].image.secure_url})`,\r\n                              }}\r\n                            ></div>\r\n                          )}\r\n                        </div>\r\n                        <div\r\n                          className=\"divide\"\r\n                          style={{ margin: \"8px 2px 0\", background: \"white\" }}\r\n                        ></div>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button className=\"btn-add-order\" onClick={handleAddToOrder}>\r\n            Add to order\r\n          </button>\r\n        </div>\r\n        <div className=\"body-modal\">\r\n          <ContentOrder\r\n            dataHis={idProductAdded}\r\n            setIdProductAdded={setIdProductAdded}\r\n            apiFetchOrderHistory={apiGetOrder}\r\n            apiGetHistory={apiGetHistory}\r\n          />\r\n        </div>\r\n      </Modal>\r\n      <ModalHistory\r\n        isOpen={openHistory}\r\n        onClose={closeModalHistory}\r\n        openModalDetail={openModalDetail}\r\n        setIdOrder={setIdOrder}\r\n        apiGetHistory={apiGetHistory}\r\n        setView={setView}\r\n        loadLog={loadLog}\r\n      />\r\n      <ModalDetail\r\n        isOpen={openDetail}\r\n        onClose={closeModalDetail}\r\n        idOrder={idOrder}\r\n        view={view}\r\n        setLoadLog={setLoadLog}\r\n        setLoadOrder={setLoadOrder}\r\n      >\r\n        {\" \"}\r\n      </ModalDetail>\r\n    </>\r\n    // <div style={{ textAlign: 'center', margin: '20px' }}>\r\n    //   <input\r\n    //     type=\"file\"\r\n    //     accept=\"image/*\"\r\n    //     onChange={handleImageChange}\r\n    //   />\r\n    //   {selectedImage && (\r\n    //     <div style={{ marginTop: '20px' }}>\r\n    //       <h3>Ảnh đã tải lên:</h3>\r\n    //       <img\r\n    //         src={selectedImage}\r\n    //         alt=\"Uploaded\"\r\n    //         style={{ maxWidth: '300px', maxHeight: '300px' }}\r\n    //       />\r\n    //     </div>\r\n    //   )}\r\n    // </div>\r\n  );\r\n}\r\n\r\nconst ContentOrder = ({\r\n  dataHis,\r\n  setIdProductAdded,\r\n  apiFetchOrderHistory,\r\n  apiGetHistory,\r\n}) => {\r\n  const initItem = (item) => {\r\n    return {\r\n      name: item.name,\r\n      description: item.description,\r\n      supplier: item.supplierDetails.name,\r\n      price: item.purchasePrice.replace(/\\./g, \"\"),\r\n      imageUrl: item.image.secure_url,\r\n      supplierId: item.supplierDetails._id,\r\n      quantity: 1,\r\n      status: \"pending\",\r\n      email: true,\r\n      isChecked: true,\r\n      emailName: item.supplierDetails.email,\r\n      productId: item._id,\r\n    };\r\n  };\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, loading } = useAuth();\r\n  const [listProductWereAdded, setListProductWereAdded] = useState([]);\r\n  const listItem = dataHis.map((item) => initItem(item));\r\n  const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);\r\n  const [isDropdownOpenSupplier, setIsDropdownOpenSupplier] = useState(\r\n    Array(listProductWereAdded.length).fill(false)\r\n  );\r\n  const [selectedSupplier, setSelectedSupplier] = useState(\r\n    Array(listProductWereAdded.length).fill(\"\")\r\n  );\r\n  const [quantities, setQuantities] = useState(\r\n    listProductWereAdded.map((product) => product.quantity) // Khởi tạo mảng quantity từ listProductWereAdded\r\n  );\r\n\r\n  const [isOpen, setIsOpen] = useState(\r\n    new Array(listProductWereAdded.length).fill(false)\r\n  ); // Khởi tạo mảng isOpen\r\n  const [myTax, setMyTax] = useState(10);\r\n  useEffect(() => {\r\n    if (dataHis && dataHis.length > 0) {\r\n      const newItems = dataHis.map(initItem);\r\n      console.log(dataHis, listProductWereAdded);\r\n      if (\r\n        !listProductWereAdded.some((item) =>\r\n          dataHis.some((it) => it._id === item.productId)\r\n        )\r\n      ) {\r\n        setListProductWereAdded((prevList) => [...newItems, ...prevList]);\r\n      }\r\n      setIdProductAdded([]);\r\n    }\r\n  }, [dataHis]);\r\n  const handleSupplierChange = (supplier, index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newList = [...prev];\r\n      newList[index].supplier = supplier; // Cập nhật nhà cung cấp cho ô hiện tại\r\n      return newList;\r\n    });\r\n\r\n    // Cập nhật selectedSupplier\r\n    setSelectedSupplier((prev) => {\r\n      const newSelectedSuppliers = [...prev];\r\n      newSelectedSuppliers[index] = supplier; // Lưu giá trị đã chọn\r\n      return newSelectedSuppliers;\r\n    });\r\n\r\n    // Ẩn dropdown sau khi chọn\r\n    setIsDropdownOpenSupplier((prev) => {\r\n      const newDropdownState = [...prev];\r\n      newDropdownState[index] = false; // Ẩn dropdown cho ô hiện tại\r\n      return newDropdownState;\r\n    });\r\n  };\r\n  const handleSupplierClick = (index) => {\r\n    setIsDropdownOpenSupplier((prev) => {\r\n      const newDropdownState = [...prev];\r\n      newDropdownState[index] = !newDropdownState[index]; // Đảo ngược trạng thái cho ô hiện tại\r\n      return newDropdownState;\r\n    });\r\n  };\r\n  const amountBill = () => {\r\n    let sum = 0;\r\n    listProductWereAdded.forEach((product) => {\r\n      sum += product.price.replace(/\\./g, \"\") * product.quantity;\r\n    });\r\n    return sum;\r\n  };\r\n  const toggleDropdown = (index) => {\r\n    setIsOpen((prev) => {\r\n      const newOpen = [...prev];\r\n      newOpen[index] = !newOpen[index]; // Đảo ngược giá trị tại index\r\n      return newOpen;\r\n    });\r\n  };\r\n\r\n  const dropdownRef = useRef(null);\r\n  const dropdownRefSupplier = useRef(null);\r\n  const handleStatusClick = (index) => {\r\n    setDropdownOpenIndex((prev) => (prev === index ? null : index));\r\n  };\r\n\r\n  const handleStatusChange = (index, newStatus) => {\r\n    setListProductWereAdded((prev) => {\r\n      const updatedProducts = [...prev];\r\n      updatedProducts[index].status = newStatus;\r\n      setDropdownOpenIndex(null);\r\n      return updatedProducts;\r\n    });\r\n    // Ẩn dropdown sau khi chọn\r\n  };\r\n  const handleClickOutside = (event) => {\r\n    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n      setDropdownOpenIndex(null); // Ẩn dropdown khi click ra ngoài\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const increase = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newQuantities = [...prev];\r\n      newQuantities[index].quantity += 1; // Tăng giá trị\r\n      return newQuantities;\r\n    });\r\n  };\r\n\r\n  const decrease = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newQuantities = [...prev];\r\n      if (newQuantities[index].quantity > 0) {\r\n        newQuantities[index].quantity -= 1; // Tăng giá trị\r\n      }\r\n      return newQuantities;\r\n    });\r\n  };\r\n\r\n  const handleRemove = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const newList = [...prev];\r\n      newList.splice(index, 1); // Xoá phần tử\r\n      return newList;\r\n    });\r\n\r\n    setIsOpen((prev) => {\r\n      const newOpen = [...prev];\r\n      newOpen.splice(index, 1); // Cập nhật mảng isOpen\r\n      return newOpen;\r\n    });\r\n  };\r\n  const handleInputQuantitty = (index, e) => {\r\n    const newQuantity = e.target.value; // Lấy giá trị mới từ input\r\n    setListProductWereAdded((prev) => {\r\n      // Tạo bản sao của danh sách hiện tại\r\n      const updatedList = [...prev];\r\n      // Cập nhật số lượng sản phẩm tại chỉ số index\r\n      updatedList[index] = {\r\n        ...updatedList[index],\r\n        quantity: newQuantity,\r\n      };\r\n      return updatedList; // Trả về danh sách đã cập nhật\r\n    });\r\n  };\r\n  const handleCheckboxChange = (index) => {\r\n    setListProductWereAdded((prev) => {\r\n      const updatedProducts = [...listProductWereAdded];\r\n      updatedProducts[index].email = !updatedProducts[index].email;\r\n      return updatedProducts;\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    const groupBySupplier = listProductWereAdded.reduce(\r\n      (acc, item) => {\r\n        // Kiểm tra xem đã có supplier này trong nhóm chưa\r\n        if (!acc.dataForm[item.supplier]) {\r\n          acc.dataForm[item.supplier] = [];\r\n        }\r\n        acc.dataForm[item.supplier].push(item); // Thêm item vào đúng nhóm\r\n        return acc;\r\n      },\r\n      { user: {}, dataForm: {} }\r\n    );\r\n    groupBySupplier.user = {\r\n      id: user._id,\r\n      name: user.name,\r\n      email: user.email,\r\n      ownerId: user.id_owner,\r\n      id_owner: user.id_owner,\r\n      role: user.role,\r\n    };\r\n    groupBySupplier.tax = myTax;\r\n    const url = \"http://localhost:8080/api/import/orderHistory/save\";\r\n\r\n    try {\r\n      const response = await fetch(url, {\r\n        method: \"POST\", // Phương thức POST\r\n        headers: {\r\n          \"Content-Type\": \"application/json\", // Xác định kiểu dữ liệu là JSON\r\n        },\r\n        body: JSON.stringify(groupBySupplier), // Chuyển đổi dữ liệu thành chuỗi JSON\r\n      });\r\n\r\n      if (response.ok) {\r\n        // Nếu thành công, xử lý kết quả\r\n        notify(1, \"you've completed importing goods\", \"Successfully!\");\r\n        const responseData = await response.json();\r\n        console.log(\"Dữ liệu đã được gửi thành công\", responseData);\r\n        await apiFetchOrderHistory.current.fetchOrder(\"\");\r\n        await apiGetHistory.current.debouncedFetchSuggestions(\r\n          \" \",\r\n          \"http://localhost:8080/api/import/loggingOrder/listOrder\",\r\n          1,\r\n          10\r\n        );\r\n\r\n        setIdProductAdded([]);\r\n        setListProductWereAdded([]);\r\n      } else {\r\n        notify(2, \"you don't have the role to do this\", \"Fail!\");\r\n        // Nếu có lỗi từ server\r\n        console.error(\"Lỗi khi gửi dữ liệu:\", response.statusText);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Lỗi kết nối:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"list-product-title\">List product </div>\r\n      <div className=\"list-product-content\">\r\n        <div className=\"list-product-detail\">\r\n          <table>\r\n            <thead>\r\n              <tr>\r\n                <th>STT</th>\r\n                <th>Ảnh Mô Tả</th>\r\n                <th>Sản Phẩm</th>\r\n                <th>Nhà Cung Cấp</th>\r\n                <th>Số Lượng</th>\r\n                <th>Thành Tiền</th>\r\n                <th>Status</th>\r\n                <th>Delete</th>\r\n                <th>Mail</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {listProductWereAdded.map((product, index) => (\r\n                <tr key={index}>\r\n                  <td>{index + 1}</td>\r\n                  <td>\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                      }}\r\n                    >\r\n                      <div\r\n                        className=\"body-container-img-description\"\r\n                        style={{ backgroundImage: `url(${product.imageUrl})` }}\r\n                      ></div>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"modal-body-product-name\">\r\n                      {product.name}\r\n                    </div>\r\n                    <div className=\"modal-body-product-description\">\r\n                      {product.description}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div style={{ position: \"relative\" }}>\r\n                      {product.supplier}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <div className=\"Quantity\">\r\n                      <button\r\n                        className=\"Quantity-button\"\r\n                        onClick={() => decrease(index)}\r\n                      >\r\n                        -\r\n                      </button>\r\n                      <input\r\n                        value={listProductWereAdded[index].quantity}\r\n                        className=\"Quantity-input\"\r\n                        onChange={(e) => handleInputQuantitty(index, e)}\r\n                      />\r\n                      <button\r\n                        className=\"Quantity-button\"\r\n                        onClick={() => increase(index)}\r\n                      >\r\n                        +\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    {(\r\n                      product.price.replace(/\\./g, \"\") *\r\n                      listProductWereAdded[index].quantity\r\n                    ).toLocaleString()}{\" \"}\r\n                    VND\r\n                  </td>\r\n                  <td>\r\n                    <div\r\n                      className={`product-status ${listProductWereAdded[index].status}`}\r\n                      onClick={() => handleStatusClick(index)}\r\n                      style={{ position: \"relative\", cursor: \"pointer\" }}\r\n                    >\r\n                      {product.status}\r\n                      {dropdownOpenIndex === index && (\r\n                        <div ref={dropdownRef} className=\"dropdown\">\r\n                          <div\r\n                            className=\"dropdown-item\"\r\n                            onClick={() => handleStatusChange(index, \"pending\")}\r\n                          >\r\n                            Pending\r\n                          </div>\r\n                          <div\r\n                            className=\"dropdown-item \"\r\n                            onClick={() =>\r\n                              handleStatusChange(index, \"deliveried\")\r\n                            }\r\n                          >\r\n                            Delivered\r\n                          </div>\r\n                          <div\r\n                            className=\"dropdown-item \"\r\n                            onClick={() =>\r\n                              handleStatusChange(index, \"canceled\")\r\n                            }\r\n                          >\r\n                            Canceled\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                  <td>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={product.isChecked}\r\n                      onChange={() => handleRemove(index)} // Call handler on change\r\n                      id={`checkbox-${index}`}\r\n                    />\r\n                  </td>\r\n                  <td>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={listProductWereAdded[index].email}\r\n                      onChange={() => handleCheckboxChange(index)}\r\n                    />\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className=\"order-tax\">\r\n          TAX :{\" \"}\r\n          <input\r\n            type=\"text\"\r\n            style={{\r\n              borderRadius: \"8px\",\r\n              maxWidth: \"60px\",\r\n              border: \"1px solid #333\",\r\n              fontSize: \"16px\",\r\n              color: \"#333\",\r\n              textAlign: \"right\",\r\n              lineHeight: \"24px\",\r\n              paddingRight: \"8px\",\r\n            }}\r\n            value={myTax}\r\n            name=\"tax\"\r\n            onChange={(e) => {\r\n              if (/^\\d*$/.test(e.target.value)) {\r\n                setMyTax(e.target.value);\r\n              }\r\n            }}\r\n          />\r\n          <span style={{ fontSize: 16, fontWeight: 300 }}>{\"   \"}%</span>{\" \"}\r\n        </div>\r\n        <div className=\"order-tax\">\r\n          Tổng tiền:{\" \"}\r\n          <span style={{ fontSize: 16, fontWeight: 300 }}>\r\n            {((amountBill() * (myTax + 100)) / 100)\r\n              .toFixed(0)\r\n              .toString()\r\n              .replace(/\\B(?=(\\d{3})+(?!\\d))/g, \".\")}{\" \"}\r\n            VND\r\n          </span>\r\n        </div>\r\n        <div className=\"complete-order\">\r\n          <button onClick={() => handleSubmit()}>Complete</button>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Import2;\r\n"], "mappings": ";;;AAAA;AACA;AACA,OAAOA,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,KAAK,IACVC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,QACL,OAAO;AACd,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,KAAK,MAAM,0CAA0C;AAC5D,OAAO,cAAc;AACrB,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,UAAU,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAChE,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IAAEwC,IAAI;IAAEC;EAAQ,CAAC,GAAGhC,OAAO,CAAC,CAAC;EACnC,MAAMiC,WAAW,GAAGzC,MAAM,CAAC,CAAC;EAC5B,MAAM0C,aAAa,GAAG1C,MAAM,CAAC,CAAC;EAC9B,MAAM,CAAC2C,IAAI,EAAEC,OAAO,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA,MAAMkD,SAAS,GAAGA,CAAA,KAAM/B,SAAS,CAAC,IAAI,CAAC;EACvC,MAAMgC,UAAU,GAAGA,CAAA,KAAMhC,SAAS,CAAC,KAAK,CAAC;EACzC,MAAMiC,gBAAgB,GAAGA,CAAA,KAAMzB,cAAc,CAAC,IAAI,CAAC;EACnD,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM1B,cAAc,CAAC,KAAK,CAAC;EACrD,MAAM2B,gBAAgB,GAAGA,CAAA,KAAMzB,aAAa,CAAC,KAAK,CAAC;EACnD,MAAM0B,eAAe,GAAGA,CAAA,KAAM1B,aAAa,CAAC,IAAI,CAAC;EACjD,MAAM;IAAE2B,YAAY;IAAEC;EAAY,CAAC,GAAG9C,UAAU,CAAC,CAAC;EAClDT,SAAS,CAAC,MAAM;IACd,MAAMwD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,IAAIjB,OAAO,EAAE;QACb,MAAMkB,GAAG,GAAG,MAAMC,KAAK,CACrB,2EAA2EpB,IAAI,CAACqB,QAAQ,EAC1F,CAAC;QACD,MAAMC,OAAO,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,CAAC;QAChCxB,UAAU,CAACuB,OAAO,CAAC;MACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;MACpB;IACF,CAAC;IACDN,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;EACb,MAAM0B,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC/B,IAAIC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAAC,CAAC;IACzBpD,aAAa,CAACgD,IAAI,CAAC;IACnB,IAAIG,OAAO,CAACE,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9BF,OAAO,GAAGA,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;MAClCxC,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIuC,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACtBC,yBAAyB,CACvBL,OAAO,EACP,kDACF,CAAC;MACH,CAAC,MAAM;QACLzC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,MAAM;MACLE,YAAY,CAAC,IAAI,CAAC;MAClB,IAAIuC,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACtB,MAAME,OAAO,GAAGxC,OAAO,CACpByC,MAAM,CAAEC,IAAI,IACXA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACX,OAAO,CAACU,WAAW,CAAC,CAAC,CACxD,CAAC,CACAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,IAAIN,OAAO,CAACF,MAAM,EAAE;UAClBrD,UAAU,CAACuD,OAAO,CAACO,GAAG,CAAEL,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC;UAC5ClD,cAAc,CAAC+C,OAAO,CAAC;QACzB,CAAC,MAAM;UACLb,OAAO,CAACqB,GAAG,CAAC,SAAS,CAAC;UACtBT,yBAAyB,CACvBL,OAAO,EACP,uDACF,CAAC;QACH;MACF,CAAC,MAAM;QACLzC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB;IACF;EACF,CAAC;EACD;EACA,MAAMwD,uBAAuB,GAAG,MAAAA,CAAOf,OAAO,EAAEgB,QAAQ,KAAK;IAC3D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpF,KAAK,CAACqF,GAAG,CAACF,QAAQ,EAAE;QACzCG,MAAM,EAAE;UACNC,KAAK,EAAEpB,OAAO;UACdqB,OAAO,EAAErD,IAAI,CAACqB;QAChB;MACF,CAAC,CAAC;MACF,MAAMiC,IAAI,GAAGL,QAAQ,CAACM,IAAI,CAACV,GAAG,CAAEW,CAAC,IAAKA,CAAC,CAACf,IAAI,CAAC;MAC7C1D,UAAU,CAACuE,IAAI,CAAC;MAChBvD,UAAU,CAAE0D,IAAI,IAAK;QACnB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,EAAE,GAAGR,QAAQ,CAACM,IAAI,CAAC;QAC3C,OAAOG,OAAO;MAChB,CAAC,CAAC;MACFnE,cAAc,CAAC0D,QAAQ,CAACM,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EACD,MAAMW,yBAAyB,GAAG1E,WAAW,CAC3CG,QAAQ,CACN,CAACkE,OAAO,EAAEgB,QAAQ,KAAKD,uBAAuB,CAACf,OAAO,EAAEgB,QAAQ,CAAC,EACjE,GACF,CAAC,EACD,CAAChD,IAAI,CAAC,CAAC;EACT,CAAC;EAED,MAAM2D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMC,KAAK,GAAGtE,WAAW,CAACiD,MAAM,CAAEe,IAAI,IAAKA,IAAI,CAACb,IAAI,IAAI7D,UAAU,CAAC;IACnEW,cAAc,CAAC,EAAE,CAAC;IAClB,MAAMsE,WAAW,GAAGD,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IAC3C,IAAI;MACF;MACA,IAAIC,WAAW,EAAE;QACf,IAAIZ,QAAQ;QACZ,IAAI,CAACzD,SAAS,EAAE;UACdyD,QAAQ,GAAG,MAAM7B,KAAK,CACpB,kEAAkEyC,WAAW,CAACC,GAAG,YAAY9D,IAAI,CAACqB,QAAQ,EAAE,EAC5G;YACE0C,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CACF,CAAC;QACH;;QAEA;QACA,IAAI,CAACxE,SAAS,IAAIyD,QAAQ,CAACgB,EAAE,EAAE;UAC7B,MAAMV,IAAI,GAAG,MAAMN,QAAQ,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC;UACpC5B,iBAAiB,CAAC4D,IAAI,CAAC;UACvB;UACA1E,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,MAAM,IAAIS,SAAS,EAAE;UACpBG,iBAAiB,CAACiE,KAAK,CAAC;UACxB/E,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,MAAM;UACL0C,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC;QACxC;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EACD,MAAMwC,UAAU,GAAGA,CAAA,KAAM;IACvBC,UAAU,CAAC,MAAM;MACflF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACD,MAAMmF,oBAAoB,GAAIC,MAAM,IAAK;IACvCxF,aAAa,CAACwF,MAAM,CAAC,CAAC,CAAC;IACvBpF,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1B,CAAC;EACD;EACA,oBACEZ,OAAA,CAAAE,SAAA;IAAA+F,QAAA,gBACEjG,OAAA,CAAChB,eAAe;MACdkH,aAAa,EAAE7D,SAAU;MACzB8D,SAAS,EAAE5D,gBAAiB;MAC5BG,eAAe,EAAEA,eAAgB;MACjClB,UAAU,EAAEA,UAAW;MACvB4E,QAAQ,EAAEvE,WAAY;MACtBG,OAAO,EAAEA,OAAQ;MACjBG,SAAS,EAAEA,SAAU;MACrBD,UAAU,EAAEA,UAAW;MACvBE,YAAY,EAAEA;IAAa;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFxG,OAAA,CAACN,KAAK;MAACW,MAAM,EAAEA,MAAO;MAACoG,OAAO,EAAEnE,UAAW;MAAA2D,QAAA,gBACzCjG,OAAA;QAAK0G,SAAS,EAAC,aAAa;QAAAT,QAAA,EAAC;MAAyB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DxG,OAAA;QAAK0G,SAAS,EAAC;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9BxG,OAAA;QAAK0G,SAAS,EAAC,cAAc;QAAAT,QAAA,gBAC3BjG,OAAA;UAAK0G,SAAS,EAAC,kBAAkB;UAAAT,QAAA,eAC/BjG,OAAA;YAAK2G,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,IAAI,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAG,CAAE;YAAAb,QAAA,gBACvDjG,OAAA;cAAM2G,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEG,UAAU,EAAE;cAAO,CAAE;cAAAd,QAAA,GAAC,mBAC5C,EAAC,GAAG;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPxG,OAAA;cAAK0G,SAAS,EAAC,yBAAyB;cAAAT,QAAA,gBACtCjG,OAAA;gBACEgH,IAAI,EAAC,MAAM;gBACXL,KAAK,EAAE;kBAAEE,IAAI,EAAE;gBAAE,CAAE;gBACnBH,SAAS,EAAC,mBAAmB;gBAC7BO,WAAW,EAAC,gCAAgC;gBAC5CvD,KAAK,EAAEnD,UAAW;gBAClB2G,QAAQ,EAAE5D,YAAa;gBACvB6D,MAAM,EAAEtB,UAAW,CAAC;gBAAA;gBACpBuB,OAAO,EAAEA,CAAA,KAAMxG,eAAe,CAAC,IAAI,CAAE,CAAC;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,EACD7F,YAAY,IAAIF,OAAO,CAACsD,MAAM,GAAG,CAAC,iBACjC/D,OAAA;gBAAI0G,SAAS,EAAC,UAAU;gBAAAT,QAAA,EACrBxF,OAAO,CAAC+D,GAAG,CAAC,CAACwB,MAAM,EAAEqB,KAAK,kBACzBrH,OAAA;kBAEE0G,SAAS,EAAC,aAAa;kBACvBY,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAACC,MAAM,CAAE;kBAAAC,QAAA,gBAE5CjG,OAAA;oBAAK0G,SAAS,EAAC,uBAAuB;oBAAAT,QAAA,GACnCD,MAAM,EACN7E,SAAS,IAAIF,WAAW,CAAC8C,MAAM,GAAG,CAAC,iBAClC/D,OAAA;sBACE0G,SAAS,EAAC,sBAAsB;sBAChCC,KAAK,EAAE;wBACLY,eAAe,EAAE,OAAOtG,WAAW,CAACoG,KAAK,CAAC,CAACG,KAAK,CAACC,UAAU;sBAC7D;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNxG,OAAA;oBACE0G,SAAS,EAAC,QAAQ;oBAClBC,KAAK,EAAE;sBAAEe,MAAM,EAAE,WAAW;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA,GAlBFa,KAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBR,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxG,OAAA;UAAQ0G,SAAS,EAAC,eAAe;UAACY,OAAO,EAAEhC,gBAAiB;UAAAW,QAAA,EAAC;QAE7D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNxG,OAAA;QAAK0G,SAAS,EAAC,YAAY;QAAAT,QAAA,eACzBjG,OAAA,CAAC4H,YAAY;UACXC,OAAO,EAAExG,cAAe;UACxBC,iBAAiB,EAAEA,iBAAkB;UACrCwG,oBAAoB,EAAEjG,WAAY;UAClCC,aAAa,EAAEA;QAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACRxG,OAAA,CAACf,YAAY;MACXoB,MAAM,EAAEQ,WAAY;MACpB4F,OAAO,EAAEjE,iBAAkB;MAC3BE,eAAe,EAAEA,eAAgB;MACjClB,UAAU,EAAEA,UAAW;MACvBM,aAAa,EAAEA,aAAc;MAC7BE,OAAO,EAAEA,OAAQ;MACjBC,OAAO,EAAEA;IAAQ;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eACFxG,OAAA,CAACL,WAAW;MACVU,MAAM,EAAEU,UAAW;MACnB0F,OAAO,EAAEhE,gBAAiB;MAC1BlB,OAAO,EAAEA,OAAQ;MACjBQ,IAAI,EAAEA,IAAK;MACXG,UAAU,EAAEA,UAAW;MACvBE,YAAY,EAAEA,YAAa;MAAA6D,QAAA,EAE1B;IAAG;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA,eACd;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ;AAACpG,EAAA,CA/QQD,OAAO;EAAA,QAYYP,OAAO,EAaKE,UAAU;AAAA;AAAAiI,EAAA,GAzBzC5H,OAAO;AAiRhB,MAAMyH,YAAY,GAAGA,CAAC;EACpBC,OAAO;EACPvG,iBAAiB;EACjBwG,oBAAoB;EACpBhG;AACF,CAAC,KAAK;EAAAkG,GAAA;EACJ,MAAMC,QAAQ,GAAI9D,IAAI,IAAK;IACzB,OAAO;MACLC,IAAI,EAAED,IAAI,CAACC,IAAI;MACf8D,WAAW,EAAE/D,IAAI,CAAC+D,WAAW;MAC7BC,QAAQ,EAAEhE,IAAI,CAACiE,eAAe,CAAChE,IAAI;MACnCiE,KAAK,EAAElE,IAAI,CAACmE,aAAa,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC5CC,QAAQ,EAAErE,IAAI,CAACqD,KAAK,CAACC,UAAU;MAC/BgB,UAAU,EAAEtE,IAAI,CAACiE,eAAe,CAAC3C,GAAG;MACpCiD,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE3E,IAAI,CAACiE,eAAe,CAACQ,KAAK;MACrCG,SAAS,EAAE5E,IAAI,CAACsB;IAClB,CAAC;EACH,CAAC;EACD,MAAM;IAAE9C,YAAY;IAAEC;EAAY,CAAC,GAAG9C,UAAU,CAAC,CAAC;EAClD,MAAM;IAAE6B,IAAI;IAAEC;EAAQ,CAAC,GAAGhC,OAAO,CAAC,CAAC;EACnC,MAAM,CAACoJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM+J,QAAQ,GAAGrB,OAAO,CAACrD,GAAG,CAAEL,IAAI,IAAK8D,QAAQ,CAAC9D,IAAI,CAAC,CAAC;EACtD,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnK,QAAQ,CAClEoK,KAAK,CAACP,oBAAoB,CAACjF,MAAM,CAAC,CAACyF,IAAI,CAAC,KAAK,CAC/C,CAAC;EACD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvK,QAAQ,CACtDoK,KAAK,CAACP,oBAAoB,CAACjF,MAAM,CAAC,CAACyF,IAAI,CAAC,EAAE,CAC5C,CAAC;EACD,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGzK,QAAQ,CAC1C6J,oBAAoB,CAACxE,GAAG,CAAEqF,OAAO,IAAKA,OAAO,CAACnB,QAAQ,CAAC,CAAC;EAC1D,CAAC;EAED,MAAM,CAACrI,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAClC,IAAIoK,KAAK,CAACP,oBAAoB,CAACjF,MAAM,CAAC,CAACyF,IAAI,CAAC,KAAK,CACnD,CAAC,CAAC,CAAC;EACH,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EACtCE,SAAS,CAAC,MAAM;IACd,IAAIwI,OAAO,IAAIA,OAAO,CAAC9D,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMiG,QAAQ,GAAGnC,OAAO,CAACrD,GAAG,CAACyD,QAAQ,CAAC;MACtC7E,OAAO,CAACqB,GAAG,CAACoD,OAAO,EAAEmB,oBAAoB,CAAC;MAC1C,IACE,CAACA,oBAAoB,CAACiB,IAAI,CAAE9F,IAAI,IAC9B0D,OAAO,CAACoC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACzE,GAAG,KAAKtB,IAAI,CAAC4E,SAAS,CAChD,CAAC,EACD;QACAE,uBAAuB,CAAEkB,QAAQ,IAAK,CAAC,GAAGH,QAAQ,EAAE,GAAGG,QAAQ,CAAC,CAAC;MACnE;MACA7I,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC,EAAE,CAACuG,OAAO,CAAC,CAAC;EACb,MAAMuC,oBAAoB,GAAGA,CAACjC,QAAQ,EAAEd,KAAK,KAAK;IAChD4B,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAMiF,OAAO,GAAG,CAAC,GAAGjF,IAAI,CAAC;MACzBiF,OAAO,CAAChD,KAAK,CAAC,CAACc,QAAQ,GAAGA,QAAQ,CAAC,CAAC;MACpC,OAAOkC,OAAO;IAChB,CAAC,CAAC;;IAEF;IACAX,mBAAmB,CAAEtE,IAAI,IAAK;MAC5B,MAAMkF,oBAAoB,GAAG,CAAC,GAAGlF,IAAI,CAAC;MACtCkF,oBAAoB,CAACjD,KAAK,CAAC,GAAGc,QAAQ,CAAC,CAAC;MACxC,OAAOmC,oBAAoB;IAC7B,CAAC,CAAC;;IAEF;IACAhB,yBAAyB,CAAElE,IAAI,IAAK;MAClC,MAAMmF,gBAAgB,GAAG,CAAC,GAAGnF,IAAI,CAAC;MAClCmF,gBAAgB,CAAClD,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;MACjC,OAAOkD,gBAAgB;IACzB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,mBAAmB,GAAInD,KAAK,IAAK;IACrCiC,yBAAyB,CAAElE,IAAI,IAAK;MAClC,MAAMmF,gBAAgB,GAAG,CAAC,GAAGnF,IAAI,CAAC;MAClCmF,gBAAgB,CAAClD,KAAK,CAAC,GAAG,CAACkD,gBAAgB,CAAClD,KAAK,CAAC,CAAC,CAAC;MACpD,OAAOkD,gBAAgB;IACzB,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIC,GAAG,GAAG,CAAC;IACX1B,oBAAoB,CAAC2B,OAAO,CAAEd,OAAO,IAAK;MACxCa,GAAG,IAAIb,OAAO,CAACxB,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAGsB,OAAO,CAACnB,QAAQ;IAC5D,CAAC,CAAC;IACF,OAAOgC,GAAG;EACZ,CAAC;EACD,MAAME,cAAc,GAAIvD,KAAK,IAAK;IAChC/G,SAAS,CAAE8E,IAAI,IAAK;MAClB,MAAMyF,OAAO,GAAG,CAAC,GAAGzF,IAAI,CAAC;MACzByF,OAAO,CAACxD,KAAK,CAAC,GAAG,CAACwD,OAAO,CAACxD,KAAK,CAAC,CAAC,CAAC;MAClC,OAAOwD,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAG1L,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM2L,mBAAmB,GAAG3L,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM4L,iBAAiB,GAAI3D,KAAK,IAAK;IACnC+B,oBAAoB,CAAEhE,IAAI,IAAMA,IAAI,KAAKiC,KAAK,GAAG,IAAI,GAAGA,KAAM,CAAC;EACjE,CAAC;EAED,MAAM4D,kBAAkB,GAAGA,CAAC5D,KAAK,EAAE6D,SAAS,KAAK;IAC/CjC,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAM+F,eAAe,GAAG,CAAC,GAAG/F,IAAI,CAAC;MACjC+F,eAAe,CAAC9D,KAAK,CAAC,CAACsB,MAAM,GAAGuC,SAAS;MACzC9B,oBAAoB,CAAC,IAAI,CAAC;MAC1B,OAAO+B,eAAe;IACxB,CAAC,CAAC;IACF;EACF,CAAC;EACD,MAAMC,kBAAkB,GAAI7H,KAAK,IAAK;IACpC,IAAIuH,WAAW,CAACO,OAAO,IAAI,CAACP,WAAW,CAACO,OAAO,CAACC,QAAQ,CAAC/H,KAAK,CAACE,MAAM,CAAC,EAAE;MACtE2F,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC;EAED/J,SAAS,CAAC,MAAM;IACdkM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXG,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,QAAQ,GAAIrE,KAAK,IAAK;IAC1B4B,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAMuG,aAAa,GAAG,CAAC,GAAGvG,IAAI,CAAC;MAC/BuG,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,IAAI,CAAC,CAAC,CAAC;MACpC,OAAOiD,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAIvE,KAAK,IAAK;IAC1B4B,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAMuG,aAAa,GAAG,CAAC,GAAGvG,IAAI,CAAC;MAC/B,IAAIuG,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,GAAG,CAAC,EAAE;QACrCiD,aAAa,CAACtE,KAAK,CAAC,CAACqB,QAAQ,IAAI,CAAC,CAAC,CAAC;MACtC;MACA,OAAOiD,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,YAAY,GAAIxE,KAAK,IAAK;IAC9B4B,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAMiF,OAAO,GAAG,CAAC,GAAGjF,IAAI,CAAC;MACzBiF,OAAO,CAACyB,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOgD,OAAO;IAChB,CAAC,CAAC;IAEF/J,SAAS,CAAE8E,IAAI,IAAK;MAClB,MAAMyF,OAAO,GAAG,CAAC,GAAGzF,IAAI,CAAC;MACzByF,OAAO,CAACiB,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOwD,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMkB,oBAAoB,GAAGA,CAAC1E,KAAK,EAAE2E,CAAC,KAAK;IACzC,MAAMC,WAAW,GAAGD,CAAC,CAACvI,MAAM,CAACC,KAAK,CAAC,CAAC;IACpCuF,uBAAuB,CAAE7D,IAAI,IAAK;MAChC;MACA,MAAM8G,WAAW,GAAG,CAAC,GAAG9G,IAAI,CAAC;MAC7B;MACA8G,WAAW,CAAC7E,KAAK,CAAC,GAAG;QACnB,GAAG6E,WAAW,CAAC7E,KAAK,CAAC;QACrBqB,QAAQ,EAAEuD;MACZ,CAAC;MACD,OAAOC,WAAW,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,oBAAoB,GAAI9E,KAAK,IAAK;IACtC4B,uBAAuB,CAAE7D,IAAI,IAAK;MAChC,MAAM+F,eAAe,GAAG,CAAC,GAAGnC,oBAAoB,CAAC;MACjDmC,eAAe,CAAC9D,KAAK,CAAC,CAACuB,KAAK,GAAG,CAACuC,eAAe,CAAC9D,KAAK,CAAC,CAACuB,KAAK;MAC5D,OAAOuC,eAAe;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,eAAe,GAAGrD,oBAAoB,CAACsD,MAAM,CACjD,CAACC,GAAG,EAAEpI,IAAI,KAAK;MACb;MACA,IAAI,CAACoI,GAAG,CAACC,QAAQ,CAACrI,IAAI,CAACgE,QAAQ,CAAC,EAAE;QAChCoE,GAAG,CAACC,QAAQ,CAACrI,IAAI,CAACgE,QAAQ,CAAC,GAAG,EAAE;MAClC;MACAoE,GAAG,CAACC,QAAQ,CAACrI,IAAI,CAACgE,QAAQ,CAAC,CAACsE,IAAI,CAACtI,IAAI,CAAC,CAAC,CAAC;MACxC,OAAOoI,GAAG;IACZ,CAAC,EACD;MAAE5K,IAAI,EAAE,CAAC,CAAC;MAAE6K,QAAQ,EAAE,CAAC;IAAE,CAC3B,CAAC;IACDH,eAAe,CAAC1K,IAAI,GAAG;MACrB+K,EAAE,EAAE/K,IAAI,CAAC8D,GAAG;MACZrB,IAAI,EAAEzC,IAAI,CAACyC,IAAI;MACfwE,KAAK,EAAEjH,IAAI,CAACiH,KAAK;MACjB5D,OAAO,EAAErD,IAAI,CAACqB,QAAQ;MACtBA,QAAQ,EAAErB,IAAI,CAACqB,QAAQ;MACvB2J,IAAI,EAAEhL,IAAI,CAACgL;IACb,CAAC;IACDN,eAAe,CAACO,GAAG,GAAG9C,KAAK;IAC3B,MAAM+C,GAAG,GAAG,oDAAoD;IAEhE,IAAI;MACF,MAAMjI,QAAQ,GAAG,MAAM7B,KAAK,CAAC8J,GAAG,EAAE;QAChCnH,MAAM,EAAE,MAAM;QAAE;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB,CAAE;QACtC,CAAC;QACDmH,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,eAAe,CAAC,CAAE;MACzC,CAAC,CAAC;MAEF,IAAIzH,QAAQ,CAACgB,EAAE,EAAE;QACf;QACA/F,MAAM,CAAC,CAAC,EAAE,kCAAkC,EAAE,eAAe,CAAC;QAC9D,MAAMoN,YAAY,GAAG,MAAMrI,QAAQ,CAAC1B,IAAI,CAAC,CAAC;QAC1CE,OAAO,CAACqB,GAAG,CAAC,gCAAgC,EAAEwI,YAAY,CAAC;QAC3D,MAAMnF,oBAAoB,CAACuD,OAAO,CAAC6B,UAAU,CAAC,EAAE,CAAC;QACjD,MAAMpL,aAAa,CAACuJ,OAAO,CAACrH,yBAAyB,CACnD,GAAG,EACH,yDAAyD,EACzD,CAAC,EACD,EACF,CAAC;QAED1C,iBAAiB,CAAC,EAAE,CAAC;QACrB2H,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAC,MAAM;QACLpJ,MAAM,CAAC,CAAC,EAAE,oCAAoC,EAAE,OAAO,CAAC;QACxD;QACAuD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEuB,QAAQ,CAACuI,UAAU,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO9J,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC;EACF,CAAC;EAED,oBACErD,OAAA,CAAAE,SAAA;IAAA+F,QAAA,gBACEjG,OAAA;MAAK0G,SAAS,EAAC,oBAAoB;MAAAT,QAAA,EAAC;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvDxG,OAAA;MAAK0G,SAAS,EAAC,sBAAsB;MAAAT,QAAA,gBACnCjG,OAAA;QAAK0G,SAAS,EAAC,qBAAqB;QAAAT,QAAA,eAClCjG,OAAA;UAAAiG,QAAA,gBACEjG,OAAA;YAAAiG,QAAA,eACEjG,OAAA;cAAAiG,QAAA,gBACEjG,OAAA;gBAAAiG,QAAA,EAAI;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZxG,OAAA;gBAAAiG,QAAA,EAAI;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBxG,OAAA;gBAAAiG,QAAA,EAAI;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxG,OAAA;gBAAAiG,QAAA,EAAI;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBxG,OAAA;gBAAAiG,QAAA,EAAI;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxG,OAAA;gBAAAiG,QAAA,EAAI;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBxG,OAAA;gBAAAiG,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfxG,OAAA;gBAAAiG,QAAA,EAAI;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfxG,OAAA;gBAAAiG,QAAA,EAAI;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxG,OAAA;YAAAiG,QAAA,EACG+C,oBAAoB,CAACxE,GAAG,CAAC,CAACqF,OAAO,EAAExC,KAAK,kBACvCrH,OAAA;cAAAiG,QAAA,gBACEjG,OAAA;gBAAAiG,QAAA,EAAKoB,KAAK,GAAG;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBACE2G,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfwG,cAAc,EAAE,QAAQ;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBAAApH,QAAA,eAEFjG,OAAA;oBACE0G,SAAS,EAAC,gCAAgC;oBAC1CC,KAAK,EAAE;sBAAEY,eAAe,EAAE,OAAOsC,OAAO,CAACrB,QAAQ;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,gBACEjG,OAAA;kBAAK0G,SAAS,EAAC,yBAAyB;kBAAAT,QAAA,EACrC4D,OAAO,CAACzF;gBAAI;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxG,OAAA;kBAAK0G,SAAS,EAAC,gCAAgC;kBAAAT,QAAA,EAC5C4D,OAAO,CAAC3B;gBAAW;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBAAK2G,KAAK,EAAE;oBAAE2G,QAAQ,EAAE;kBAAW,CAAE;kBAAArH,QAAA,EAClC4D,OAAO,CAAC1B;gBAAQ;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBAAK0G,SAAS,EAAC,UAAU;kBAAAT,QAAA,gBACvBjG,OAAA;oBACE0G,SAAS,EAAC,iBAAiB;oBAC3BY,OAAO,EAAEA,CAAA,KAAMsE,QAAQ,CAACvE,KAAK,CAAE;oBAAApB,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxG,OAAA;oBACE0D,KAAK,EAAEsF,oBAAoB,CAAC3B,KAAK,CAAC,CAACqB,QAAS;oBAC5ChC,SAAS,EAAC,gBAAgB;oBAC1BQ,QAAQ,EAAG8E,CAAC,IAAKD,oBAAoB,CAAC1E,KAAK,EAAE2E,CAAC;kBAAE;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACFxG,OAAA;oBACE0G,SAAS,EAAC,iBAAiB;oBAC3BY,OAAO,EAAEA,CAAA,KAAMoE,QAAQ,CAACrE,KAAK,CAAE;oBAAApB,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,GACG,CACC4D,OAAO,CAACxB,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAChCS,oBAAoB,CAAC3B,KAAK,CAAC,CAACqB,QAAQ,EACpC6E,cAAc,CAAC,CAAC,EAAE,GAAG,EAAC,KAE1B;cAAA;gBAAAlH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBACE0G,SAAS,EAAE,kBAAkBsC,oBAAoB,CAAC3B,KAAK,CAAC,CAACsB,MAAM,EAAG;kBAClErB,OAAO,EAAEA,CAAA,KAAM0D,iBAAiB,CAAC3D,KAAK,CAAE;kBACxCV,KAAK,EAAE;oBAAE2G,QAAQ,EAAE,UAAU;oBAAEE,MAAM,EAAE;kBAAU,CAAE;kBAAAvH,QAAA,GAElD4D,OAAO,CAAClB,MAAM,EACdQ,iBAAiB,KAAK9B,KAAK,iBAC1BrH,OAAA;oBAAKyN,GAAG,EAAE3C,WAAY;oBAACpE,SAAS,EAAC,UAAU;oBAAAT,QAAA,gBACzCjG,OAAA;sBACE0G,SAAS,EAAC,eAAe;sBACzBY,OAAO,EAAEA,CAAA,KAAM2D,kBAAkB,CAAC5D,KAAK,EAAE,SAAS,CAAE;sBAAApB,QAAA,EACrD;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNxG,OAAA;sBACE0G,SAAS,EAAC,gBAAgB;sBAC1BY,OAAO,EAAEA,CAAA,KACP2D,kBAAkB,CAAC5D,KAAK,EAAE,YAAY,CACvC;sBAAApB,QAAA,EACF;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNxG,OAAA;sBACE0G,SAAS,EAAC,gBAAgB;sBAC1BY,OAAO,EAAEA,CAAA,KACP2D,kBAAkB,CAAC5D,KAAK,EAAE,UAAU,CACrC;sBAAApB,QAAA,EACF;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBACEgH,IAAI,EAAC,UAAU;kBACf0G,OAAO,EAAE7D,OAAO,CAAChB,SAAU;kBAC3B3B,QAAQ,EAAEA,CAAA,KAAM2E,YAAY,CAACxE,KAAK,CAAE,CAAC;kBAAA;kBACrCqF,EAAE,EAAE,YAAYrF,KAAK;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLxG,OAAA;gBAAAiG,QAAA,eACEjG,OAAA;kBACEgH,IAAI,EAAC,UAAU;kBACf0G,OAAO,EAAE1E,oBAAoB,CAAC3B,KAAK,CAAC,CAACuB,KAAM;kBAC3C1B,QAAQ,EAAEA,CAAA,KAAMiF,oBAAoB,CAAC9E,KAAK;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GA1GEa,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2GV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNxG,OAAA;QAAK0G,SAAS,EAAC,WAAW;QAAAT,QAAA,GAAC,OACpB,EAAC,GAAG,eACTjG,OAAA;UACEgH,IAAI,EAAC,MAAM;UACXL,KAAK,EAAE;YACLgH,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE,gBAAgB;YACxBC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,MAAM;YACbC,SAAS,EAAE,OAAO;YAClBC,UAAU,EAAE,MAAM;YAClBC,YAAY,EAAE;UAChB,CAAE;UACFxK,KAAK,EAAEoG,KAAM;UACb1F,IAAI,EAAC,KAAK;UACV8C,QAAQ,EAAG8E,CAAC,IAAK;YACf,IAAI,OAAO,CAACmC,IAAI,CAACnC,CAAC,CAACvI,MAAM,CAACC,KAAK,CAAC,EAAE;cAChCqG,QAAQ,CAACiC,CAAC,CAACvI,MAAM,CAACC,KAAK,CAAC;YAC1B;UACF;QAAE;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFxG,OAAA;UAAM2G,KAAK,EAAE;YAAEmH,QAAQ,EAAE,EAAE;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAAnI,QAAA,GAAE,KAAK,EAAC,GAAC;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAC,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNxG,OAAA;QAAK0G,SAAS,EAAC,WAAW;QAAAT,QAAA,GAAC,sBACf,EAAC,GAAG,eACdjG,OAAA;UAAM2G,KAAK,EAAE;YAAEmH,QAAQ,EAAE,EAAE;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAAnI,QAAA,GAC5C,CAAEwE,UAAU,CAAC,CAAC,IAAIX,KAAK,GAAG,GAAG,CAAC,GAAI,GAAG,EACnCuE,OAAO,CAAC,CAAC,CAAC,CACVC,QAAQ,CAAC,CAAC,CACV/F,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC,EAAE,GAAG,EAAC,KAEhD;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxG,OAAA;QAAK0G,SAAS,EAAC,gBAAgB;QAAAT,QAAA,eAC7BjG,OAAA;UAAQsH,OAAO,EAAEA,CAAA,KAAM8E,YAAY,CAAC,CAAE;UAAAnG,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACwB,GAAA,CAxZIJ,YAAY;EAAA,QAsBsB9H,UAAU,EACtBF,OAAO;AAAA;AAAA2O,GAAA,GAvB7B3G,YAAY;AA0ZlB,eAAezH,OAAO;AAAC,IAAA4H,EAAA,EAAAwG,GAAA;AAAAC,YAAA,CAAAzG,EAAA;AAAAyG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}