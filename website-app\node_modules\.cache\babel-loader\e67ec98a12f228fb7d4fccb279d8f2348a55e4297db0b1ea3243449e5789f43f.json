{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\ManageAccount\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from \"react\";\nimport \"./ManageAccount.css\";\nimport { getRoles } from \"../../services/Roles/rolesService\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { useLoading } from \"../../components/introduce/Loading\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AccountTable() {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [rolesData, setRolesData] = useState([]);\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [showModal, setShowModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const dropdownRef = useRef(null);\n  const [confirmOtp, setConfirmOtp] = useState(false);\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    id: user ? user.id : \"\",\n    name: \"\",\n    email: \"\",\n    password: \"\",\n    role: \"\",\n    id_owner: user ? user.id_owner : \"\",\n    code: \"\"\n  });\n  const getAccounts = async userId => {\n    if (!userId) {\n      console.error(\"Lỗi: userId không hợp lệ!\");\n      return;\n    }\n    try {\n      const response = await fetch(`http://localhost:8080/api/accounts/show?userId=${userId}`, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      if (!response.ok) {\n        console.log(\"Response status:\", response.status);\n        throw new Error(`Network response was not ok: ${response.statusText}`);\n      }\n      const data = await response.json();\n      setAccounts(data);\n    } catch (error) {\n      console.error(\"Lỗi khi gọi API:\", error);\n    }\n  };\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setShowMenuIndex(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  useEffect(() => {\n    const fetchRoles = async () => {\n      if (user) {\n        startLoading();\n        await getAccounts(user.id_owner);\n        const roles = await getRoles(user.id_owner);\n        setRolesData(roles);\n        stopLoading();\n        setFormData(prevData => ({\n          ...prevData,\n          id_owner: user._id\n        })); // cập nhật id_owner\n      }\n    };\n    fetchRoles();\n  }, [user]);\n  const toggleMenu = index => {\n    setShowMenuIndex(showMenuIndex === index ? null : index);\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const filteredAccounts = accounts.filter(account => {\n    const name = account.name ? account.name.toLowerCase() : \"\";\n    const email = account.email ? account.email.toLowerCase() : \"\";\n    const role = account.role ? account.role.toLowerCase() : \"\";\n    return name.includes(searchTerm.toLowerCase()) || email.includes(searchTerm.toLowerCase()) || role.includes(searchTerm.toLowerCase());\n  });\n  const handleCreateAccount = async e => {\n    e.preventDefault();\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: formData.role,\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/accounts/create\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      console.log(data);\n      if (confirmOtp) {\n        if (data.message === \"Staff is created successfully\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\n        }\n      } else {\n        if (data.message === \"Confirmation code sent\") {\n          setConfirmOtp(true);\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n        } else if (data.message === \"User_new updated successfully!\") {\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\n          setFormData({\n            id: user ? user.id : \"\",\n            name: \"\",\n            email: \"\",\n            password: \"\",\n            role: \"\",\n            id_owner: user ? user.id_owner : \"\",\n            code: \"\"\n          });\n          setConfirmOtp(false);\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\n        } else {\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const sentAgain = async () => {\n    setConfirmOtp(true);\n    try {\n      const dataUser = {\n        id: user ? user.id : \"\",\n        role: user ? user.role : \"\",\n        id_owner: user ? user.id_owner : \"\",\n        email: formData.email,\n        password: formData.password,\n        name: formData.name,\n        confirmOtp: confirmOtp,\n        code: formData.code\n      };\n      startLoading();\n      const response = await fetch(\"http://localhost:8080/api/accounts/send_again\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          dataUser,\n          user\n        })\n      });\n      const data = await response.json();\n      stopLoading();\n      // Khi gửi mã xác nhận\n      if (data.message === \"Confirmation code sent\") {\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\n        setFormData(prev => ({\n          ...prev,\n          code: \"\"\n        }));\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\n      } else {\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const handleDeleteAccount = async accountId => {\n    if (window.confirm(\"Are you sure you want to delete this account?\")) {\n      try {\n        startLoading();\n        const response = await fetch(`http://localhost:8080/api/accounts/delete/${accountId}`, {\n          method: \"DELETE\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(user)\n        });\n        if (!response.ok) {\n          notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\n          throw new Error(`Failed to delete account: ${response.statusText}`);\n        }\n        if (user._id === accountId) {\n          logout();\n        }\n        await getAccounts(user.id_owner); // Refresh the accounts list\n        stopLoading();\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\n      } catch (error) {\n        notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\n        console.error(\"Error deleting account:\", error);\n        stopLoading();\n      }\n    }\n  };\n  const handleOpenEditModal = account => {\n    setFormData({\n      id: account._id,\n      name: account.name,\n      email: account.email,\n      role: account.role,\n      password: account.password // Assuming password can be left blank for editing\n    });\n    setShowEditModal(true);\n  };\n  const handleEditAccount = async e => {\n    e.preventDefault();\n    try {\n      startLoading();\n      const response = await fetch(`http://localhost:8080/api/accounts/edit/${formData.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      console.log(\"Success:\", data);\n      stopLoading();\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\n      await getAccounts(user.id_owner); // Use await here as handleCreateAccount is async\n      setShowModal(false); // Hide modal on success\n    } catch (error) {\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\n      console.error(\"Error edit:\", error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-table\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Qu\\u1EA3n l\\xED t\\xE0i kho\\u1EA3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"uy-search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"search-input\",\n          placeholder: \"Search for...\",\n          value: searchTerm,\n          onChange: handleSearchChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"create-order-btn\",\n          onClick: () => setShowModal(true),\n          children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleCreateAccount,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: \"10px\"\n            },\n            children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n nh\\xE2n vi\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), confirmOtp && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"code\",\n              placeholder: \"\\u0110i\\u1EC1n m\\xE3 x\\xE1c nh\\u1EADn \",\n              value: formData.code,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"uy-sentagain\",\n              onClick: sentAgain,\n              children: \"G\\u1EEDi l\\u1EA1i m\\xE3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: () => setShowEditModal(false),\n          children: \"\\u2716\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"create-account-form\",\n          onSubmit: handleEditAccount,\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Edit Staff Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Full Name\",\n            value: formData.name,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Password\",\n            value: formData.password,\n            onChange: handleInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"role\",\n            value: formData.role,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              disabled: true,\n              children: \"Select Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), rolesData.map(role => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: role.role,\n              children: role.role\n            }, role._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowEditModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\u1ECD T\\xEAn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ph\\xE2n Quy\\u1EC1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tr\\u1EA1ng Th\\xE1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"L\\u01B0\\u01A1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"H\\xE0nh \\u0110\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredAccounts.map(account => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${account.status ? account.status.toLowerCase() : \"active\"}`,\n              children: account.status || \"Acctive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: account.salary || \"N/A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uy-action\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleMenu(account._id),\n                className: \"menu-btn\",\n                children: \"\\u22EE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), showMenuIndex === account._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uy-dropdown-menu\",\n                ref: dropdownRef,\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleOpenEditModal(account),\n                    children: \"Ch\\u1EC9nh s\\u1EEDa\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: () => handleDeleteAccount(account._id),\n                    children: \"X\\xF3a\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, account._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        marginBottom: \"5px\",\n        color: \"red\"\n      },\n      children: \"L\\u01B0u \\xFD n\\u1EBFu s\\u1EEDa quy\\u1EC1n c\\u1EE7a Admin sang m\\u1ED9t quy\\u1EC1n kh\\xE1c m\\xE0 kh\\xF4ng bao g\\u1ED3m (\\\"*role\\\") b\\u1EA1n s\\u1EBD kh\\xF4ng th\\u1EC3 ph\\xE2n quy\\u1EC1n n\\u1EEFa\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"deleteAccountBtn\",\n      onClick: () => handleDeleteAccount(user._id),\n      children: \"X\\xF3a T\\xE0i Kho\\u1EA3n\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n}\n_s(AccountTable, \"Hv0BcuUoUoFpI5mjsX3mZ2iLEpc=\", false, function () {\n  return [useLoading, useAuth, useNavigate];\n});\n_c = AccountTable;\nexport default AccountTable;\nvar _c;\n$RefreshReg$(_c, \"AccountTable\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "getRoles", "useAuth", "useLoading", "notify", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AccountTable", "_s", "accounts", "setAccounts", "rolesData", "setRolesData", "startLoading", "stopLoading", "user", "logout", "showMenuIndex", "setShowMenuIndex", "searchTerm", "setSearchTerm", "showModal", "setShowModal", "showEditModal", "setShowEditModal", "dropdownRef", "confirmOtp", "setConfirmOtp", "navigate", "formData", "setFormData", "id", "name", "email", "password", "role", "id_owner", "code", "getAccounts", "userId", "console", "error", "response", "fetch", "method", "headers", "ok", "log", "status", "Error", "statusText", "data", "json", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "fetchRoles", "roles", "prevData", "_id", "toggleMenu", "index", "handleSearchChange", "e", "value", "filteredAccounts", "filter", "account", "toLowerCase", "includes", "handleCreateAccount", "preventDefault", "dataUser", "body", "JSON", "stringify", "message", "sentAgain", "prev", "handleDeleteAccount", "accountId", "window", "confirm", "handleOpenEditModal", "handleEditAccount", "handleInputChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "onClick", "onSubmit", "style", "marginBottom", "required", "disabled", "map", "salary", "ref", "color", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/ManageAccount/index.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\r\nimport \"./ManageAccount.css\";\r\nimport { getRoles } from \"../../services/Roles/rolesService\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { useLoading } from \"../../components/introduce/Loading\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nfunction AccountTable() {\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [rolesData, setRolesData] = useState([]);\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, logout } = useAuth();\r\n  const [showMenuIndex, setShowMenuIndex] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const dropdownRef = useRef(null);\r\n  const [confirmOtp, setConfirmOtp] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const [formData, setFormData] = useState({\r\n    id: user ? user.id : \"\",\r\n    name: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    role: \"\",\r\n    id_owner: user ? user.id_owner : \"\",\r\n    code: \"\",\r\n  });\r\n\r\n  const getAccounts = async (userId) => {\r\n    if (!userId) {\r\n      console.error(\"Lỗi: userId không hợp lệ!\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/accounts/show?userId=${userId}`,\r\n        {\r\n          method: \"GET\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        console.log(\"Response status:\", response.status);\r\n        throw new Error(`Network response was not ok: ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      setAccounts(data);\r\n    } catch (error) {\r\n      console.error(\"Lỗi khi gọi API:\", error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setShowMenuIndex(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchRoles = async () => {\r\n      if (user) {\r\n        startLoading();\r\n        await getAccounts(user.id_owner);\r\n        const roles = await getRoles(user.id_owner);\r\n        setRolesData(roles);\r\n        stopLoading();\r\n        setFormData((prevData) => ({ ...prevData, id_owner: user._id })); // cập nhật id_owner\r\n      }\r\n    };\r\n    fetchRoles();\r\n  }, [user]);\r\n\r\n  const toggleMenu = (index) => {\r\n    setShowMenuIndex(showMenuIndex === index ? null : index);\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  const filteredAccounts = accounts.filter((account) => {\r\n    const name = account.name ? account.name.toLowerCase() : \"\";\r\n    const email = account.email ? account.email.toLowerCase() : \"\";\r\n    const role = account.role ? account.role.toLowerCase() : \"\";\r\n\r\n    return (\r\n      name.includes(searchTerm.toLowerCase()) ||\r\n      email.includes(searchTerm.toLowerCase()) ||\r\n      role.includes(searchTerm.toLowerCase())\r\n    );\r\n  });\r\n\r\n  const handleCreateAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: formData.role,\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\r\n        \"http://localhost:8080/api/accounts/create\",\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({ dataUser, user }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      console.log(data);\r\n\r\n      if (confirmOtp) {\r\n        if (data.message === \"Staff is created successfully\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Lỗi xác nhận mã\", \"Thất bại\");\r\n        }\r\n      } else {\r\n        if (data.message === \"Confirmation code sent\") {\r\n          setConfirmOtp(true);\r\n          notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n        } else if (data.message === \"User_new updated successfully!\") {\r\n          notify(1, \"Tạo thành công tài khoản\", \"Thành công\");\r\n          setFormData({\r\n            id: user ? user.id : \"\",\r\n            name: \"\",\r\n            email: \"\",\r\n            password: \"\",\r\n            role: \"\",\r\n            id_owner: user ? user.id_owner : \"\",\r\n            code: \"\",\r\n          });\r\n          setConfirmOtp(false);\r\n          setShowModal(false); // Đóng modal khi tạo tài khoản thành công\r\n          await getAccounts(user.id_owner); // Cập nhật danh sách tài khoản\r\n        } else {\r\n          notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const sentAgain = async () => {\r\n    setConfirmOtp(true);\r\n    try {\r\n      const dataUser = {\r\n        id: user ? user.id : \"\",\r\n        role: user ? user.role : \"\",\r\n        id_owner: user ? user.id_owner : \"\",\r\n        email: formData.email,\r\n        password: formData.password,\r\n        name: formData.name,\r\n        confirmOtp: confirmOtp,\r\n        code: formData.code,\r\n      };\r\n      startLoading();\r\n      const response = await fetch(\r\n        \"http://localhost:8080/api/accounts/send_again\",\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({ dataUser, user }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      stopLoading();\r\n      // Khi gửi mã xác nhận\r\n      if (data.message === \"Confirmation code sent\") {\r\n        setConfirmOtp(true); // Chuyển sang trạng thái nhập mã xác nhận\r\n        setFormData((prev) => ({ ...prev, code: \"\" }));\r\n        notify(1, \"Mã xác nhận đã được gửi\", \"Thành công\");\r\n      } else {\r\n        notify(2, data.message || \"Không thể gửi mã xác nhận\", \"Thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleDeleteAccount = async (accountId) => {\r\n    if (window.confirm(\"Are you sure you want to delete this account?\")) {\r\n      try {\r\n        startLoading();\r\n        const response = await fetch(\r\n          `http://localhost:8080/api/accounts/delete/${accountId}`,\r\n          {\r\n            method: \"DELETE\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify(user),\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\r\n          throw new Error(`Failed to delete account: ${response.statusText}`);\r\n        }\r\n        if (user._id === accountId) {\r\n          logout();\r\n        }\r\n\r\n        await getAccounts(user.id_owner); // Refresh the accounts list\r\n        stopLoading();\r\n        notify(1, \"Xóa thành công tài khoản\", \"Thành công\");\r\n      } catch (error) {\r\n        notify(2, \"Xóa tài khoản thất bại\", \"Thất bại\");\r\n        console.error(\"Error deleting account:\", error);\r\n        stopLoading();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleOpenEditModal = (account) => {\r\n    setFormData({\r\n      id: account._id,\r\n      name: account.name,\r\n      email: account.email,\r\n      role: account.role,\r\n      password: account.password, // Assuming password can be left blank for editing\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const handleEditAccount = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      startLoading();\r\n      const response = await fetch(\r\n        `http://localhost:8080/api/accounts/edit/${formData.id}`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(formData),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n      console.log(\"Success:\", data);\r\n      stopLoading();\r\n      notify(1, \"Chỉnh sửa tài khoản thành công\", \"Thành công\");\r\n      await getAccounts(user.id_owner); // Use await here as handleCreateAccount is async\r\n      setShowModal(false); // Hide modal on success\r\n    } catch (error) {\r\n      notify(2, \"Chỉnh sửa tài khoản thất bại\", \"Thất bại\");\r\n      console.error(\"Error edit:\", error);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  return (\r\n    <div className=\"account-table\">\r\n      <div className=\"account-header\">\r\n        <h2>Quản lí tài khoản</h2>\r\n        <div className=\"uy-search-container\">\r\n          <input\r\n            type=\"text\"\r\n            className=\"search-input\"\r\n            placeholder=\"Search for...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n          />\r\n          <button\r\n            className=\"create-order-btn\"\r\n            onClick={() => setShowModal(true)}\r\n          >\r\n            Tạo tài khoản nhân viên\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {showModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button className=\"close-modal\" onClick={() => setShowModal(false)}>\r\n              ✖\r\n            </button>\r\n            <form\r\n              className=\"create-account-form\"\r\n              onSubmit={handleCreateAccount}\r\n            >\r\n              <h3 style={{ marginBottom: \"10px\" }}>Tạo tài khoản nhân viên</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n\r\n              {confirmOtp && (\r\n                <>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"code\"\r\n                    placeholder=\"Điền mã xác nhận \"\r\n                    value={formData.code}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                  />\r\n                  <p className=\"uy-sentagain\" onClick={sentAgain}>\r\n                    Gửi lại mã\r\n                  </p>\r\n                </>\r\n              )}\r\n\r\n              <button type=\"submit\">\r\n                {confirmOtp ? \"Xác minh và tạo tài khoản\" : \"Gửi mã OTP\"}\r\n              </button>\r\n              <button type=\"button\" onClick={() => setShowModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showEditModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"modal-content\">\r\n            <button\r\n              className=\"close-modal\"\r\n              onClick={() => setShowEditModal(false)}\r\n            >\r\n              ✖\r\n            </button>\r\n            <form className=\"create-account-form\" onSubmit={handleEditAccount}>\r\n              {\" \"}\r\n              {/* Changed class name here */}\r\n              <h3>Edit Staff Account</h3>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Full Name\"\r\n                value={formData.name}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                required\r\n              />\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                placeholder=\"Password\"\r\n                value={formData.password}\r\n                onChange={handleInputChange}\r\n              />\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\" disabled>\r\n                  Select Role\r\n                </option>\r\n                {rolesData.map((role) => (\r\n                  <option key={role._id} value={role.role}>\r\n                    {role.role}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <button type=\"submit\">Submit</button>\r\n              <button type=\"button\" onClick={() => setShowEditModal(false)}>\r\n                Cancel\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <table>\r\n        <thead>\r\n          <tr>\r\n            <th>Họ Tên</th>\r\n            <th>Phân Quyền</th>\r\n            <th>Email</th>\r\n            <th>Trạng Thái</th>\r\n            <th>Lương</th>\r\n            <th>Hành Động</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredAccounts.map((account) => (\r\n            <tr key={account._id}>\r\n              <td>{account.name}</td>\r\n              <td>{account.role}</td>\r\n              <td>{account.email}</td>\r\n              <td>\r\n                <span\r\n                  className={`status ${\r\n                    account.status ? account.status.toLowerCase() : \"active\"\r\n                  }`}\r\n                >\r\n                  {account.status || \"Acctive\"}\r\n                </span>\r\n              </td>\r\n              <td>{account.salary || \"N/A\"}</td>\r\n              <td>\r\n                <div className=\"uy-action\">\r\n                  <button\r\n                    onClick={() => toggleMenu(account._id)}\r\n                    className=\"menu-btn\"\r\n                  >\r\n                    ⋮\r\n                  </button>\r\n                  {showMenuIndex === account._id && (\r\n                    <div className=\"uy-dropdown-menu\" ref={dropdownRef}>\r\n                      <ul>\r\n                        <li onClick={() => handleOpenEditModal(account)}>\r\n                          Chỉnh sửa\r\n                        </li>\r\n                        <li onClick={() => handleDeleteAccount(account._id)}>\r\n                          Xóa\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      <p style={{ marginBottom: \"5px\", color: \"red\" }}>\r\n        Lưu ý nếu sửa quyền của Admin sang một quyền khác mà không bao gồm\r\n        (\"*role\") bạn sẽ không thể phân quyền nữa\r\n      </p>\r\n      <button\r\n        className=\"deleteAccountBtn\"\r\n        onClick={() => handleDeleteAccount(user._id)}\r\n      >\r\n        Xóa Tài Khoản\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AccountTable;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,MAAM,QAAQ,4CAA4C;AACnE,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM;IAAEgB,YAAY;IAAEC;EAAY,CAAC,GAAGd,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEe,IAAI;IAAEC;EAAO,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM4B,WAAW,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM+B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC;IACvCkC,EAAE,EAAEhB,IAAI,GAAGA,IAAI,CAACgB,EAAE,GAAG,EAAE;IACvBC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAErB,IAAI,GAAGA,IAAI,CAACqB,QAAQ,GAAG,EAAE;IACnCC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAOC,MAAM,IAAK;IACpC,IAAI,CAACA,MAAM,EAAE;MACXC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;MAC1C;IACF;IAEA,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kDAAkDJ,MAAM,EAAE,EAC1D;QACEK,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChBN,OAAO,CAACO,GAAG,CAAC,kBAAkB,EAAEL,QAAQ,CAACM,MAAM,CAAC;QAChD,MAAM,IAAIC,KAAK,CAAC,gCAAgCP,QAAQ,CAACQ,UAAU,EAAE,CAAC;MACxE;MAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAElC1C,WAAW,CAACyC,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC;EAED9C,SAAS,CAAC,MAAM;IACd,MAAM0D,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAI7B,WAAW,CAAC8B,OAAO,IAAI,CAAC9B,WAAW,CAAC8B,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEvC,gBAAgB,CAAC,IAAI,CAAC;MACxB;IACF,CAAC;IAEDwC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAE1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN1D,SAAS,CAAC,MAAM;IACd,MAAMkE,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI9C,IAAI,EAAE;QACRF,YAAY,CAAC,CAAC;QACd,MAAMyB,WAAW,CAACvB,IAAI,CAACqB,QAAQ,CAAC;QAChC,MAAM0B,KAAK,GAAG,MAAMhE,QAAQ,CAACiB,IAAI,CAACqB,QAAQ,CAAC;QAC3CxB,YAAY,CAACkD,KAAK,CAAC;QACnBhD,WAAW,CAAC,CAAC;QACbgB,WAAW,CAAEiC,QAAQ,KAAM;UAAE,GAAGA,QAAQ;UAAE3B,QAAQ,EAAErB,IAAI,CAACiD;QAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE;IACF,CAAC;IACDH,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC9C,IAAI,CAAC,CAAC;EAEV,MAAMkD,UAAU,GAAIC,KAAK,IAAK;IAC5BhD,gBAAgB,CAACD,aAAa,KAAKiD,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChChD,aAAa,CAACgD,CAAC,CAACX,MAAM,CAACY,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,gBAAgB,GAAG7D,QAAQ,CAAC8D,MAAM,CAAEC,OAAO,IAAK;IACpD,MAAMxC,IAAI,GAAGwC,OAAO,CAACxC,IAAI,GAAGwC,OAAO,CAACxC,IAAI,CAACyC,WAAW,CAAC,CAAC,GAAG,EAAE;IAC3D,MAAMxC,KAAK,GAAGuC,OAAO,CAACvC,KAAK,GAAGuC,OAAO,CAACvC,KAAK,CAACwC,WAAW,CAAC,CAAC,GAAG,EAAE;IAC9D,MAAMtC,IAAI,GAAGqC,OAAO,CAACrC,IAAI,GAAGqC,OAAO,CAACrC,IAAI,CAACsC,WAAW,CAAC,CAAC,GAAG,EAAE;IAE3D,OACEzC,IAAI,CAAC0C,QAAQ,CAACvD,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,IACvCxC,KAAK,CAACyC,QAAQ,CAACvD,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,IACxCtC,IAAI,CAACuC,QAAQ,CAACvD,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC;EAE3C,CAAC,CAAC;EAEF,MAAME,mBAAmB,GAAG,MAAOP,CAAC,IAAK;IACvCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG;QACf9C,EAAE,EAAEhB,IAAI,GAAGA,IAAI,CAACgB,EAAE,GAAG,EAAE;QACvBI,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBC,QAAQ,EAAErB,IAAI,GAAGA,IAAI,CAACqB,QAAQ,GAAG,EAAE;QACnCH,KAAK,EAAEJ,QAAQ,CAACI,KAAK;QACrBC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;QAC3BF,IAAI,EAAEH,QAAQ,CAACG,IAAI;QACnBN,UAAU,EAAEA,UAAU;QACtBW,IAAI,EAAER,QAAQ,CAACQ;MACjB,CAAC;MACDxB,YAAY,CAAC,CAAC;MACd,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,2CAA2C,EAC3C;QACEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEH,QAAQ;UAAE9D;QAAK,CAAC;MACzC,CACF,CAAC;MAED,MAAMoC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClCtC,WAAW,CAAC,CAAC;MACb0B,OAAO,CAACO,GAAG,CAACI,IAAI,CAAC;MAEjB,IAAIzB,UAAU,EAAE;QACd,IAAIyB,IAAI,CAAC8B,OAAO,KAAK,+BAA+B,EAAE;UACpDhF,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnD6B,WAAW,CAAC;YACVC,EAAE,EAAEhB,IAAI,GAAGA,IAAI,CAACgB,EAAE,GAAG,EAAE;YACvBC,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRC,QAAQ,EAAErB,IAAI,GAAGA,IAAI,CAACqB,QAAQ,GAAG,EAAE;YACnCC,IAAI,EAAE;UACR,CAAC,CAAC;UACFV,aAAa,CAAC,KAAK,CAAC;UACpBL,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAMgB,WAAW,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLnC,MAAM,CAAC,CAAC,EAAEkD,IAAI,CAAC8B,OAAO,IAAI,iBAAiB,EAAE,UAAU,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAI9B,IAAI,CAAC8B,OAAO,KAAK,wBAAwB,EAAE;UAC7CtD,aAAa,CAAC,IAAI,CAAC;UACnB1B,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;QACpD,CAAC,MAAM,IAAIkD,IAAI,CAAC8B,OAAO,KAAK,gCAAgC,EAAE;UAC5DhF,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;UACnD6B,WAAW,CAAC;YACVC,EAAE,EAAEhB,IAAI,GAAGA,IAAI,CAACgB,EAAE,GAAG,EAAE;YACvBC,IAAI,EAAE,EAAE;YACRC,KAAK,EAAE,EAAE;YACTC,QAAQ,EAAE,EAAE;YACZC,IAAI,EAAE,EAAE;YACRC,QAAQ,EAAErB,IAAI,GAAGA,IAAI,CAACqB,QAAQ,GAAG,EAAE;YACnCC,IAAI,EAAE;UACR,CAAC,CAAC;UACFV,aAAa,CAAC,KAAK,CAAC;UACpBL,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,MAAMgB,WAAW,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACLnC,MAAM,CAAC,CAAC,EAAEkD,IAAI,CAAC8B,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;QACpE;MACF;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMyC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BvD,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMkD,QAAQ,GAAG;QACf9C,EAAE,EAAEhB,IAAI,GAAGA,IAAI,CAACgB,EAAE,GAAG,EAAE;QACvBI,IAAI,EAAEpB,IAAI,GAAGA,IAAI,CAACoB,IAAI,GAAG,EAAE;QAC3BC,QAAQ,EAAErB,IAAI,GAAGA,IAAI,CAACqB,QAAQ,GAAG,EAAE;QACnCH,KAAK,EAAEJ,QAAQ,CAACI,KAAK;QACrBC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;QAC3BF,IAAI,EAAEH,QAAQ,CAACG,IAAI;QACnBN,UAAU,EAAEA,UAAU;QACtBW,IAAI,EAAER,QAAQ,CAACQ;MACjB,CAAC;MACDxB,YAAY,CAAC,CAAC;MACd,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+C,EAC/C;QACEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEH,QAAQ;UAAE9D;QAAK,CAAC;MACzC,CACF,CAAC;MAED,MAAMoC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClCtC,WAAW,CAAC,CAAC;MACb;MACA,IAAIqC,IAAI,CAAC8B,OAAO,KAAK,wBAAwB,EAAE;QAC7CtD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QACrBG,WAAW,CAAEqD,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE9C,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;QAC9CpC,MAAM,CAAC,CAAC,EAAE,yBAAyB,EAAE,YAAY,CAAC;MACpD,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC,EAAEkD,IAAI,CAAC8B,OAAO,IAAI,2BAA2B,EAAE,UAAU,CAAC;MACpE;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAM2C,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF1E,YAAY,CAAC,CAAC;QACd,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,6CAA6C0C,SAAS,EAAE,EACxD;UACEzC,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjE,IAAI;QAC3B,CACF,CAAC;QAED,IAAI,CAAC2B,QAAQ,CAACI,EAAE,EAAE;UAChB7C,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;UAC/C,MAAM,IAAIgD,KAAK,CAAC,6BAA6BP,QAAQ,CAACQ,UAAU,EAAE,CAAC;QACrE;QACA,IAAInC,IAAI,CAACiD,GAAG,KAAKqB,SAAS,EAAE;UAC1BrE,MAAM,CAAC,CAAC;QACV;QAEA,MAAMsB,WAAW,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;QAClCtB,WAAW,CAAC,CAAC;QACbb,MAAM,CAAC,CAAC,EAAE,0BAA0B,EAAE,YAAY,CAAC;MACrD,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdxC,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,UAAU,CAAC;QAC/CuC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C3B,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAM0E,mBAAmB,GAAIhB,OAAO,IAAK;IACvC1C,WAAW,CAAC;MACVC,EAAE,EAAEyC,OAAO,CAACR,GAAG;MACfhC,IAAI,EAAEwC,OAAO,CAACxC,IAAI;MAClBC,KAAK,EAAEuC,OAAO,CAACvC,KAAK;MACpBE,IAAI,EAAEqC,OAAO,CAACrC,IAAI;MAClBD,QAAQ,EAAEsC,OAAO,CAACtC,QAAQ,CAAE;IAC9B,CAAC,CAAC;IACFV,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMiE,iBAAiB,GAAG,MAAOrB,CAAC,IAAK;IACrCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF/D,YAAY,CAAC,CAAC;MACd,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,2CAA2Cd,QAAQ,CAACE,EAAE,EAAE,EACxD;QACEa,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnD,QAAQ;MAC/B,CACF,CAAC;MAED,MAAMsB,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClCZ,OAAO,CAACO,GAAG,CAAC,UAAU,EAAEI,IAAI,CAAC;MAC7BrC,WAAW,CAAC,CAAC;MACbb,MAAM,CAAC,CAAC,EAAE,gCAAgC,EAAE,YAAY,CAAC;MACzD,MAAMqC,WAAW,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;MAClCd,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdxC,MAAM,CAAC,CAAC,EAAE,8BAA8B,EAAE,UAAU,CAAC;MACrDuC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED,MAAMiD,iBAAiB,GAAItB,CAAC,IAAK;IAC/B,MAAM;MAAEpC,IAAI;MAAEqC;IAAM,CAAC,GAAGD,CAAC,CAACX,MAAM;IAChC3B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACG,IAAI,GAAGqC;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,oBACEjE,OAAA;IAAKuF,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxF,OAAA;MAAKuF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxF,OAAA;QAAAwF,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B5F,OAAA;QAAKuF,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCxF,OAAA;UACE6F,IAAI,EAAC,MAAM;UACXN,SAAS,EAAC,cAAc;UACxBO,WAAW,EAAC,eAAe;UAC3B7B,KAAK,EAAElD,UAAW;UAClBgF,QAAQ,EAAEhC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACF5F,OAAA;UACEuF,SAAS,EAAC,kBAAkB;UAC5BS,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,IAAI,CAAE;UAAAsE,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3E,SAAS,iBACRjB,OAAA;MAAKuF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxF,OAAA;QAAKuF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxF,OAAA;UAAQuF,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,KAAK,CAAE;UAAAsE,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5F,OAAA;UACEuF,SAAS,EAAC,qBAAqB;UAC/BU,QAAQ,EAAE1B,mBAAoB;UAAAiB,QAAA,gBAE9BxF,OAAA;YAAIkG,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE5F,OAAA;YACE6F,IAAI,EAAC,MAAM;YACXjE,IAAI,EAAC,MAAM;YACXkE,WAAW,EAAC,WAAW;YACvB7B,KAAK,EAAExC,QAAQ,CAACG,IAAK;YACrBmE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5F,OAAA;YACE6F,IAAI,EAAC,OAAO;YACZjE,IAAI,EAAC,OAAO;YACZkE,WAAW,EAAC,OAAO;YACnB7B,KAAK,EAAExC,QAAQ,CAACI,KAAM;YACtBkE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5F,OAAA;YACE6F,IAAI,EAAC,UAAU;YACfjE,IAAI,EAAC,UAAU;YACfkE,WAAW,EAAC,UAAU;YACtB7B,KAAK,EAAExC,QAAQ,CAACK,QAAS;YACzBiE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5F,OAAA;YACE4B,IAAI,EAAC,MAAM;YACXqC,KAAK,EAAExC,QAAQ,CAACM,IAAK;YACrBgE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAERxF,OAAA;cAAQiE,KAAK,EAAC,EAAE;cAACoC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRrF,SAAS,CAAC+F,GAAG,CAAEvE,IAAI,iBAClB/B,OAAA;cAAuBiE,KAAK,EAAElC,IAAI,CAACA,IAAK;cAAAyD,QAAA,EACrCzD,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC6B,GAAG;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAERtE,UAAU,iBACTtB,OAAA,CAAAE,SAAA;YAAAsF,QAAA,gBACExF,OAAA;cACE6F,IAAI,EAAC,MAAM;cACXjE,IAAI,EAAC,MAAM;cACXkE,WAAW,EAAC,wCAAmB;cAC/B7B,KAAK,EAAExC,QAAQ,CAACQ,IAAK;cACrB8D,QAAQ,EAAET,iBAAkB;cAC5Bc,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF5F,OAAA;cAAGuF,SAAS,EAAC,cAAc;cAACS,OAAO,EAAElB,SAAU;cAAAU,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,eACJ,CACH,eAED5F,OAAA;YAAQ6F,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAClBlE,UAAU,GAAG,2BAA2B,GAAG;UAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACT5F,OAAA;YAAQ6F,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,KAAK,CAAE;YAAAsE,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAzE,aAAa,iBACZnB,OAAA;MAAKuF,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BxF,OAAA;QAAKuF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxF,OAAA;UACEuF,SAAS,EAAC,aAAa;UACvBS,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAAC,KAAK,CAAE;UAAAoE,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5F,OAAA;UAAMuF,SAAS,EAAC,qBAAqB;UAACU,QAAQ,EAAEZ,iBAAkB;UAAAG,QAAA,GAC/D,GAAG,eAEJxF,OAAA;YAAAwF,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B5F,OAAA;YACE6F,IAAI,EAAC,MAAM;YACXjE,IAAI,EAAC,MAAM;YACXkE,WAAW,EAAC,WAAW;YACvB7B,KAAK,EAAExC,QAAQ,CAACG,IAAK;YACrBmE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5F,OAAA;YACE6F,IAAI,EAAC,OAAO;YACZjE,IAAI,EAAC,OAAO;YACZkE,WAAW,EAAC,OAAO;YACnB7B,KAAK,EAAExC,QAAQ,CAACI,KAAM;YACtBkE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5F,OAAA;YACE6F,IAAI,EAAC,UAAU;YACfjE,IAAI,EAAC,UAAU;YACfkE,WAAW,EAAC,UAAU;YACtB7B,KAAK,EAAExC,QAAQ,CAACK,QAAS;YACzBiE,QAAQ,EAAET;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF5F,OAAA;YACE4B,IAAI,EAAC,MAAM;YACXqC,KAAK,EAAExC,QAAQ,CAACM,IAAK;YACrBgE,QAAQ,EAAET,iBAAkB;YAC5Bc,QAAQ;YAAAZ,QAAA,gBAERxF,OAAA;cAAQiE,KAAK,EAAC,EAAE;cAACoC,QAAQ;cAAAb,QAAA,EAAC;YAE1B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRrF,SAAS,CAAC+F,GAAG,CAAEvE,IAAI,iBAClB/B,OAAA;cAAuBiE,KAAK,EAAElC,IAAI,CAACA,IAAK;cAAAyD,QAAA,EACrCzD,IAAI,CAACA;YAAI,GADCA,IAAI,CAAC6B,GAAG;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACT5F,OAAA;YAAQ6F,IAAI,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC5F,OAAA;YAAQ6F,IAAI,EAAC,QAAQ;YAACG,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAAC,KAAK,CAAE;YAAAoE,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5F,OAAA;MAAAwF,QAAA,gBACExF,OAAA;QAAAwF,QAAA,eACExF,OAAA;UAAAwF,QAAA,gBACExF,OAAA;YAAAwF,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5F,OAAA;YAAAwF,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB5F,OAAA;YAAAwF,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5F,OAAA;YAAAwF,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB5F,OAAA;YAAAwF,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5F,OAAA;YAAAwF,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5F,OAAA;QAAAwF,QAAA,EACGtB,gBAAgB,CAACoC,GAAG,CAAElC,OAAO,iBAC5BpE,OAAA;UAAAwF,QAAA,gBACExF,OAAA;YAAAwF,QAAA,EAAKpB,OAAO,CAACxC;UAAI;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5F,OAAA;YAAAwF,QAAA,EAAKpB,OAAO,CAACrC;UAAI;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5F,OAAA;YAAAwF,QAAA,EAAKpB,OAAO,CAACvC;UAAK;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB5F,OAAA;YAAAwF,QAAA,eACExF,OAAA;cACEuF,SAAS,EAAE,UACTnB,OAAO,CAACxB,MAAM,GAAGwB,OAAO,CAACxB,MAAM,CAACyB,WAAW,CAAC,CAAC,GAAG,QAAQ,EACvD;cAAAmB,QAAA,EAEFpB,OAAO,CAACxB,MAAM,IAAI;YAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACL5F,OAAA;YAAAwF,QAAA,EAAKpB,OAAO,CAACmC,MAAM,IAAI;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClC5F,OAAA;YAAAwF,QAAA,eACExF,OAAA;cAAKuF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxF,OAAA;gBACEgG,OAAO,EAAEA,CAAA,KAAMnC,UAAU,CAACO,OAAO,CAACR,GAAG,CAAE;gBACvC2B,SAAS,EAAC,UAAU;gBAAAC,QAAA,EACrB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACR/E,aAAa,KAAKuD,OAAO,CAACR,GAAG,iBAC5B5D,OAAA;gBAAKuF,SAAS,EAAC,kBAAkB;gBAACiB,GAAG,EAAEnF,WAAY;gBAAAmE,QAAA,eACjDxF,OAAA;kBAAAwF,QAAA,gBACExF,OAAA;oBAAIgG,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAAChB,OAAO,CAAE;oBAAAoB,QAAA,EAAC;kBAEjD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5F,OAAA;oBAAIgG,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACZ,OAAO,CAACR,GAAG,CAAE;oBAAA4B,QAAA,EAAC;kBAErD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAnCExB,OAAO,CAACR,GAAG;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoChB,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACR5F,OAAA;MAAGkG,KAAK,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEM,KAAK,EAAE;MAAM,CAAE;MAAAjB,QAAA,EAAC;IAGjD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACJ5F,OAAA;MACEuF,SAAS,EAAC,kBAAkB;MAC5BS,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACrE,IAAI,CAACiD,GAAG,CAAE;MAAA4B,QAAA,EAC9C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACxF,EAAA,CApgBQD,YAAY;EAAA,QAGmBP,UAAU,EACvBD,OAAO,EAOfG,WAAW;AAAA;AAAA4G,EAAA,GAXrBvG,YAAY;AAsgBrB,eAAeA,YAAY;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}