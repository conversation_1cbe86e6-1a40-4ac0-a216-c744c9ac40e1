{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\Manage_product\\\\Product_detail.js\",\n  _s = $RefreshSig$();\n// ProductDetail.js\nimport React, { useState, useRef, useEffect } from \"react\";\nimport \"../Manage_product/Product_detail.css\";\nimport { useLoading } from \"../introduce/Loading\";\nimport { useAuth } from \"../introduce/useAuth\";\nimport { notify } from \"../../components/Notification/notification\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetail = ({\n  product,\n  onClose,\n  onUpdate\n}) => {\n  _s();\n  const {\n    startLoading,\n    stopLoading\n  } = useLoading();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const CLOUD_NAME = \"ddgrjo6jr\";\n  const UPLOAD_PRESET = \"my-app\";\n  const [g, setg] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [editData, setEditData] = useState({\n    ...product\n  });\n  const [products, Setproduct] = useState(product);\n  const [details, Setdetails] = useState(\"\");\n  const [link, SetLink] = useState(product.image ? product.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\");\n  const [showCamera, setShowCamera] = useState(false);\n  const fileInputRef = useRef(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const streamRef = useRef(null);\n  const scrollableRef = useRef(null);\n  const [suppliers, setSuppliers] = useState([]); // state for suppliers list\n  useEffect(() => {\n    const fetchSuppliers = async () => {\n      let body = {\n        user: user\n      };\n      try {\n        let response = await fetch(\"http://localhost:8080/api/products/get_supplier\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify(body)\n        });\n        const data = await response.json();\n        console.log(data.suppliers);\n        setSuppliers(data.suppliers);\n      } catch (error) {\n        console.error(\"Error fetching suppliers:\", error);\n      }\n    };\n    fetchSuppliers();\n  }, []);\n  const scrollToTop = () => {\n    if (scrollableRef.current) {\n      scrollableRef.current.scrollTo({\n        top: 0,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const startCamera = async () => {\n    setShowCamera(true);\n    scrollToTop();\n    streamRef.current = await navigator.mediaDevices.getUserMedia({\n      video: true\n    });\n    videoRef.current.srcObject = streamRef.current;\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Xóa dấu phân tách cũ và chuyển thành số\n    const numericValue = Number(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\n\n    // Định dạng lại nếu là số hợp lệ\n    const formattedValue = !Number.isNaN(numericValue) ? numericValue.toLocaleString(\"vi-VN\") : value;\n\n    // Cập nhật formData với giá trị đã chuyển đổi\n    setEditData({\n      ...editData,\n      [name]: typeof formattedValue === \"string\" ? formattedValue.toLowerCase().replace(/,/g, \".\") : value.replace(/,/g, \".\")\n    });\n  };\n  const handleChange_link = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditData({\n      ...editData,\n      [name]: value\n    });\n    fileInputRef.current.value = \"\";\n    SetLink(value);\n  };\n  const handleChangedetail = e => {\n    const {\n      value\n    } = e.target;\n    Setdetails(value);\n  };\n  const handleEditToggle = () => {\n    setIsEditing(!isEditing);\n  };\n  const handleUpdate = async e => {\n    e.preventDefault();\n    let x = {\n      ...editData\n    };\n    if (editData.image != product.image && editData.image) {\n      const imageData = new FormData();\n      imageData.append(\"file\", editData.image);\n      imageData.append(\"upload_preset\", UPLOAD_PRESET);\n      try {\n        startLoading();\n        const cloudinaryResponse = await fetch(`https://api.cloudinary.com/v1_1/${CLOUD_NAME}/upload`, {\n          method: \"POST\",\n          body: imageData // Gửi FormData trực tiếp mà không cần JSON.stringify\n        });\n        const data = await cloudinaryResponse.json();\n        const secure_url = data.secure_url;\n        const public_id = data.public_id;\n        x = {\n          ...x,\n          image: {\n            secure_url,\n            public_id\n          } // Thêm thông tin hình ảnh\n        };\n      } catch (error) {\n        console.error(\"Error uploading image:\", error);\n        notify(2, \"Đã xảy ra lỗi khi tải lên hình ảnh.\", \"Thất bại\");\n      }\n    }\n    onUpdate(x, details, editData.image != product.image && editData.image);\n    Setproduct(editData);\n    setIsEditing(false);\n  };\n  const captureImage = () => {\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext(\"2d\");\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n    const imageUrl = canvas.toDataURL(\"image/png\");\n    SetLink(imageUrl);\n    if (streamRef.current) {\n      const tracks = streamRef.current.getTracks();\n      tracks.forEach(track => track.stop()); // Dừng từng track trong stream\n      videoRef.current.srcObject = null; // Gán srcObject về null\n      streamRef.current = null; // Đặt lại tham chiếu stream\n    }\n    setShowCamera(false); // Đóng camera sau khi chụp\n    // Tạo một file blob từ imageUrl và đặt vào input file\n    fetch(imageUrl).then(res => res.blob()).then(blob => {\n      const file = new File([blob], \"capture.png\", {\n        type: \"image/png\"\n      });\n      const dataTransfer = new DataTransfer();\n      dataTransfer.items.add(file);\n      fileInputRef.current.files = dataTransfer.files;\n      setEditData(prevData => ({\n        ...prevData,\n        image: file // Lưu trữ file vào state\n      }));\n    });\n  };\n  const handleChangeimage = e => {\n    setEditData({\n      ...editData,\n      image: e.target.files[0]\n    });\n    const imageUrl = URL.createObjectURL(e.target.files[0]);\n    console.log(\"Link ảnh đã được cập nhật:\", imageUrl);\n    SetLink(imageUrl); // Cập nhật link với URL ngắn hơn\n  };\n  const stopCamera = () => {\n    if (streamRef.current) {\n      const tracks = streamRef.current.getTracks();\n      tracks.forEach(track => track.stop()); // Dừng từng track trong stream\n      videoRef.current.srcObject = null; // Gán srcObject về null\n      streamRef.current = null; // Đặt lại tham chiếu stream\n    }\n    setShowCamera(false); // Đóng modal hoặc ẩn camera\n  };\n  const handleNChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setEditData({\n      ...editData,\n      [name]: value.toLowerCase()\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-detail-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-detail-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"close-button\",\n        onClick: onClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), !isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-info-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\xEAn:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Lo\\u1EA1i:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Th\\u01B0\\u01A1ng hi\\u1EC7u:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.brand\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"M\\xE3:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.sku\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Gi\\xE1 b\\xE1n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"$\", products.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng tr\\xEAn k\\u1EC7:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.stock_in_shelf\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"M\\u1EE9c \\u0111\\u1ED9 c\\u1EA7n \\u0111\\u01B0\\u1EE3c nh\\u1EADp h\\xE0ng:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.reorderLevel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Nh\\xE0 cung c\\u1EA5p:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.supplier ? products.supplier.name : \"nhà cung cấp của sản phầm này  đã bị xóa vui lòng hãy thêm nhà cung cấp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Ng\\xE0y nh\\u1EADp:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: new Date(products.purchaseDate).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"V\\u1ECB tr\\xED:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng trong kho h\\xE0ng:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.stock_in_Warehouse\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u0110\\u01A1n v\\u1ECB:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.unit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Gi\\xE1 nh\\u1EADp:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"$\", products.purchasePrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Notes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Link \\u1EA3nh:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: products.image ? products.image.secure_url : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: products.image ? products.image.secure_url : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\",\n          alt: \"Product Image\",\n          className: \"product-image-show\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"edit-button-detail\",\n          onClick: handleEditToggle,\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-edit-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Edit Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleUpdate,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              children: \"T\\xEAn *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"name\",\n              name: \"name\",\n              value: editData.name,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: \"Lo\\u1EA1i *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"category\",\n              name: \"category\",\n              value: editData.category,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"brand\",\n              children: \"Th\\u01B0\\u01A1ng hi\\u1EC7u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"brand\",\n              name: \"brand\",\n              value: editData.brand,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sku\",\n              children: \"M\\xE3 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"sku\",\n              name: \"sku\",\n              value: editData.sku,\n              onChange: handleNChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"price\",\n              children: \"Gi\\xE1 b\\xE1n *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"price\",\n              name: \"price\",\n              value: editData.price,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"purchasePrice\",\n              children: \"Gi\\xE1 nh\\u1EADp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"purchasePrice\",\n              name: \"purchasePrice\",\n              value: editData.purchasePrice,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stock_in_shelf\",\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng tr\\xEAn k\\u1EC7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"stock_in_shelf\",\n              name: \"stock_in_shelf\",\n              value: editData.stock_in_shelf,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"reorderLevel\",\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng c\\u1EA7n \\u0111\\u01B0\\u1EE3c nh\\u1EADp h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"reorderLevel\",\n              name: \"reorderLevel\",\n              value: editData.reorderLevel,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"supplier\",\n              children: \"Nh\\xE0 cung c\\u1EA5p\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"supplier\",\n              name: \"supplier\",\n              value: editData.supplier ? editData.supplier._id : \"\",\n              onChange: handleNChange,\n              children: suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: supplier._id,\n                children: supplier.name\n              }, supplier._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"purchaseDate\",\n              children: \"Ng\\xE0y nh\\u1EADp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"purchaseDate\",\n              name: \"purchaseDate\",\n              value: editData.purchaseDate,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"location\",\n              children: \"V\\u1ECB tr\\xED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"location\",\n              name: \"location\",\n              value: editData.location,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stock_in_Warehouse\",\n              children: \"S\\u1ED1 l\\u01B0\\u1EE3ng trong kho h\\xE0ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"stock_in_Warehouse\",\n              name: \"stock_in_Warehouse\",\n              value: editData.stock_in_Warehouse,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"unit\",\n              children: \"\\u0111\\u01A1n v\\u1ECB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"unit\",\n              name: \"unit\",\n              value: editData.unit,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"notes\",\n              children: \"Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"notes\",\n              name: \"notes\",\n              value: editData.notes,\n              onChange: handleNChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"image\",\n              children: \"Image:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: link,\n              className: \"product-image-show\",\n              alt: \"Product Image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"change_image\",\n              onClick: () => {\n                setg(x => {\n                  return !x;\n                });\n              },\n              children: \"Thay \\u0111\\u1ED5i \\u1EA3nh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), g && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                children: \"Image (3 c\\xE1ch \\u0111\\u1EC3 nh\\u1EADp \\u1EA3nh)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  marginBottom: \"3px\"\n                },\n                children: \"1. t\\u1EA3i \\u1EA3nh l\\xEAn t\\u1EEB m\\xE1y\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                ref: fileInputRef,\n                name: \"image\",\n                onChange: handleChangeimage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  marginBottom: \"3px\",\n                  marginTop: \"3px\"\n                },\n                children: \"2. link \\u1EA3nh tr\\xEAn m\\u1EA1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"image\",\n                name: \"image\",\n                value: editData.image,\n                onChange: handleChange_link\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  marginBottom: \"3px\",\n                  marginTop: \"3px\"\n                },\n                children: \"3. ch\\u1EE5p \\u1EA3nh tr\\u1EF1c ti\\u1EBFp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"capture\",\n                onClick: startCamera,\n                children: \"Ch\\u1EE5p \\u1EA3nh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this), showCamera && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"camera-modal\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"camera-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: videoRef,\n                    autoPlay: true,\n                    style: {\n                      width: \"100%\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"button-capture\",\n                    onClick: captureImage,\n                    children: \"Ch\\u1EE5p\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"button-capture\",\n                    onClick: stopCamera,\n                    children: \"H\\u1EE7y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                  display: \"none\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"detail\",\n              children: \"Th\\xF4ng tin chi ti\\u1EBFt thay \\u0111\\u1ED5i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"detail\",\n              name: \"detail\",\n              value: details,\n              onChange: handleChangedetail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"submit-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"save-button\",\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"cancel-button\",\n              onClick: handleEditToggle,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetail, \"UDPQfrMCPfjlj4wKBFpR6100CK8=\", false, function () {\n  return [useLoading, useAuth];\n});\n_c = ProductDetail;\nexport default ProductDetail;\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useLoading", "useAuth", "notify", "jsxDEV", "_jsxDEV", "ProductDetail", "product", "onClose", "onUpdate", "_s", "startLoading", "stopLoading", "user", "loading", "CLOUD_NAME", "UPLOAD_PRESET", "g", "setg", "isEditing", "setIsEditing", "editData", "setEditData", "products", "Setproduct", "details", "Setdetails", "link", "SetLink", "image", "secure_url", "showCamera", "setShowCamera", "fileInputRef", "videoRef", "canvasRef", "streamRef", "scrollableRef", "suppliers", "setSuppliers", "fetchSuppliers", "body", "response", "fetch", "method", "headers", "JSON", "stringify", "data", "json", "console", "log", "error", "scrollToTop", "current", "scrollTo", "top", "behavior", "startCamera", "navigator", "mediaDevices", "getUserMedia", "video", "srcObject", "handleChange", "e", "name", "value", "target", "numericValue", "Number", "replace", "formattedValue", "isNaN", "toLocaleString", "toLowerCase", "handleChange_link", "handleChangedetail", "handleEditToggle", "handleUpdate", "preventDefault", "x", "imageData", "FormData", "append", "cloudinaryResponse", "public_id", "captureImage", "canvas", "context", "getContext", "width", "videoWidth", "height", "videoHeight", "drawImage", "imageUrl", "toDataURL", "tracks", "getTracks", "for<PERSON>ach", "track", "stop", "then", "res", "blob", "file", "File", "type", "dataTransfer", "DataTransfer", "items", "add", "files", "prevData", "handleChangeimage", "URL", "createObjectURL", "stopCamera", "handleNChange", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category", "brand", "sku", "price", "stock_in_shelf", "reorderLevel", "supplier", "Date", "purchaseDate", "toLocaleDateString", "location", "stock_in_Warehouse", "unit", "purchasePrice", "notes", "src", "alt", "onSubmit", "htmlFor", "id", "onChange", "required", "_id", "map", "style", "marginBottom", "ref", "marginTop", "autoPlay", "display", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/Manage_product/Product_detail.js"], "sourcesContent": ["// ProductDetail.js\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport \"../Manage_product/Product_detail.css\";\r\nimport { useLoading } from \"../introduce/Loading\";\r\nimport { useAuth } from \"../introduce/useAuth\";\r\nimport { notify } from \"../../components/Notification/notification\";\r\nconst ProductDetail = ({ product, onClose, onUpdate }) => {\r\n  const { startLoading, stopLoading } = useLoading();\r\n  const { user, loading } = useAuth();\r\n  const CLOUD_NAME = \"ddgrjo6jr\";\r\n  const UPLOAD_PRESET = \"my-app\";\r\n  const [g, setg] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [editData, setEditData] = useState({ ...product });\r\n  const [products, Setproduct] = useState(product);\r\n  const [details, Setdetails] = useState(\"\");\r\n  const [link, SetLink] = useState(\r\n    product.image\r\n      ? product.image.secure_url\r\n      : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n  );\r\n  const [showCamera, setShowCamera] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n  const videoRef = useRef(null);\r\n  const canvasRef = useRef(null);\r\n  const streamRef = useRef(null);\r\n  const scrollableRef = useRef(null);\r\n  const [suppliers, setSuppliers] = useState([]); // state for suppliers list\r\n  useEffect(() => {\r\n    const fetchSuppliers = async () => {\r\n      let body = {\r\n        user: user,\r\n      };\r\n      try {\r\n        let response = await fetch(\r\n          \"http://localhost:8080/api/products/get_supplier\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify(body),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        console.log(data.suppliers);\r\n        setSuppliers(data.suppliers);\r\n      } catch (error) {\r\n        console.error(\"Error fetching suppliers:\", error);\r\n      }\r\n    };\r\n    fetchSuppliers();\r\n  }, []);\r\n  const scrollToTop = () => {\r\n    if (scrollableRef.current) {\r\n      scrollableRef.current.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    }\r\n  };\r\n  const startCamera = async () => {\r\n    setShowCamera(true);\r\n    scrollToTop();\r\n    streamRef.current = await navigator.mediaDevices.getUserMedia({\r\n      video: true,\r\n    });\r\n    videoRef.current.srcObject = streamRef.current;\r\n  };\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Xóa dấu phân tách cũ và chuyển thành số\r\n    const numericValue = Number(value.replace(/,/g, \"\").replace(/\\./g, \"\"));\r\n\r\n    // Định dạng lại nếu là số hợp lệ\r\n    const formattedValue = !Number.isNaN(numericValue)\r\n      ? numericValue.toLocaleString(\"vi-VN\")\r\n      : value;\r\n\r\n    // Cập nhật formData với giá trị đã chuyển đổi\r\n    setEditData({\r\n      ...editData,\r\n      [name]:\r\n        typeof formattedValue === \"string\"\r\n          ? formattedValue.toLowerCase().replace(/,/g, \".\")\r\n          : value.replace(/,/g, \".\"),\r\n    });\r\n  };\r\n\r\n  const handleChange_link = (e) => {\r\n    const { name, value } = e.target;\r\n    setEditData({\r\n      ...editData,\r\n      [name]: value,\r\n    });\r\n    fileInputRef.current.value = \"\";\r\n    SetLink(value);\r\n  };\r\n  const handleChangedetail = (e) => {\r\n    const { value } = e.target;\r\n    Setdetails(value);\r\n  };\r\n  const handleEditToggle = () => {\r\n    setIsEditing(!isEditing);\r\n  };\r\n\r\n  const handleUpdate = async (e) => {\r\n    e.preventDefault();\r\n    let x = { ...editData };\r\n    if (editData.image != product.image && editData.image) {\r\n      const imageData = new FormData();\r\n      imageData.append(\"file\", editData.image);\r\n      imageData.append(\"upload_preset\", UPLOAD_PRESET);\r\n      try {\r\n        startLoading();\r\n        const cloudinaryResponse = await fetch(\r\n          `https://api.cloudinary.com/v1_1/${CLOUD_NAME}/upload`,\r\n          {\r\n            method: \"POST\",\r\n            body: imageData, // Gửi FormData trực tiếp mà không cần JSON.stringify\r\n          }\r\n        );\r\n        const data = await cloudinaryResponse.json();\r\n        const secure_url = data.secure_url;\r\n        const public_id = data.public_id;\r\n        x = {\r\n          ...x,\r\n          image: { secure_url, public_id }, // Thêm thông tin hình ảnh\r\n        };\r\n      } catch (error) {\r\n        console.error(\"Error uploading image:\", error);\r\n        notify(2, \"Đã xảy ra lỗi khi tải lên hình ảnh.\", \"Thất bại\");\r\n      }\r\n    }\r\n    onUpdate(x, details, editData.image != product.image && editData.image);\r\n    Setproduct(editData);\r\n    setIsEditing(false);\r\n  };\r\n  const captureImage = () => {\r\n    const video = videoRef.current;\r\n    const canvas = canvasRef.current;\r\n    const context = canvas.getContext(\"2d\");\r\n    canvas.width = video.videoWidth;\r\n    canvas.height = video.videoHeight;\r\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\r\n    const imageUrl = canvas.toDataURL(\"image/png\");\r\n    SetLink(imageUrl);\r\n    if (streamRef.current) {\r\n      const tracks = streamRef.current.getTracks();\r\n      tracks.forEach((track) => track.stop()); // Dừng từng track trong stream\r\n      videoRef.current.srcObject = null; // Gán srcObject về null\r\n      streamRef.current = null; // Đặt lại tham chiếu stream\r\n    }\r\n    setShowCamera(false); // Đóng camera sau khi chụp\r\n    // Tạo một file blob từ imageUrl và đặt vào input file\r\n    fetch(imageUrl)\r\n      .then((res) => res.blob())\r\n      .then((blob) => {\r\n        const file = new File([blob], \"capture.png\", { type: \"image/png\" });\r\n        const dataTransfer = new DataTransfer();\r\n        dataTransfer.items.add(file);\r\n        fileInputRef.current.files = dataTransfer.files;\r\n        setEditData((prevData) => ({\r\n          ...prevData,\r\n          image: file, // Lưu trữ file vào state\r\n        }));\r\n      });\r\n  };\r\n  const handleChangeimage = (e) => {\r\n    setEditData({\r\n      ...editData,\r\n      image: e.target.files[0],\r\n    });\r\n    const imageUrl = URL.createObjectURL(e.target.files[0]);\r\n    console.log(\"Link ảnh đã được cập nhật:\", imageUrl);\r\n    SetLink(imageUrl); // Cập nhật link với URL ngắn hơn\r\n  };\r\n  const stopCamera = () => {\r\n    if (streamRef.current) {\r\n      const tracks = streamRef.current.getTracks();\r\n      tracks.forEach((track) => track.stop()); // Dừng từng track trong stream\r\n      videoRef.current.srcObject = null; // Gán srcObject về null\r\n      streamRef.current = null; // Đặt lại tham chiếu stream\r\n    }\r\n    setShowCamera(false); // Đóng modal hoặc ẩn camera\r\n  };\r\n  const handleNChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setEditData({\r\n      ...editData,\r\n      [name]: value.toLowerCase(),\r\n    });\r\n  };\r\n  return (\r\n    <div className=\"product-detail-overlay\">\r\n      <div className=\"product-detail-container\">\r\n        <span className=\"close-button\" onClick={onClose}>\r\n          &times;\r\n        </span>\r\n        {!isEditing ? (\r\n          <div className=\"product-info\">\r\n            {/* <h2 style={{ whiteSpace: \"wrap\" }}>Tên : {products.name}</h2>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Loại:</strong> {products.category}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Thương hiệu:</strong> {products.brand}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Mã:</strong> {products.sku}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Giá bán:</strong> ${products.price}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>số lượng trên kệ:</strong> {products.stock_in_shelf}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Mức độ cần được nhập hàng:</strong>{\" \"}\r\n              {products.reorderLevel}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Nhà cung cấp:</strong> {products.supplier.name}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Ngày nhập:</strong>{\" \"}\r\n              {new Date(products.purchaseDate).toLocaleDateString()}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Vị trí:</strong> {products.location}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Số lượng trong kho hàng:</strong>{\" \"}\r\n              {products.stock_in_Warehouse}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Đơn vị:</strong> {products.unit}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Giá nhập:</strong> ${products.purchasePrice}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Notes:</strong> {products.notes}\r\n            </p>\r\n            <p style={{ whiteSpace: \"normal\", overflowWrap: \"break-word\" }}>\r\n              <strong>Link ảnh :</strong>{\" \"}\r\n              {products.image ? products.image.secure_url : \"\"}\r\n            </p> */}\r\n            <div className=\"product-info-details\">\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Tên:</strong>\r\n                <span>{products.name}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Loại:</strong>\r\n                <span>{products.category}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Thương hiệu:</strong>\r\n                <span>{products.brand}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Mã:</strong>\r\n                <span>{products.sku}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Giá bán:</strong>\r\n                <span>${products.price}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Số lượng trên kệ:</strong>\r\n                <span>{products.stock_in_shelf}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Mức độ cần được nhập hàng:</strong>\r\n                <span>{products.reorderLevel}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Nhà cung cấp:</strong>\r\n                <span>\r\n                  {products.supplier\r\n                    ? products.supplier.name\r\n                    : \"nhà cung cấp của sản phầm này  đã bị xóa vui lòng hãy thêm nhà cung cấp\"}\r\n                </span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Ngày nhập:</strong>\r\n                <span>\r\n                  {new Date(products.purchaseDate).toLocaleDateString()}\r\n                </span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Vị trí:</strong>\r\n                <span>{products.location}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Số lượng trong kho hàng:</strong>\r\n                <span>{products.stock_in_Warehouse}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Đơn vị:</strong>\r\n                <span>{products.unit}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Giá nhập:</strong>\r\n                <span>${products.purchasePrice}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Notes:</strong>\r\n                <span>{products.notes}</span>\r\n              </div>\r\n              <div className=\"product-info-details-row\">\r\n                <strong>Link ảnh:</strong>\r\n                <span>{products.image ? products.image.secure_url : \"\"}</span>\r\n              </div>\r\n            </div>\r\n            <img\r\n              src={\r\n                products.image\r\n                  ? products.image.secure_url\r\n                  : \"https://www.shutterstock.com/shutterstock/photos/600304136/display_1500/stock-vector-full-basket-of-food-grocery-shopping-special-offer-vector-line-icon-design-600304136.jpg\"\r\n              }\r\n              alt=\"Product Image\"\r\n              className=\"product-image-show\"\r\n            />\r\n            <br></br>\r\n\r\n            <button className=\"edit-button-detail\" onClick={handleEditToggle}>\r\n              Edit\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"product-edit-form\">\r\n            <h2>Edit Product</h2>\r\n            <form onSubmit={handleUpdate}>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"name\">Tên *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={editData.name}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"category\">Loại *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"category\"\r\n                  name=\"category\"\r\n                  value={editData.category}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"brand\">Thương hiệu</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"brand\"\r\n                  name=\"brand\"\r\n                  value={editData.brand}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"sku\">Mã *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"sku\"\r\n                  name=\"sku\"\r\n                  value={editData.sku}\r\n                  onChange={handleNChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"price\">Giá bán *</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"price\"\r\n                  name=\"price\"\r\n                  value={editData.price}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"purchasePrice\">Giá nhập</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"purchasePrice\"\r\n                  name=\"purchasePrice\"\r\n                  value={editData.purchasePrice}\r\n                  onChange={handleChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"stock_in_shelf\">Số lượng trên kệ</label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"stock_in_shelf\"\r\n                  name=\"stock_in_shelf\"\r\n                  value={editData.stock_in_shelf}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"reorderLevel\">\r\n                  Số lượng cần được nhập hàng\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"reorderLevel\"\r\n                  name=\"reorderLevel\"\r\n                  value={editData.reorderLevel}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"supplier\">Nhà cung cấp</label>\r\n                <select\r\n                  id=\"supplier\"\r\n                  name=\"supplier\"\r\n                  value={editData.supplier ? editData.supplier._id : \"\"}\r\n                  onChange={handleNChange}\r\n                >\r\n                  {suppliers.map((supplier) => (\r\n                    <option key={supplier._id} value={supplier._id}>\r\n                      {supplier.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"purchaseDate\">Ngày nhập</label>\r\n                <input\r\n                  type=\"date\"\r\n                  id=\"purchaseDate\"\r\n                  name=\"purchaseDate\"\r\n                  value={editData.purchaseDate}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"location\">Vị trí</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"location\"\r\n                  name=\"location\"\r\n                  value={editData.location}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"stock_in_Warehouse\">\r\n                  Số lượng trong kho hàng\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"stock_in_Warehouse\"\r\n                  name=\"stock_in_Warehouse\"\r\n                  value={editData.stock_in_Warehouse}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"unit\">đơn vị</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"unit\"\r\n                  name=\"unit\"\r\n                  value={editData.unit}\r\n                  onChange={handleNChange}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"notes\">Notes</label>\r\n                <textarea\r\n                  id=\"notes\"\r\n                  name=\"notes\"\r\n                  value={editData.notes}\r\n                  onChange={handleNChange}\r\n                ></textarea>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"image\">Image:</label>\r\n                <img\r\n                  src={link}\r\n                  className=\"product-image-show\"\r\n                  alt=\"Product Image\"\r\n                />\r\n                <div\r\n                  className=\"change_image\"\r\n                  onClick={() => {\r\n                    setg((x) => {\r\n                      return !x;\r\n                    });\r\n                  }}\r\n                >\r\n                  Thay đổi ảnh\r\n                </div>\r\n                {g && (\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"image\">Image (3 cách để nhập ảnh)</label>\r\n                    <p style={{ marginBottom: \"3px\" }}>1. tải ảnh lên từ máy</p>\r\n                    <input\r\n                      type=\"file\"\r\n                      ref={fileInputRef}\r\n                      name=\"image\"\r\n                      onChange={handleChangeimage}\r\n                    />\r\n                    <p style={{ marginBottom: \"3px\", marginTop: \"3px\" }}>\r\n                      2. link ảnh trên mạng\r\n                    </p>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"image\"\r\n                      name=\"image\"\r\n                      value={editData.image}\r\n                      onChange={handleChange_link}\r\n                    />\r\n                    <p style={{ marginBottom: \"3px\", marginTop: \"3px\" }}>\r\n                      3. chụp ảnh trực tiếp\r\n                    </p>\r\n                    <div className=\"capture\" onClick={startCamera}>\r\n                      Chụp ảnh\r\n                    </div>\r\n\r\n                    {/* Modal hiển thị camera */}\r\n                    {showCamera && (\r\n                      <div className=\"camera-modal\">\r\n                        <div className=\"camera-container\">\r\n                          <video\r\n                            ref={videoRef}\r\n                            autoPlay\r\n                            style={{ width: \"100%\" }}\r\n                          />\r\n                          <button\r\n                            className=\"button-capture\"\r\n                            onClick={captureImage}\r\n                          >\r\n                            Chụp\r\n                          </button>\r\n                          <button\r\n                            className=\"button-capture\"\r\n                            onClick={stopCamera}\r\n                          >\r\n                            Hủy\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"detail\">Thông tin chi tiết thay đổi</label>\r\n                <textarea\r\n                  id=\"detail\"\r\n                  name=\"detail\"\r\n                  value={details}\r\n                  onChange={handleChangedetail}\r\n                ></textarea>\r\n              </div>\r\n              <div className=\"submit-row\">\r\n                <button type=\"submit\" className=\"save-button\">\r\n                  Save\r\n                </button>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"cancel-button\"\r\n                  onClick={handleEditToggle}\r\n                >\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductDetail;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,sCAAsC;AAC7C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,MAAM,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACpE,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGX,UAAU,CAAC,CAAC;EAClD,MAAM;IAAEY,IAAI;IAAEC;EAAQ,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACnC,MAAMa,UAAU,GAAG,WAAW;EAC9B,MAAMC,aAAa,GAAG,QAAQ;EAC9B,MAAM,CAACC,CAAC,EAAEC,IAAI,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IAAE,GAAGS;EAAQ,CAAC,CAAC;EACxD,MAAM,CAACgB,QAAQ,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAACS,OAAO,CAAC;EAChD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6B,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAC9BS,OAAO,CAACsB,KAAK,GACTtB,OAAO,CAACsB,KAAK,CAACC,UAAU,GACxB,+KACN,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmC,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMmC,QAAQ,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMoC,SAAS,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqC,SAAS,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsC,aAAa,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChDE,SAAS,CAAC,MAAM;IACd,MAAMwC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIC,IAAI,GAAG;QACT5B,IAAI,EAAEA;MACR,CAAC;MACD,IAAI;QACF,IAAI6B,QAAQ,GAAG,MAAMC,KAAK,CACxB,iDAAiD,EACjD;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;QAC3B,CACF,CAAC;QACD,MAAMO,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAACH,IAAI,CAACV,SAAS,CAAC;QAC3BC,YAAY,CAACS,IAAI,CAACV,SAAS,CAAC;MAC9B,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IACDZ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIhB,aAAa,CAACiB,OAAO,EAAE;MACzBjB,aAAa,CAACiB,OAAO,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChE;EACF,CAAC;EACD,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B1B,aAAa,CAAC,IAAI,CAAC;IACnBqB,WAAW,CAAC,CAAC;IACbjB,SAAS,CAACkB,OAAO,GAAG,MAAMK,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;MAC5DC,KAAK,EAAE;IACT,CAAC,CAAC;IACF5B,QAAQ,CAACoB,OAAO,CAACS,SAAS,GAAG3B,SAAS,CAACkB,OAAO;EAChD,CAAC;EACD,MAAMU,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACH,KAAK,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;IAEvE;IACA,MAAMC,cAAc,GAAG,CAACF,MAAM,CAACG,KAAK,CAACJ,YAAY,CAAC,GAC9CA,YAAY,CAACK,cAAc,CAAC,OAAO,CAAC,GACpCP,KAAK;;IAET;IACA7C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC6C,IAAI,GACH,OAAOM,cAAc,KAAK,QAAQ,GAC9BA,cAAc,CAACG,WAAW,CAAC,CAAC,CAACJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAC/CJ,KAAK,CAACI,OAAO,CAAC,IAAI,EAAE,GAAG;IAC/B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,iBAAiB,GAAIX,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC9C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC6C,IAAI,GAAGC;IACV,CAAC,CAAC;IACFlC,YAAY,CAACqB,OAAO,CAACa,KAAK,GAAG,EAAE;IAC/BvC,OAAO,CAACuC,KAAK,CAAC;EAChB,CAAC;EACD,MAAMU,kBAAkB,GAAIZ,CAAC,IAAK;IAChC,MAAM;MAAEE;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC1B1C,UAAU,CAACyC,KAAK,CAAC;EACnB,CAAC;EACD,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1D,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAM4D,YAAY,GAAG,MAAOd,CAAC,IAAK;IAChCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,IAAIC,CAAC,GAAG;MAAE,GAAG5D;IAAS,CAAC;IACvB,IAAIA,QAAQ,CAACQ,KAAK,IAAItB,OAAO,CAACsB,KAAK,IAAIR,QAAQ,CAACQ,KAAK,EAAE;MACrD,MAAMqD,SAAS,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAChCD,SAAS,CAACE,MAAM,CAAC,MAAM,EAAE/D,QAAQ,CAACQ,KAAK,CAAC;MACxCqD,SAAS,CAACE,MAAM,CAAC,eAAe,EAAEpE,aAAa,CAAC;MAChD,IAAI;QACFL,YAAY,CAAC,CAAC;QACd,MAAM0E,kBAAkB,GAAG,MAAM1C,KAAK,CACpC,mCAAmC5B,UAAU,SAAS,EACtD;UACE6B,MAAM,EAAE,MAAM;UACdH,IAAI,EAAEyC,SAAS,CAAE;QACnB,CACF,CAAC;QACD,MAAMlC,IAAI,GAAG,MAAMqC,kBAAkB,CAACpC,IAAI,CAAC,CAAC;QAC5C,MAAMnB,UAAU,GAAGkB,IAAI,CAAClB,UAAU;QAClC,MAAMwD,SAAS,GAAGtC,IAAI,CAACsC,SAAS;QAChCL,CAAC,GAAG;UACF,GAAGA,CAAC;UACJpD,KAAK,EAAE;YAAEC,UAAU;YAAEwD;UAAU,CAAC,CAAE;QACpC,CAAC;MACH,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CjD,MAAM,CAAC,CAAC,EAAE,qCAAqC,EAAE,UAAU,CAAC;MAC9D;IACF;IACAM,QAAQ,CAACwE,CAAC,EAAExD,OAAO,EAAEJ,QAAQ,CAACQ,KAAK,IAAItB,OAAO,CAACsB,KAAK,IAAIR,QAAQ,CAACQ,KAAK,CAAC;IACvEL,UAAU,CAACH,QAAQ,CAAC;IACpBD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EACD,MAAMmE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMzB,KAAK,GAAG5B,QAAQ,CAACoB,OAAO;IAC9B,MAAMkC,MAAM,GAAGrD,SAAS,CAACmB,OAAO;IAChC,MAAMmC,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACvCF,MAAM,CAACG,KAAK,GAAG7B,KAAK,CAAC8B,UAAU;IAC/BJ,MAAM,CAACK,MAAM,GAAG/B,KAAK,CAACgC,WAAW;IACjCL,OAAO,CAACM,SAAS,CAACjC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE0B,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACK,MAAM,CAAC;IAC3D,MAAMG,QAAQ,GAAGR,MAAM,CAACS,SAAS,CAAC,WAAW,CAAC;IAC9CrE,OAAO,CAACoE,QAAQ,CAAC;IACjB,IAAI5D,SAAS,CAACkB,OAAO,EAAE;MACrB,MAAM4C,MAAM,GAAG9D,SAAS,CAACkB,OAAO,CAAC6C,SAAS,CAAC,CAAC;MAC5CD,MAAM,CAACE,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACzCpE,QAAQ,CAACoB,OAAO,CAACS,SAAS,GAAG,IAAI,CAAC,CAAC;MACnC3B,SAAS,CAACkB,OAAO,GAAG,IAAI,CAAC,CAAC;IAC5B;IACAtB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtB;IACAW,KAAK,CAACqD,QAAQ,CAAC,CACZO,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEE,IAAI,IAAK;MACd,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,aAAa,EAAE;QAAEG,IAAI,EAAE;MAAY,CAAC,CAAC;MACnE,MAAMC,YAAY,GAAG,IAAIC,YAAY,CAAC,CAAC;MACvCD,YAAY,CAACE,KAAK,CAACC,GAAG,CAACN,IAAI,CAAC;MAC5BzE,YAAY,CAACqB,OAAO,CAAC2D,KAAK,GAAGJ,YAAY,CAACI,KAAK;MAC/C3F,WAAW,CAAE4F,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACXrF,KAAK,EAAE6E,IAAI,CAAE;MACf,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EACD,MAAMS,iBAAiB,GAAIlD,CAAC,IAAK;IAC/B3C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXQ,KAAK,EAAEoC,CAAC,CAACG,MAAM,CAAC6C,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,MAAMjB,QAAQ,GAAGoB,GAAG,CAACC,eAAe,CAACpD,CAAC,CAACG,MAAM,CAAC6C,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD/D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE6C,QAAQ,CAAC;IACnDpE,OAAO,CAACoE,QAAQ,CAAC,CAAC,CAAC;EACrB,CAAC;EACD,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIlF,SAAS,CAACkB,OAAO,EAAE;MACrB,MAAM4C,MAAM,GAAG9D,SAAS,CAACkB,OAAO,CAAC6C,SAAS,CAAC,CAAC;MAC5CD,MAAM,CAACE,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACzCpE,QAAQ,CAACoB,OAAO,CAACS,SAAS,GAAG,IAAI,CAAC,CAAC;MACnC3B,SAAS,CAACkB,OAAO,GAAG,IAAI,CAAC,CAAC;IAC5B;IACAtB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,MAAMuF,aAAa,GAAItD,CAAC,IAAK;IAC3B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC9C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC6C,IAAI,GAAGC,KAAK,CAACQ,WAAW,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;EACD,oBACEtE,OAAA;IAAKmH,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCpH,OAAA;MAAKmH,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCpH,OAAA;QAAMmH,SAAS,EAAC,cAAc;QAACE,OAAO,EAAElH,OAAQ;QAAAiH,QAAA,EAAC;MAEjD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACN,CAAC3G,SAAS,gBACTd,OAAA;QAAKmH,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAgD3BpH,OAAA;UAAKmH,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCpH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAAC2C;YAAI;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACwG;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7BzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACyG;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAAC0G;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzBzH,OAAA;cAAAoH,QAAA,GAAM,GAAC,EAAClG,QAAQ,CAAC2G,KAAK;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAAC4G;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAAC6G;YAAY;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BzH,OAAA;cAAAoH,QAAA,EACGlG,QAAQ,CAAC8G,QAAQ,GACd9G,QAAQ,CAAC8G,QAAQ,CAACnE,IAAI,GACtB;YAAyE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3BzH,OAAA;cAAAoH,QAAA,EACG,IAAIa,IAAI,CAAC/G,QAAQ,CAACgH,YAAY,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACkH;YAAQ;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzCzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACmH;YAAkB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACoH;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1BzH,OAAA;cAAAoH,QAAA,GAAM,GAAC,EAAClG,QAAQ,CAACqH,aAAa;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvBzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACsH;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCpH,OAAA;cAAAoH,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1BzH,OAAA;cAAAoH,QAAA,EAAOlG,QAAQ,CAACM,KAAK,GAAGN,QAAQ,CAACM,KAAK,CAACC,UAAU,GAAG;YAAE;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzH,OAAA;UACEyI,GAAG,EACDvH,QAAQ,CAACM,KAAK,GACVN,QAAQ,CAACM,KAAK,CAACC,UAAU,GACzB,+KACL;UACDiH,GAAG,EAAC,eAAe;UACnBvB,SAAS,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFzH,OAAA;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzH,OAAA;UAAQmH,SAAS,EAAC,oBAAoB;UAACE,OAAO,EAAE5C,gBAAiB;UAAA2C,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENzH,OAAA;QAAKmH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpH,OAAA;UAAAoH,QAAA,EAAI;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBzH,OAAA;UAAM2I,QAAQ,EAAEjE,YAAa;UAAA0C,QAAA,gBAC3BpH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,MAAM;cAAAxB,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,MAAM;cACThF,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE9C,QAAQ,CAAC6C,IAAK;cACrBiF,QAAQ,EAAE5B,aAAc;cACxB6B,QAAQ;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,UAAU;cAAAxB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,UAAU;cACbhF,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE9C,QAAQ,CAAC0G,QAAS;cACzBoB,QAAQ,EAAE5B,aAAc;cACxB6B,QAAQ;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,OAAO;cACVhF,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9C,QAAQ,CAAC2G,KAAM;cACtBmB,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,KAAK;cAAAxB,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,KAAK;cACRhF,IAAI,EAAC,KAAK;cACVC,KAAK,EAAE9C,QAAQ,CAAC4G,GAAI;cACpBkB,QAAQ,EAAE5B,aAAc;cACxB6B,QAAQ;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,OAAO;cACVhF,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9C,QAAQ,CAAC6G,KAAM;cACtBiB,QAAQ,EAAEnF,YAAa;cACvBoF,QAAQ;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,eAAe;cAAAxB,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,eAAe;cAClBhF,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAE9C,QAAQ,CAACuH,aAAc;cAC9BO,QAAQ,EAAEnF;YAAa;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,gBAAgB;cAAAxB,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDzH,OAAA;cACEuG,IAAI,EAAC,QAAQ;cACbsC,EAAE,EAAC,gBAAgB;cACnBhF,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAE9C,QAAQ,CAAC8G,cAAe;cAC/BgB,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,cAAc;cAAAxB,QAAA,EAAC;YAE9B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACEuG,IAAI,EAAC,QAAQ;cACbsC,EAAE,EAAC,cAAc;cACjBhF,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAE9C,QAAQ,CAAC+G,YAAa;cAC7Be,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,UAAU;cAAAxB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9CzH,OAAA;cACE6I,EAAE,EAAC,UAAU;cACbhF,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE9C,QAAQ,CAACgH,QAAQ,GAAGhH,QAAQ,CAACgH,QAAQ,CAACgB,GAAG,GAAG,EAAG;cACtDF,QAAQ,EAAE5B,aAAc;cAAAE,QAAA,EAEvBnF,SAAS,CAACgH,GAAG,CAAEjB,QAAQ,iBACtBhI,OAAA;gBAA2B8D,KAAK,EAAEkE,QAAQ,CAACgB,GAAI;gBAAA5B,QAAA,EAC5CY,QAAQ,CAACnE;cAAI,GADHmE,QAAQ,CAACgB,GAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,cAAc;cAAAxB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,cAAc;cACjBhF,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAE9C,QAAQ,CAACkH,YAAa;cAC7BY,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,UAAU;cAAAxB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,UAAU;cACbhF,IAAI,EAAC,UAAU;cACfC,KAAK,EAAE9C,QAAQ,CAACoH,QAAS;cACzBU,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,oBAAoB;cAAAxB,QAAA,EAAC;YAEpC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACEuG,IAAI,EAAC,QAAQ;cACbsC,EAAE,EAAC,oBAAoB;cACvBhF,IAAI,EAAC,oBAAoB;cACzBC,KAAK,EAAE9C,QAAQ,CAACqH,kBAAmB;cACnCS,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,MAAM;cAAAxB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCzH,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXsC,EAAE,EAAC,MAAM;cACThF,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE9C,QAAQ,CAACsH,IAAK;cACrBQ,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCzH,OAAA;cACE6I,EAAE,EAAC,OAAO;cACVhF,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9C,QAAQ,CAACwH,KAAM;cACtBM,QAAQ,EAAE5B;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,OAAO;cAAAxB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCzH,OAAA;cACEyI,GAAG,EAAEnH,IAAK;cACV6F,SAAS,EAAC,oBAAoB;cAC9BuB,GAAG,EAAC;YAAe;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFzH,OAAA;cACEmH,SAAS,EAAC,cAAc;cACxBE,OAAO,EAAEA,CAAA,KAAM;gBACbxG,IAAI,CAAE+D,CAAC,IAAK;kBACV,OAAO,CAACA,CAAC;gBACX,CAAC,CAAC;cACJ,CAAE;cAAAwC,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL7G,CAAC,iBACAZ,OAAA;cAAKmH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpH,OAAA;gBAAO4I,OAAO,EAAC,OAAO;gBAAAxB,QAAA,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDzH,OAAA;gBAAGkJ,KAAK,EAAE;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAA/B,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DzH,OAAA;gBACEuG,IAAI,EAAC,MAAM;gBACX6C,GAAG,EAAExH,YAAa;gBAClBiC,IAAI,EAAC,OAAO;gBACZiF,QAAQ,EAAEhC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFzH,OAAA;gBAAGkJ,KAAK,EAAE;kBAAEC,YAAY,EAAE,KAAK;kBAAEE,SAAS,EAAE;gBAAM,CAAE;gBAAAjC,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzH,OAAA;gBACEuG,IAAI,EAAC,MAAM;gBACXsC,EAAE,EAAC,OAAO;gBACVhF,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE9C,QAAQ,CAACQ,KAAM;gBACtBsH,QAAQ,EAAEvE;cAAkB;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFzH,OAAA;gBAAGkJ,KAAK,EAAE;kBAAEC,YAAY,EAAE,KAAK;kBAAEE,SAAS,EAAE;gBAAM,CAAE;gBAAAjC,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzH,OAAA;gBAAKmH,SAAS,EAAC,SAAS;gBAACE,OAAO,EAAEhE,WAAY;gBAAA+D,QAAA,EAAC;cAE/C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAGL/F,UAAU,iBACT1B,OAAA;gBAAKmH,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BpH,OAAA;kBAAKmH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BpH,OAAA;oBACEoJ,GAAG,EAAEvH,QAAS;oBACdyH,QAAQ;oBACRJ,KAAK,EAAE;sBAAE5D,KAAK,EAAE;oBAAO;kBAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACFzH,OAAA;oBACEmH,SAAS,EAAC,gBAAgB;oBAC1BE,OAAO,EAAEnC,YAAa;oBAAAkC,QAAA,EACvB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTzH,OAAA;oBACEmH,SAAS,EAAC,gBAAgB;oBAC1BE,OAAO,EAAEJ,UAAW;oBAAAG,QAAA,EACrB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDzH,OAAA;gBAAQoJ,GAAG,EAAEtH,SAAU;gBAACoH,KAAK,EAAE;kBAAEK,OAAO,EAAE;gBAAO;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAO4I,OAAO,EAAC,QAAQ;cAAAxB,QAAA,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DzH,OAAA;cACE6I,EAAE,EAAC,QAAQ;cACXhF,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAE1C,OAAQ;cACf0H,QAAQ,EAAEtE;YAAmB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNzH,OAAA;YAAKmH,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpH,OAAA;cAAQuG,IAAI,EAAC,QAAQ;cAACY,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE9C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzH,OAAA;cACEuG,IAAI,EAAC,QAAQ;cACbY,SAAS,EAAC,eAAe;cACzBE,OAAO,EAAE5C,gBAAiB;cAAA2C,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpH,EAAA,CAnkBIJ,aAAa;EAAA,QACqBL,UAAU,EACtBC,OAAO;AAAA;AAAA2J,EAAA,GAF7BvJ,aAAa;AAqkBnB,eAAeA,aAAa;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}