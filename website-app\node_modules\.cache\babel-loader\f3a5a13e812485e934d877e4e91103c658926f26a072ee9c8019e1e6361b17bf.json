{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\components\\\\introduce\\\\resetpassword.js\",\n  _s = $RefreshSig$();\nimport \"../../components/introduce/resetpassword.css\";\nimport { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Change_password = ({\n  email,\n  off\n}) => {\n  _s();\n  const [password, SetPassword] = useState(\"\");\n  const [confirmpassword, SetconfirmPassword] = useState(\"\");\n  const [error, SetError] = useState(\"\");\n  const submit_log = e => {\n    e.preventDefault();\n    if (password != confirmpassword) {\n      SetError(\"nhập lại mật khẩu không khớp với mật khẩu\");\n      return;\n    }\n    const body = {\n      email: email,\n      password: password\n    };\n    fetch(\"http://localhost:8080/api/login/change_password2\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(body)\n    }).then(response => response.json()).then(data => {\n      console.log(data.message);\n      if (data.message === \"Success\") {\n        off();\n      } else {\n        SetError(data.message);\n      }\n    }).catch(error => {\n      console.error(\"Lỗi:\", error);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"change-login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"change-login-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"change-login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: \"30px\",\n              lineHeight: \"1.7\",\n              fontWeight: \"bold\"\n            },\n            children: \"Reset Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              marginTop: \"10px\"\n            },\n            children: \"\\u0110i\\u1EC1n m\\u1EADt kh\\u1EA9u m\\u1EDBi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"change-close-btn\",\n          onClick: () => {\n            off();\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"change-login-form\",\n        onSubmit: submit_log,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"change-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"password\",\n            type: \"password\",\n            placeholder: \"password\",\n            value: password,\n            onChange: e => {\n              SetPassword(e.target.value);\n              SetError(\"\");\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            name: \"password\",\n            type: \"password\",\n            placeholder: \"Nh\\u1EADp l\\u1EA1i password\",\n            value: confirmpassword,\n            onChange: e => {\n              SetconfirmPassword(e.target.value);\n              SetError(\"\");\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            id: \"login-btn\",\n            type: \"submit\",\n            children: \"X\\xE1c nh\\u1EADn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: \"red\",\n              padding: \"10px 0px\"\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(Change_password, \"o5kAxQ5W5QamkzTwEsV2yAoE39A=\");\n_c = Change_password;\nexport default Change_password;\nvar _c;\n$RefreshReg$(_c, \"Change_password\");", "map": {"version": 3, "names": ["useState", "jsxDEV", "_jsxDEV", "Change_password", "email", "off", "_s", "password", "SetPassword", "confirmpassword", "SetconfirmPassword", "error", "SetError", "submit_log", "e", "preventDefault", "body", "fetch", "method", "headers", "JSON", "stringify", "then", "response", "json", "data", "console", "log", "message", "catch", "className", "children", "style", "fontSize", "lineHeight", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "onClick", "onSubmit", "name", "type", "placeholder", "value", "onChange", "target", "required", "id", "color", "padding", "_c", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/components/introduce/resetpassword.js"], "sourcesContent": ["import \"../../components/introduce/resetpassword.css\";\r\nimport { useState } from \"react\";\r\nconst Change_password = ({ email, off }) => {\r\n  const [password, SetPassword] = useState(\"\");\r\n  const [confirmpassword, SetconfirmPassword] = useState(\"\");\r\n  const [error, SetError] = useState(\"\");\r\n  const submit_log = (e) => {\r\n    e.preventDefault();\r\n    if (password != confirmpassword) {\r\n      SetError(\"nhập lại mật khẩu không khớp với mật khẩu\");\r\n      return;\r\n    }\r\n    const body = {\r\n      email: email,\r\n      password: password,\r\n    };\r\n    fetch(\"http://localhost:8080/api/login/change_password2\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(body),\r\n    })\r\n      .then((response) => response.json())\r\n      .then((data) => {\r\n        console.log(data.message);\r\n        if (data.message === \"Success\") {\r\n          off();\r\n        } else {\r\n          SetError(data.message);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Lỗi:\", error);\r\n      });\r\n  };\r\n  return (\r\n    <div className=\"change-login\">\r\n      <div className=\"change-login-modal\">\r\n        <div className=\"change-login-header\">\r\n          <div>\r\n            <h1\r\n              style={{\r\n                fontSize: \"30px\",\r\n                lineHeight: \"1.7\",\r\n                fontWeight: \"bold\",\r\n              }}\r\n            >\r\n              Reset Password\r\n            </h1>\r\n            <h2 style={{ marginTop: \"10px\" }}>Điền mật khẩu mới</h2>\r\n          </div>\r\n          <span\r\n            className=\"change-close-btn\"\r\n            onClick={() => {\r\n              off();\r\n            }}\r\n          >\r\n            &times;\r\n          </span>\r\n        </div>\r\n        <form className=\"change-login-form\" onSubmit={submit_log}>\r\n          <div className=\"change-form-group\">\r\n            <input\r\n              name=\"password\"\r\n              type=\"password\"\r\n              placeholder=\"password\"\r\n              value={password}\r\n              onChange={(e) => {\r\n                SetPassword(e.target.value);\r\n                SetError(\"\");\r\n              }}\r\n              required\r\n            />\r\n            <input\r\n              name=\"password\"\r\n              type=\"password\"\r\n              placeholder=\"Nhập lại password\"\r\n              value={confirmpassword}\r\n              onChange={(e) => {\r\n                SetconfirmPassword(e.target.value);\r\n                SetError(\"\");\r\n              }}\r\n              required\r\n            />\r\n            <button id=\"login-btn\" type=\"submit\">\r\n              Xác nhận\r\n            </button>\r\n            <p style={{ color: \"red\", padding: \"10px 0px\" }}>{error}</p>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nexport default Change_password;\r\n"], "mappings": ";;AAAA,OAAO,8CAA8C;AACrD,SAASA,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACjC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAI,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,eAAe,EAAEC,kBAAkB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMa,UAAU,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIR,QAAQ,IAAIE,eAAe,EAAE;MAC/BG,QAAQ,CAAC,2CAA2C,CAAC;MACrD;IACF;IACA,MAAMI,IAAI,GAAG;MACXZ,KAAK,EAAEA,KAAK;MACZG,QAAQ,EAAEA;IACZ,CAAC;IACDU,KAAK,CAAC,kDAAkD,EAAE;MACxDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDH,IAAI,EAAEI,IAAI,CAACC,SAAS,CAACL,IAAI;IAC3B,CAAC,CAAC,CACCM,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAK;MACdC,OAAO,CAACC,GAAG,CAACF,IAAI,CAACG,OAAO,CAAC;MACzB,IAAIH,IAAI,CAACG,OAAO,KAAK,SAAS,EAAE;QAC9BvB,GAAG,CAAC,CAAC;MACP,CAAC,MAAM;QACLO,QAAQ,CAACa,IAAI,CAACG,OAAO,CAAC;MACxB;IACF,CAAC,CAAC,CACDC,KAAK,CAAElB,KAAK,IAAK;MAChBe,OAAO,CAACf,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC;IAC9B,CAAC,CAAC;EACN,CAAC;EACD,oBACET,OAAA;IAAK4B,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3B7B,OAAA;MAAK4B,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC7B,OAAA;QAAK4B,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC7B,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YACE8B,KAAK,EAAE;cACLC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrC,OAAA;YAAI8B,KAAK,EAAE;cAAEQ,SAAS,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNrC,OAAA;UACE4B,SAAS,EAAC,kBAAkB;UAC5BW,OAAO,EAAEA,CAAA,KAAM;YACbpC,GAAG,CAAC,CAAC;UACP,CAAE;UAAA0B,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA;QAAM4B,SAAS,EAAC,mBAAmB;QAACY,QAAQ,EAAE7B,UAAW;QAAAkB,QAAA,eACvD7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YACEyC,IAAI,EAAC,UAAU;YACfC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,UAAU;YACtBC,KAAK,EAAEvC,QAAS;YAChBwC,QAAQ,EAAGjC,CAAC,IAAK;cACfN,WAAW,CAACM,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAC;cAC3BlC,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFqC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFrC,OAAA;YACEyC,IAAI,EAAC,UAAU;YACfC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,6BAAmB;YAC/BC,KAAK,EAAErC,eAAgB;YACvBsC,QAAQ,EAAGjC,CAAC,IAAK;cACfJ,kBAAkB,CAACI,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAC;cAClClC,QAAQ,CAAC,EAAE,CAAC;YACd,CAAE;YACFqC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFrC,OAAA;YAAQgD,EAAE,EAAC,WAAW;YAACN,IAAI,EAAC,QAAQ;YAAAb,QAAA,EAAC;UAErC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrC,OAAA;YAAG8B,KAAK,EAAE;cAAEmB,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAW,CAAE;YAAArB,QAAA,EAAEpB;UAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA5FIH,eAAe;AAAAkD,EAAA,GAAflD,eAAe;AA6FrB,eAAeA,eAAe;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}