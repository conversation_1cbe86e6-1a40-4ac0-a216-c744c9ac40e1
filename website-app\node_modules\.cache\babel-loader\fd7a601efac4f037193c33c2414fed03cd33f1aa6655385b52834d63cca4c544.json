{"ast": null, "code": "var _jsxFileName = \"F:\\\\File\\\\Web\\\\BTLNMCNNPM\\\\Front\\\\NMCNPM_IT3180_20241\\\\website-app\\\\src\\\\pages\\\\home\\\\chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport io from \"socket.io-client\";\nimport { useAuth } from \"../../components/introduce/useAuth\";\nimport { IoCallSharp } from \"react-icons/io5\";\nimport { FaVideo } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Chat({\n  chats,\n  ring\n}) {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const chatEndRef = useRef(null);\n  const [message, setMessage] = useState(\"\");\n  const [chat, setChat] = useState([]);\n  const [socketConnected, setSocketConnected] = useState(false); // ✅ Trạng thái kết nối\n  const messageHandled = useRef(false);\n  const socketRef = useRef(null); // ✅ Sử dụng useRef để lưu socket\n  // ✅ useEffect để khởi tạo socket và fetch messages\n  useEffect(() => {\n    if (loading || !user) return;\n\n    // Khởi tạo socket connection một lần duy nhất\n    if (!socketRef.current) {\n      const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || \"http://localhost:8080\";\n      console.log(\"🔌 Attempting to connect to:\", SOCKET_URL);\n      socketRef.current = io(SOCKET_URL, {\n        transports: [\"polling\"],\n        // ✅ Chỉ sử dụng polling để tránh lỗi WebSocket\n        timeout: 20000,\n        forceNew: true,\n        autoConnect: true,\n        reconnection: true,\n        reconnectionAttempts: 3,\n        // Giảm số lần thử lại\n        reconnectionDelay: 2000,\n        upgrade: false,\n        // ✅ Không upgrade lên WebSocket\n        rememberUpgrade: false,\n        // Thêm các options để debug\n        withCredentials: false,\n        extraHeaders: {\n          \"Access-Control-Allow-Origin\": \"*\"\n        }\n      });\n\n      // Log connection status và error handling chi tiết\n      socketRef.current.on(\"connect\", () => {\n        console.log(\"✅ Socket connected successfully\");\n        console.log(\"Transport:\", socketRef.current.io.engine.transport.name);\n        setSocketConnected(true); // ✅ Cập nhật trạng thái kết nối\n      });\n      socketRef.current.on(\"connect_error\", error => {\n        console.error(\"❌ Socket connection error:\", error);\n        console.error(\"Error type:\", error.type);\n        console.error(\"Error description:\", error.description);\n        setSocketConnected(false); // ✅ Cập nhật trạng thái kết nối\n      });\n      socketRef.current.on(\"disconnect\", reason => {\n        console.warn(\"🔌 Socket disconnected:\", reason);\n        setSocketConnected(false); // ✅ Cập nhật trạng thái kết nối\n      });\n      socketRef.current.on(\"reconnect\", attemptNumber => {\n        console.log(\"🔄 Socket reconnected after\", attemptNumber, \"attempts\");\n      });\n      socketRef.current.on(\"reconnect_error\", error => {\n        console.error(\"🔄❌ Reconnection failed:\", error);\n      });\n      socketRef.current.on(\"reconnect_failed\", () => {\n        console.error(\"💀 All reconnection attempts failed\");\n        // Có thể thêm logic fallback ở đây\n      });\n    }\n    const fetchMessages = async () => {\n      try {\n        const response = await fetch(\"http://localhost:8080/api/chat/getMessages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            user\n          })\n        });\n        const data = await response.json();\n        console.log(data);\n        if (Array.isArray(data)) {\n          const formattedData = data.map(msg => ({\n            ...msg,\n            isUser: msg.sender._id === user._id\n          }));\n          setChat(formattedData);\n        }\n      } catch (err) {\n        console.error(\"Error fetching messages:\", err);\n      }\n    };\n    fetchMessages();\n\n    // Cleanup function khi component unmount\n    return () => {\n      if (socketRef.current) {\n        socketRef.current.disconnect();\n        socketRef.current = null;\n      }\n    };\n  }, [loading, user]);\n  // ✅ useEffect để setup socket listeners\n  useEffect(() => {\n    if (!socketRef.current || !user) return;\n    const handleReceiveMessage = data => {\n      console.log(\"Received message:\", data);\n      console.log(\"Message handled:\", !messageHandled.current);\n      console.log(\"Different sender:\", data.sender._id !== user._id);\n      if (!messageHandled.current && data.sender._id !== user._id) {\n        messageHandled.current = true;\n        const newMessage = {\n          ...data,\n          isUser: data.sender._id === user._id\n        };\n        console.log(\"Current chat:\", chats);\n        if (!chats) {\n          ring();\n        }\n        setChat(prev => [...prev, newMessage]);\n        setTimeout(() => {\n          messageHandled.current = false; // Reset để xử lý tin nhắn mới\n        }, 1000);\n      }\n    };\n\n    // Đăng ký event listener\n    socketRef.current.on(\"receive_message\", handleReceiveMessage);\n\n    // Cleanup function\n    return () => {\n      if (socketRef.current) {\n        socketRef.current.off(\"receive_message\", handleReceiveMessage);\n      }\n    };\n  }, [user, chats, ring]);\n  useEffect(() => {\n    var _chatEndRef$current;\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [chat]);\n  // ✅ Hàm gửi tin nhắn sử dụng socketRef\n  const sendMessage = () => {\n    if (message.trim() !== \"\" && socketRef.current) {\n      const newMessage = {\n        sender: user,\n        owner: user.id_owner,\n        content: message\n      };\n\n      // Emit message qua socket\n      socketRef.current.emit(\"send_message\", newMessage);\n\n      // Thêm message vào chat local\n      setChat(prev => [...prev, {\n        ...{\n          sender: user,\n          content: message\n        },\n        isUser: true\n      }]);\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\n    } else if (!socketRef.current) {\n      console.error(\"Socket not connected. Cannot send message.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.container,\n      display: chats ? \"block\" : \"none\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user === null || user === void 0 ? void 0 : user.avatar,\n          alt: \"Avatar\",\n          style: styles.headerAvatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.headerName,\n          children: (user === null || user === void 0 ? void 0 : user.name) || \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            ...styles.connectionStatus,\n            backgroundColor: socketConnected ? \"#4CAF50\" : \"#f44336\"\n          },\n          title: socketConnected ? \"Connected\" : \"Disconnected\",\n          children: socketConnected ? \"●\" : \"●\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerRight,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(IoCallSharp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.headerButton,\n          children: /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.chatWindow,\n      children: [chat.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...styles.messageContainer,\n          justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\"\n        },\n        children: [!msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.message,\n            backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\"\n          },\n          children: msg.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), msg.isUser && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: msg.sender.avatar,\n          alt: \"Avatar\",\n          style: styles.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chatEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.inputContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        placeholder: \"Type a message...\",\n        style: styles.input\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        style: styles.button,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n}\n_s(Chat, \"zoyVmbeSZnPDJ36zXJW1qusE5mg=\", false, function () {\n  return [useAuth];\n});\n_c = Chat;\nconst styles = {\n  container: {\n    width: \"300px\",\n    margin: \"20px auto\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"5px\",\n    overflow: \"hidden\",\n    fontFamily: \"Arial, sans-serif\",\n    position: \"fixed\",\n    right: \"150px\",\n    bottom: \"95px\",\n    zIndex: 1000\n  },\n  chatWindow: {\n    height: \"300px\",\n    overflowY: \"scroll\",\n    padding: \"10px\",\n    backgroundColor: \"#f1f1f1\"\n  },\n  messageContainer: {\n    display: \"flex\",\n    alignItems: \"center\",\n    marginBottom: \"10px\"\n  },\n  avatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    margin: \"0 10px\"\n  },\n  message: {\n    padding: \"8px 12px\",\n    borderRadius: \"10px\",\n    maxWidth: \"200px\",\n    fontSize: \"14px\"\n  },\n  inputContainer: {\n    display: \"flex\",\n    borderTop: \"1px solid #ccc\",\n    padding: \"10px\",\n    backgroundColor: \"white\"\n  },\n  input: {\n    flex: 1,\n    padding: \"8px\",\n    fontSize: \"14px\",\n    border: \"1px solid #ccc\",\n    borderRadius: \"4px\",\n    outline: \"none\"\n  },\n  button: {\n    padding: \"8px 12px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\",\n    marginLeft: \"5px\"\n  },\n  header: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"10px\",\n    backgroundColor: \"#007bff\",\n    color: \"#fff\",\n    borderBottom: \"1px solid #ccc\"\n  },\n  headerLeft: {\n    display: \"flex\",\n    alignItems: \"center\"\n  },\n  headerAvatar: {\n    width: \"40px\",\n    height: \"40px\",\n    borderRadius: \"50%\",\n    marginRight: \"10px\"\n  },\n  headerName: {\n    fontSize: \"16px\",\n    fontWeight: \"bold\"\n  },\n  headerRight: {\n    display: \"flex\",\n    gap: \"5px\"\n  },\n  headerButton: {\n    padding: \"5px 10px\",\n    fontSize: \"14px\",\n    backgroundColor: \"#0056b3\",\n    color: \"#fff\",\n    border: \"none\",\n    borderRadius: \"4px\",\n    cursor: \"pointer\"\n  },\n  connectionStatus: {\n    display: \"inline-block\",\n    width: \"8px\",\n    height: \"8px\",\n    borderRadius: \"50%\",\n    marginLeft: \"8px\",\n    fontSize: \"8px\",\n    color: \"white\",\n    textAlign: \"center\",\n    lineHeight: \"8px\"\n  }\n};\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "io", "useAuth", "IoCallSharp", "FaVideo", "jsxDEV", "_jsxDEV", "Cha<PERSON>", "chats", "ring", "_s", "user", "loading", "chatEndRef", "message", "setMessage", "chat", "setChat", "socketConnected", "setSocketConnected", "messageHandled", "socketRef", "current", "SOCKET_URL", "process", "env", "REACT_APP_SOCKET_URL", "console", "log", "transports", "timeout", "forceNew", "autoConnect", "reconnection", "reconnectionAttempts", "reconnectionDelay", "upgrade", "rememberUpgrade", "withCredentials", "extraHeaders", "on", "engine", "transport", "name", "error", "type", "description", "reason", "warn", "attemptNumber", "fetchMessages", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "Array", "isArray", "formattedData", "map", "msg", "isUser", "sender", "_id", "err", "disconnect", "handleReceiveMessage", "newMessage", "prev", "setTimeout", "off", "_chatEndRef$current", "scrollIntoView", "behavior", "sendMessage", "trim", "owner", "id_owner", "content", "emit", "style", "styles", "container", "display", "children", "header", "headerLeft", "src", "avatar", "alt", "headerAvatar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "headerName", "connectionStatus", "backgroundColor", "title", "headerRight", "headerButton", "chatWindow", "index", "messageContainer", "justifyContent", "ref", "inputContainer", "value", "onChange", "e", "target", "placeholder", "input", "onClick", "button", "_c", "width", "margin", "border", "borderRadius", "overflow", "fontFamily", "position", "right", "bottom", "zIndex", "height", "overflowY", "padding", "alignItems", "marginBottom", "max<PERSON><PERSON><PERSON>", "fontSize", "borderTop", "flex", "outline", "color", "cursor", "marginLeft", "borderBottom", "marginRight", "fontWeight", "gap", "textAlign", "lineHeight", "$RefreshReg$"], "sources": ["F:/File/Web/BTLNMCNNPM/Front/NMCNPM_IT3180_20241/website-app/src/pages/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport io from \"socket.io-client\";\r\nimport { useAuth } from \"../../components/introduce/useAuth\";\r\nimport { IoCallSharp } from \"react-icons/io5\";\r\nimport { FaVideo } from \"react-icons/fa\";\r\n\r\nfunction Chat({ chats, ring }) {\r\n  const { user, loading } = useAuth();\r\n  const chatEndRef = useRef(null);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [chat, setChat] = useState([]);\r\n  const [socketConnected, setSocketConnected] = useState(false); // ✅ Trạng thái kết nối\r\n  const messageHandled = useRef(false);\r\n  const socketRef = useRef(null); // ✅ Sử dụng useRef để lưu socket\r\n  // ✅ useEffect để khởi tạo socket và fetch messages\r\n  useEffect(() => {\r\n    if (loading || !user) return;\r\n\r\n    // Khởi tạo socket connection một lần duy nhất\r\n    if (!socketRef.current) {\r\n      const SOCKET_URL =\r\n        process.env.REACT_APP_SOCKET_URL || \"http://localhost:8080\";\r\n\r\n      console.log(\"🔌 Attempting to connect to:\", SOCKET_URL);\r\n\r\n      socketRef.current = io(SOCKET_URL, {\r\n        transports: [\"polling\"], // ✅ Chỉ sử dụng polling để tránh lỗi WebSocket\r\n        timeout: 20000,\r\n        forceNew: true,\r\n        autoConnect: true,\r\n        reconnection: true,\r\n        reconnectionAttempts: 3, // Giảm số lần thử lại\r\n        reconnectionDelay: 2000,\r\n        upgrade: false, // ✅ Không upgrade lên WebSocket\r\n        rememberUpgrade: false,\r\n        // Thêm các options để debug\r\n        withCredentials: false,\r\n        extraHeaders: {\r\n          \"Access-Control-Allow-Origin\": \"*\",\r\n        },\r\n      });\r\n\r\n      // Log connection status và error handling chi tiết\r\n      socketRef.current.on(\"connect\", () => {\r\n        console.log(\"✅ Socket connected successfully\");\r\n        console.log(\"Transport:\", socketRef.current.io.engine.transport.name);\r\n        setSocketConnected(true); // ✅ Cập nhật trạng thái kết nối\r\n      });\r\n\r\n      socketRef.current.on(\"connect_error\", (error) => {\r\n        console.error(\"❌ Socket connection error:\", error);\r\n        console.error(\"Error type:\", error.type);\r\n        console.error(\"Error description:\", error.description);\r\n        setSocketConnected(false); // ✅ Cập nhật trạng thái kết nối\r\n      });\r\n\r\n      socketRef.current.on(\"disconnect\", (reason) => {\r\n        console.warn(\"🔌 Socket disconnected:\", reason);\r\n        setSocketConnected(false); // ✅ Cập nhật trạng thái kết nối\r\n      });\r\n\r\n      socketRef.current.on(\"reconnect\", (attemptNumber) => {\r\n        console.log(\"🔄 Socket reconnected after\", attemptNumber, \"attempts\");\r\n      });\r\n\r\n      socketRef.current.on(\"reconnect_error\", (error) => {\r\n        console.error(\"🔄❌ Reconnection failed:\", error);\r\n      });\r\n\r\n      socketRef.current.on(\"reconnect_failed\", () => {\r\n        console.error(\"💀 All reconnection attempts failed\");\r\n        // Có thể thêm logic fallback ở đây\r\n      });\r\n    }\r\n\r\n    const fetchMessages = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          \"http://localhost:8080/api/chat/getMessages\",\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({ user }),\r\n          }\r\n        );\r\n        const data = await response.json();\r\n        console.log(data);\r\n        if (Array.isArray(data)) {\r\n          const formattedData = data.map((msg) => ({\r\n            ...msg,\r\n            isUser: msg.sender._id === user._id,\r\n          }));\r\n          setChat(formattedData);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching messages:\", err);\r\n      }\r\n    };\r\n\r\n    fetchMessages();\r\n\r\n    // Cleanup function khi component unmount\r\n    return () => {\r\n      if (socketRef.current) {\r\n        socketRef.current.disconnect();\r\n        socketRef.current = null;\r\n      }\r\n    };\r\n  }, [loading, user]);\r\n  // ✅ useEffect để setup socket listeners\r\n  useEffect(() => {\r\n    if (!socketRef.current || !user) return;\r\n\r\n    const handleReceiveMessage = (data) => {\r\n      console.log(\"Received message:\", data);\r\n      console.log(\"Message handled:\", !messageHandled.current);\r\n      console.log(\"Different sender:\", data.sender._id !== user._id);\r\n\r\n      if (!messageHandled.current && data.sender._id !== user._id) {\r\n        messageHandled.current = true;\r\n\r\n        const newMessage = {\r\n          ...data,\r\n          isUser: data.sender._id === user._id,\r\n        };\r\n        console.log(\"Current chat:\", chats);\r\n        if (!chats) {\r\n          ring();\r\n        }\r\n        setChat((prev) => [...prev, newMessage]);\r\n        setTimeout(() => {\r\n          messageHandled.current = false; // Reset để xử lý tin nhắn mới\r\n        }, 1000);\r\n      }\r\n    };\r\n\r\n    // Đăng ký event listener\r\n    socketRef.current.on(\"receive_message\", handleReceiveMessage);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (socketRef.current) {\r\n        socketRef.current.off(\"receive_message\", handleReceiveMessage);\r\n      }\r\n    };\r\n  }, [user, chats, ring]);\r\n  useEffect(() => {\r\n    chatEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [chat]);\r\n  // ✅ Hàm gửi tin nhắn sử dụng socketRef\r\n  const sendMessage = () => {\r\n    if (message.trim() !== \"\" && socketRef.current) {\r\n      const newMessage = {\r\n        sender: user,\r\n        owner: user.id_owner,\r\n        content: message,\r\n      };\r\n\r\n      // Emit message qua socket\r\n      socketRef.current.emit(\"send_message\", newMessage);\r\n\r\n      // Thêm message vào chat local\r\n      setChat((prev) => [\r\n        ...prev,\r\n        {\r\n          ...{ sender: user, content: message },\r\n          isUser: true,\r\n        },\r\n      ]);\r\n\r\n      setMessage(\"\"); // Xóa nội dung tin nhắn sau khi gửi\r\n    } else if (!socketRef.current) {\r\n      console.error(\"Socket not connected. Cannot send message.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ ...styles.container, display: chats ? \"block\" : \"none\" }}>\r\n      {/* Header */}\r\n      <div style={styles.header}>\r\n        <div style={styles.headerLeft}>\r\n          <img src={user?.avatar} alt=\"Avatar\" style={styles.headerAvatar} />\r\n          <span style={styles.headerName}>{user?.name || \"Chat\"}</span>\r\n          {/* ✅ Indicator trạng thái kết nối */}\r\n          <span\r\n            style={{\r\n              ...styles.connectionStatus,\r\n              backgroundColor: socketConnected ? \"#4CAF50\" : \"#f44336\",\r\n            }}\r\n            title={socketConnected ? \"Connected\" : \"Disconnected\"}\r\n          >\r\n            {socketConnected ? \"●\" : \"●\"}\r\n          </span>\r\n        </div>\r\n        <div style={styles.headerRight}>\r\n          <button style={styles.headerButton}>\r\n            <IoCallSharp />\r\n          </button>\r\n          <button style={styles.headerButton}>\r\n            <FaVideo />\r\n          </button>\r\n        </div>\r\n      </div>\r\n      {/* Chat Window */}\r\n      <div style={styles.chatWindow}>\r\n        {chat.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              ...styles.messageContainer,\r\n              justifyContent: msg.isUser ? \"flex-end\" : \"flex-start\",\r\n            }}\r\n          >\r\n            {!msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n            <div\r\n              style={{\r\n                ...styles.message,\r\n                backgroundColor: msg.isUser ? \"#d1e7ff\" : \"#e1ffc7\",\r\n              }}\r\n            >\r\n              {msg.content}\r\n            </div>\r\n            {msg.isUser && (\r\n              <img src={msg.sender.avatar} alt=\"Avatar\" style={styles.avatar} />\r\n            )}\r\n          </div>\r\n        ))}\r\n        <div ref={chatEndRef} />\r\n      </div>\r\n      {/* Input Field */}\r\n      <div style={styles.inputContainer}>\r\n        <input\r\n          type=\"text\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          placeholder=\"Type a message...\"\r\n          style={styles.input}\r\n        />\r\n        <button onClick={sendMessage} style={styles.button}>\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst styles = {\r\n  container: {\r\n    width: \"300px\",\r\n    margin: \"20px auto\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"5px\",\r\n    overflow: \"hidden\",\r\n    fontFamily: \"Arial, sans-serif\",\r\n    position: \"fixed\",\r\n    right: \"150px\",\r\n    bottom: \"95px\",\r\n    zIndex: 1000,\r\n  },\r\n  chatWindow: {\r\n    height: \"300px\",\r\n    overflowY: \"scroll\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#f1f1f1\",\r\n  },\r\n  messageContainer: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    marginBottom: \"10px\",\r\n  },\r\n  avatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    margin: \"0 10px\",\r\n  },\r\n  message: {\r\n    padding: \"8px 12px\",\r\n    borderRadius: \"10px\",\r\n    maxWidth: \"200px\",\r\n    fontSize: \"14px\",\r\n  },\r\n  inputContainer: {\r\n    display: \"flex\",\r\n    borderTop: \"1px solid #ccc\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"white\",\r\n  },\r\n  input: {\r\n    flex: 1,\r\n    padding: \"8px\",\r\n    fontSize: \"14px\",\r\n    border: \"1px solid #ccc\",\r\n    borderRadius: \"4px\",\r\n    outline: \"none\",\r\n  },\r\n  button: {\r\n    padding: \"8px 12px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n    marginLeft: \"5px\",\r\n  },\r\n  header: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    padding: \"10px\",\r\n    backgroundColor: \"#007bff\",\r\n    color: \"#fff\",\r\n    borderBottom: \"1px solid #ccc\",\r\n  },\r\n  headerLeft: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n  },\r\n  headerAvatar: {\r\n    width: \"40px\",\r\n    height: \"40px\",\r\n    borderRadius: \"50%\",\r\n    marginRight: \"10px\",\r\n  },\r\n  headerName: {\r\n    fontSize: \"16px\",\r\n    fontWeight: \"bold\",\r\n  },\r\n  headerRight: {\r\n    display: \"flex\",\r\n    gap: \"5px\",\r\n  },\r\n  headerButton: {\r\n    padding: \"5px 10px\",\r\n    fontSize: \"14px\",\r\n    backgroundColor: \"#0056b3\",\r\n    color: \"#fff\",\r\n    border: \"none\",\r\n    borderRadius: \"4px\",\r\n    cursor: \"pointer\",\r\n  },\r\n  connectionStatus: {\r\n    display: \"inline-block\",\r\n    width: \"8px\",\r\n    height: \"8px\",\r\n    borderRadius: \"50%\",\r\n    marginLeft: \"8px\",\r\n    fontSize: \"8px\",\r\n    color: \"white\",\r\n    textAlign: \"center\",\r\n    lineHeight: \"8px\",\r\n  },\r\n};\r\n\r\nexport default Chat;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,IAAIA,CAAC;EAAEC,KAAK;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAMW,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,MAAMsB,cAAc,GAAGpB,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMqB,SAAS,GAAGrB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAChC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIa,OAAO,IAAI,CAACD,IAAI,EAAE;;IAEtB;IACA,IAAI,CAACU,SAAS,CAACC,OAAO,EAAE;MACtB,MAAMC,UAAU,GACdC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB;MAE7DC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEL,UAAU,CAAC;MAEvDF,SAAS,CAACC,OAAO,GAAGrB,EAAE,CAACsB,UAAU,EAAE;QACjCM,UAAU,EAAE,CAAC,SAAS,CAAC;QAAE;QACzBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE,IAAI;QAClBC,oBAAoB,EAAE,CAAC;QAAE;QACzBC,iBAAiB,EAAE,IAAI;QACvBC,OAAO,EAAE,KAAK;QAAE;QAChBC,eAAe,EAAE,KAAK;QACtB;QACAC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE;UACZ,6BAA6B,EAAE;QACjC;MACF,CAAC,CAAC;;MAEF;MACAlB,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,SAAS,EAAE,MAAM;QACpCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEP,SAAS,CAACC,OAAO,CAACrB,EAAE,CAACwC,MAAM,CAACC,SAAS,CAACC,IAAI,CAAC;QACrExB,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC;MAEFE,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;QAC/CjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDjB,OAAO,CAACiB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACC,IAAI,CAAC;QACxClB,OAAO,CAACiB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACE,WAAW,CAAC;QACtD3B,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC;MAEFE,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,YAAY,EAAGO,MAAM,IAAK;QAC7CpB,OAAO,CAACqB,IAAI,CAAC,yBAAyB,EAAED,MAAM,CAAC;QAC/C5B,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC;MAEFE,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,WAAW,EAAGS,aAAa,IAAK;QACnDtB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqB,aAAa,EAAE,UAAU,CAAC;MACvE,CAAC,CAAC;MAEF5B,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;QACjDjB,OAAO,CAACiB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD,CAAC,CAAC;MAEFvB,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,kBAAkB,EAAE,MAAM;QAC7Cb,OAAO,CAACiB,KAAK,CAAC,qCAAqC,CAAC;QACpD;MACF,CAAC,CAAC;IACJ;IAEA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,4CAA4C,EAC5C;UACEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAE9C;UAAK,CAAC;QAC/B,CACF,CAAC;QACD,MAAM+C,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClChC,OAAO,CAACC,GAAG,CAAC8B,IAAI,CAAC;QACjB,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACvB,MAAMI,aAAa,GAAGJ,IAAI,CAACK,GAAG,CAAEC,GAAG,KAAM;YACvC,GAAGA,GAAG;YACNC,MAAM,EAAED,GAAG,CAACE,MAAM,CAACC,GAAG,KAAKxD,IAAI,CAACwD;UAClC,CAAC,CAAC,CAAC;UACHlD,OAAO,CAAC6C,aAAa,CAAC;QACxB;MACF,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZzC,OAAO,CAACiB,KAAK,CAAC,0BAA0B,EAAEwB,GAAG,CAAC;MAChD;IACF,CAAC;IAEDlB,aAAa,CAAC,CAAC;;IAEf;IACA,OAAO,MAAM;MACX,IAAI7B,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAAC+C,UAAU,CAAC,CAAC;QAC9BhD,SAAS,CAACC,OAAO,GAAG,IAAI;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,EAAED,IAAI,CAAC,CAAC;EACnB;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACsB,SAAS,CAACC,OAAO,IAAI,CAACX,IAAI,EAAE;IAEjC,MAAM2D,oBAAoB,GAAIZ,IAAI,IAAK;MACrC/B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8B,IAAI,CAAC;MACtC/B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,CAACR,cAAc,CAACE,OAAO,CAAC;MACxDK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8B,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKxD,IAAI,CAACwD,GAAG,CAAC;MAE9D,IAAI,CAAC/C,cAAc,CAACE,OAAO,IAAIoC,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKxD,IAAI,CAACwD,GAAG,EAAE;QAC3D/C,cAAc,CAACE,OAAO,GAAG,IAAI;QAE7B,MAAMiD,UAAU,GAAG;UACjB,GAAGb,IAAI;UACPO,MAAM,EAAEP,IAAI,CAACQ,MAAM,CAACC,GAAG,KAAKxD,IAAI,CAACwD;QACnC,CAAC;QACDxC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEpB,KAAK,CAAC;QACnC,IAAI,CAACA,KAAK,EAAE;UACVC,IAAI,CAAC,CAAC;QACR;QACAQ,OAAO,CAAEuD,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,UAAU,CAAC,CAAC;QACxCE,UAAU,CAAC,MAAM;UACfrD,cAAc,CAACE,OAAO,GAAG,KAAK,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;;IAED;IACAD,SAAS,CAACC,OAAO,CAACkB,EAAE,CAAC,iBAAiB,EAAE8B,oBAAoB,CAAC;;IAE7D;IACA,OAAO,MAAM;MACX,IAAIjD,SAAS,CAACC,OAAO,EAAE;QACrBD,SAAS,CAACC,OAAO,CAACoD,GAAG,CAAC,iBAAiB,EAAEJ,oBAAoB,CAAC;MAChE;IACF,CAAC;EACH,CAAC,EAAE,CAAC3D,IAAI,EAAEH,KAAK,EAAEC,IAAI,CAAC,CAAC;EACvBV,SAAS,CAAC,MAAM;IAAA,IAAA4E,mBAAA;IACd,CAAAA,mBAAA,GAAA9D,UAAU,CAACS,OAAO,cAAAqD,mBAAA,uBAAlBA,mBAAA,CAAoBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC,EAAE,CAAC7D,IAAI,CAAC,CAAC;EACV;EACA,MAAM8D,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIhE,OAAO,CAACiE,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI1D,SAAS,CAACC,OAAO,EAAE;MAC9C,MAAMiD,UAAU,GAAG;QACjBL,MAAM,EAAEvD,IAAI;QACZqE,KAAK,EAAErE,IAAI,CAACsE,QAAQ;QACpBC,OAAO,EAAEpE;MACX,CAAC;;MAED;MACAO,SAAS,CAACC,OAAO,CAAC6D,IAAI,CAAC,cAAc,EAAEZ,UAAU,CAAC;;MAElD;MACAtD,OAAO,CAAEuD,IAAI,IAAK,CAChB,GAAGA,IAAI,EACP;QACE,GAAG;UAAEN,MAAM,EAAEvD,IAAI;UAAEuE,OAAO,EAAEpE;QAAQ,CAAC;QACrCmD,MAAM,EAAE;MACV,CAAC,CACF,CAAC;MAEFlD,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM,IAAI,CAACM,SAAS,CAACC,OAAO,EAAE;MAC7BK,OAAO,CAACiB,KAAK,CAAC,4CAA4C,CAAC;IAC7D;EACF,CAAC;EAED,oBACEtC,OAAA;IAAK8E,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACC,SAAS;MAAEC,OAAO,EAAE/E,KAAK,GAAG,OAAO,GAAG;IAAO,CAAE;IAAAgF,QAAA,gBAErElF,OAAA;MAAK8E,KAAK,EAAEC,MAAM,CAACI,MAAO;MAAAD,QAAA,gBACxBlF,OAAA;QAAK8E,KAAK,EAAEC,MAAM,CAACK,UAAW;QAAAF,QAAA,gBAC5BlF,OAAA;UAAKqF,GAAG,EAAEhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACS;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnE5F,OAAA;UAAM8E,KAAK,EAAEC,MAAM,CAACc,UAAW;UAAAX,QAAA,EAAE,CAAA7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,KAAI;QAAM;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE7D5F,OAAA;UACE8E,KAAK,EAAE;YACL,GAAGC,MAAM,CAACe,gBAAgB;YAC1BC,eAAe,EAAEnF,eAAe,GAAG,SAAS,GAAG;UACjD,CAAE;UACFoF,KAAK,EAAEpF,eAAe,GAAG,WAAW,GAAG,cAAe;UAAAsE,QAAA,EAErDtE,eAAe,GAAG,GAAG,GAAG;QAAG;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5F,OAAA;QAAK8E,KAAK,EAAEC,MAAM,CAACkB,WAAY;QAAAf,QAAA,gBAC7BlF,OAAA;UAAQ8E,KAAK,EAAEC,MAAM,CAACmB,YAAa;UAAAhB,QAAA,eACjClF,OAAA,CAACH,WAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACT5F,OAAA;UAAQ8E,KAAK,EAAEC,MAAM,CAACmB,YAAa;UAAAhB,QAAA,eACjClF,OAAA,CAACF,OAAO;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5F,OAAA;MAAK8E,KAAK,EAAEC,MAAM,CAACoB,UAAW;MAAAjB,QAAA,GAC3BxE,IAAI,CAAC+C,GAAG,CAAC,CAACC,GAAG,EAAE0C,KAAK,kBACnBpG,OAAA;QAEE8E,KAAK,EAAE;UACL,GAAGC,MAAM,CAACsB,gBAAgB;UAC1BC,cAAc,EAAE5C,GAAG,CAACC,MAAM,GAAG,UAAU,GAAG;QAC5C,CAAE;QAAAuB,QAAA,GAED,CAACxB,GAAG,CAACC,MAAM,iBACV3D,OAAA;UAAKqF,GAAG,EAAE3B,GAAG,CAACE,MAAM,CAAC0B,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE,eACD5F,OAAA;UACE8E,KAAK,EAAE;YACL,GAAGC,MAAM,CAACvE,OAAO;YACjBuF,eAAe,EAAErC,GAAG,CAACC,MAAM,GAAG,SAAS,GAAG;UAC5C,CAAE;UAAAuB,QAAA,EAEDxB,GAAG,CAACkB;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACLlC,GAAG,CAACC,MAAM,iBACT3D,OAAA;UAAKqF,GAAG,EAAE3B,GAAG,CAACE,MAAM,CAAC0B,MAAO;UAACC,GAAG,EAAC,QAAQ;UAACT,KAAK,EAAEC,MAAM,CAACO;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE;MAAA,GAnBIQ,KAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN,CAAC,eACF5F,OAAA;QAAKuG,GAAG,EAAEhG;MAAW;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEN5F,OAAA;MAAK8E,KAAK,EAAEC,MAAM,CAACyB,cAAe;MAAAtB,QAAA,gBAChClF,OAAA;QACEuC,IAAI,EAAC,MAAM;QACXkE,KAAK,EAAEjG,OAAQ;QACfkG,QAAQ,EAAGC,CAAC,IAAKlG,UAAU,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC5CI,WAAW,EAAC,mBAAmB;QAC/B/B,KAAK,EAAEC,MAAM,CAAC+B;MAAM;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACF5F,OAAA;QAAQ+G,OAAO,EAAEvC,WAAY;QAACM,KAAK,EAAEC,MAAM,CAACiC,MAAO;QAAA9B,QAAA,EAAC;MAEpD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxF,EAAA,CAlPQH,IAAI;EAAA,QACeL,OAAO;AAAA;AAAAqH,EAAA,GAD1BhH,IAAI;AAoPb,MAAM8E,MAAM,GAAG;EACbC,SAAS,EAAE;IACTkC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACV,CAAC;EACDxB,UAAU,EAAE;IACVyB,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACf/B,eAAe,EAAE;EACnB,CAAC;EACDM,gBAAgB,EAAE;IAChBpB,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACD1C,MAAM,EAAE;IACN4B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBF,MAAM,EAAE;EACV,CAAC;EACD3G,OAAO,EAAE;IACPsH,OAAO,EAAE,UAAU;IACnBT,YAAY,EAAE,MAAM;IACpBY,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE;EACZ,CAAC;EACD1B,cAAc,EAAE;IACdvB,OAAO,EAAE,MAAM;IACfkD,SAAS,EAAE,gBAAgB;IAC3BL,OAAO,EAAE,MAAM;IACf/B,eAAe,EAAE;EACnB,CAAC;EACDe,KAAK,EAAE;IACLsB,IAAI,EAAE,CAAC;IACPN,OAAO,EAAE,KAAK;IACdI,QAAQ,EAAE,MAAM;IAChBd,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBgB,OAAO,EAAE;EACX,CAAC;EACDrB,MAAM,EAAE;IACNc,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChBnC,eAAe,EAAE,SAAS;IAC1BuC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE;EACd,CAAC;EACDrD,MAAM,EAAE;IACNF,OAAO,EAAE,MAAM;IACfqB,cAAc,EAAE,eAAe;IAC/ByB,UAAU,EAAE,QAAQ;IACpBD,OAAO,EAAE,MAAM;IACf/B,eAAe,EAAE,SAAS;IAC1BuC,KAAK,EAAE,MAAM;IACbG,YAAY,EAAE;EAChB,CAAC;EACDrD,UAAU,EAAE;IACVH,OAAO,EAAE,MAAM;IACf8C,UAAU,EAAE;EACd,CAAC;EACDvC,YAAY,EAAE;IACZ0B,KAAK,EAAE,MAAM;IACbU,MAAM,EAAE,MAAM;IACdP,YAAY,EAAE,KAAK;IACnBqB,WAAW,EAAE;EACf,CAAC;EACD7C,UAAU,EAAE;IACVqC,QAAQ,EAAE,MAAM;IAChBS,UAAU,EAAE;EACd,CAAC;EACD1C,WAAW,EAAE;IACXhB,OAAO,EAAE,MAAM;IACf2D,GAAG,EAAE;EACP,CAAC;EACD1C,YAAY,EAAE;IACZ4B,OAAO,EAAE,UAAU;IACnBI,QAAQ,EAAE,MAAM;IAChBnC,eAAe,EAAE,SAAS;IAC1BuC,KAAK,EAAE,MAAM;IACblB,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBkB,MAAM,EAAE;EACV,CAAC;EACDzC,gBAAgB,EAAE;IAChBb,OAAO,EAAE,cAAc;IACvBiC,KAAK,EAAE,KAAK;IACZU,MAAM,EAAE,KAAK;IACbP,YAAY,EAAE,KAAK;IACnBmB,UAAU,EAAE,KAAK;IACjBN,QAAQ,EAAE,KAAK;IACfI,KAAK,EAAE,OAAO;IACdO,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd;AACF,CAAC;AAED,eAAe7I,IAAI;AAAC,IAAAgH,EAAA;AAAA8B,YAAA,CAAA9B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}