{"name": "website-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/material": "^6.1.6", "@react-oauth/google": "^0.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "chart.js": "^4.4.6", "cloudinary": "^2.5.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "myapp": "file:", "quagga": "^0.12.1", "react": "^18.3.1", "react-audio-player": "^0.17.0", "react-big-calendar": "^1.16.3", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-facebook": "^9.0.12", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-toastify": "^10.0.6", "recharts": "^2.13.3", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}