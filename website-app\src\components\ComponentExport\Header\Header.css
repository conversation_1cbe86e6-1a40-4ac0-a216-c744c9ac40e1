.order-mgmt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f4f8; /* Light background */
    border-radius: 10px;
  }
  .order-mgmt-title {
    font-size: 24px;
    color: #2c3e50; /* Navy color for better contrast */
    font-weight: 600;
  }
  .order-mgmt-header-controls {
    display: flex;
    align-items: center;
  }
  .order-mgmt-search {
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #ccc;
    margin-right: 20px;
    background-color: #ffffff; /* White for clean look */
    color: #2c3e50; /* Navy for contrast */
    width: 250px;
    outline: none;
  }
  .order-mgmt-create-btn {
    display:block;
    background-color: #007bff; /* Bright blue for visibility */
    color: white;
    padding: 7px 20px;
    border: none;
    width:80;
    height:42;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    box-shadow: 0px 4px 8px rgba(0, 123, 255, 0.2); /* Blue shadow */
    margin-left: 30px;
  }
  .order-mgmt-history-btn {
    display: block;
    background-color: #ddff00d0; /* Bright blue for visibility */
    color: rgb(0, 0, 0);
    padding: 7px 20px;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    width:80;
    height:42;
    transition: background-color 0.3s;
    box-shadow: 0px 4px 8px rgba(0, 123, 255, 0.2); /* Blue shadow */
    margin-left: 30px;
    height:100%;
  }