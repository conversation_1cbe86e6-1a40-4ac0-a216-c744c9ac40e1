.header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    box-shadow: rgba(33, 35, 38, 0.1) 0px 10px 10px -10px;
    background-color: #fff;
    position: fixed;
    top: 0;
    z-index: 20;
    
  }
  
  .header__right{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header__setting, .header__notify, .header__user {
    margin-right: 20px;
    cursor: pointer;
    background-color: #fff;
    border-radius: 50%;
    padding: 8px;
    text-align: center;
    color: #333;
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .search-box {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 5px 10px;
    width: 200px;
}

.search-box input {
    border: none;
    outline: none;
    font-size: 16px;
    width: 100%;
    padding: 5px;
}

.search-box input::placeholder {
    color: #999;
}

.search-box i {
    color: #666;
    font-size: 16px;
    margin-right: 5px;
}
.search-icon {
  color: #666;
  font-size: 16px;
  margin-right: 5px;
} 