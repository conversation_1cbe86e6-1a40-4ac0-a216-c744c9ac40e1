.form-container {
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    position: absolute;
    z-index:2;
     left:50%;
    transform: translate(-50%);
    border: rgb(0, 0, 0) 5px solid;
    overflow-y: auto;
    max-height: 80%;
    min-height: 600px;
}
.form-container h2 {
    text-align: center; /* Căn giữa tiêu đề */
    font-size: 20px; /* Tăng kích thước chữ */
    font-weight: bold; /* Đậm chữ */
    color: #333; /* <PERSON><PERSON><PERSON> chữ */
    margin-bottom: 30px; /* Khoảng cách dưới tiêu đề */
    text-transform: uppercase; /* Chữ in hoa */
    letter-spacing: 2px; /* Khoảng cách giữa các chữ */
    border-bottom: 2px solid #007bff; /* <PERSON><PERSON><PERSON><PERSON> kẻ dưới tiêu đề */
    padding-bottom: 10px; /* <PERSON><PERSON><PERSON><PERSON> cách dưới tiêu đề */
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    min-width: 250px;
    padding: 0 10px;
    box-sizing: border-box;
    margin-bottom: 15px;
}

.form-group.full-width {
    flex: 1 1 100%;
    padding: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
    transition: border-color 0.3s;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #28a745;
    outline: none;
}

.form-group textarea {
    resize: vertical;
    height: 80px;
}


.form-group input[type="submit"]:hover {
    background-color: #218838;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-group {
        padding: 0;
    }
}
.close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #555;
    cursor: pointer;
}
.submit-row {
    display: flex;
    justify-content: center; /* Căn chỉnh nút Submit bên phải */
    margin-top: 20px; /* Khoảng cách giữa các hàng */
}
.submit-group input[type="submit"] {
    background-color: #28a745;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
    padding: 12px 20px;
    border-radius: 5px;
    transition: background-color 0.3s;
}
input[type="file"] {
    width: 100%;
    padding: 10px 0px;
    background-color: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

input[type="file"]:hover {
    background-color: #e9ecef;
    border-color: #007bff;
}

input[type="file"]::-webkit-file-upload-button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

input[type="file"]::-webkit-file-upload-button:hover {
    background-color: #0056b3;
}
.capture{
    margin-top: 5px;
    height: 40px;
    width: 150px;
    border-radius: 5px;
    background-color: yellow;
    font-size: 18px;  /* Kích thước chữ */
    font-family: 'Arial', sans-serif;  /* Kiểu chữ */
    font-weight: bold;  /* Độ đậm của chữ */
    letter-spacing: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 5px black solid;
    cursor: pointer;
    transition: 0.25s ease-in-out;
}
.capture:hover{
    background-color: rgba(0, 255, 234, 0.5);
}
.camera-modal {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .camera-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
  }
  
  video {
    border: 2px solid black;
    border-radius: 10px;
  }
  
  .button-capture {
    margin: 10px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  