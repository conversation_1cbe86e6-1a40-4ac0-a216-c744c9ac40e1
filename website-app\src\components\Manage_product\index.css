/* src/ProductManager.css */
.product-manager {
  padding: 0px 20px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Thanh filter bar với bo góc, nền mờ, và scroll */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;

}

/* Phần scrollable của các nút */
.scrollable-categories {
  overflow: hidden;
  padding: 10px 10px;
  width: 100%; 
  white-space: nowrap;
  scroll-behavior: smooth;
 
}

.scrollable-categories::-webkit-scrollbar {
  display: none; /* Ẩn thanh cuộn trên Chrome, Safari */
}

/* Nút danh mục */
.category-button {
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.6);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  font-size: 16px;
  font-family: "Arial", sans-serif; /* <PERSON><PERSON><PERSON> thiện font chữ gần giống */
}

/* Hi<PERSON>u ứng khi hover */
.category-button:hover {
  background-color: #e0e0e0;
  transform: scale(1.05);
}

/* <PERSON><PERSON><PERSON> đ<PERSON> chọn */
.category-button.active {
  background-color: #007bff;
  color: white;
  position: relative;
}

.category-button.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background-color: black; /* Đường gạch dưới */
}

/* Nút "Tạo" với kiểu dáng đẹp */
.create-button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); /* Bóng đổ nhẹ */
  margin-left: 20px;
}

.create-button:hover {
  background-color: #0056b3;
}
.scroll-button {
  background-color: #ffffff; /* Màu nền */
  color: #007bff; /* Màu chữ */
  border: none; /* Không có viền */
  border-radius: 5px; /* Bo tròn góc */
  padding: 2px 3px; /* Kích thước nút */
  font-size: 18px; /* Kích thước chữ */
  cursor: pointer; /* Con trỏ khi di chuột */
  transition: background-color 0.3s, transform 0.2s; /* Hiệu ứng chuyển màu và phóng to */
}

.scroll-button:hover {
  background-color: #ffffff; /* Màu nền khi hover */
  transform: scale(1.3); /* Phóng to một chút khi hover */
}

.scroll-button:active {
  transform: scale(0.95); /* Nhấn và thu nhỏ */
}
.extended-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px; /* Khoảng cách giữa các phần */
}

/* Kiểu cho ô tìm kiếm */
.search-input {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 20px;
  width: 30%;
}

/* Kiểu cho select sắp xếp */
.sort-select {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 20px;
  margin-left: 10px;
}

/* Kiểu cho nút xem lịch sử */
.history-button {
  padding: 10px 20px;
  background-color: #e0f000;
  color: rgb(0, 0, 0);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.history-button:hover {
  background-color: #899300;
}
.x{
  background: rgba(255, 255, 255, 0.7); /* Nền mờ */
  backdrop-filter: blur(10px);
  border-radius: 30px; /* Bo góc mềm */
  padding: 10px 20px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); /* Bóng đổ nhẹ */
  margin-bottom: 20px;
  overflow: hidden;
}
.supplier-button {
  padding: 10px 20px;
  background-color: #45da0a;
  color: rgb(0, 0, 0);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.supplier-button:hover {
  background-color: #45da0a8a;
}