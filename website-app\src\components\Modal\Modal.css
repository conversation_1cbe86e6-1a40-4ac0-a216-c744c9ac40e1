.modal-wrapper {
  position: relative;
}

.uy-modal-content {
  font-size: 14px;
  position: absolute;
  right: 0px;
  background-color: #fff;
  border-radius: 10px;
  width: 250px;
  padding: 20px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.3);
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.uy-avatar-container {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 10px;
}

.uy-avatar{
  width: 100%;
  height: 100%;
}

.uy-avatar .uy-avatar,
.uy-avatar .uy-avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover; 
  border-radius: 50%; 
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-details strong {
  font-size: 16px;
}

.email {
  font-size: 12px;
  color: #A0A0B1;
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.menu-item i {
  margin-right: 10px;
}

.menu-item:hover {
  background-color:#E7EDFF;
  color: #1514ef;
  border-radius: 5px;
  transition: 0.3 ease;
}

hr {
  border: none;
  border-top: 1px solid #E7EDFF;
  margin: 15px 0;
}
