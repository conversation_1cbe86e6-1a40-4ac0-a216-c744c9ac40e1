

/* Class .customer-form để ngăn xung đột */
.customer{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.customer-form {
    overflow-y: auto;
    max-height: 400px;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: left;
    position: relative;
    cursor: pointer;
    min-width: 350px;
    min-height:300px
}

.customer-form h2 {
    text-align: center;
    color: #007BFF;
    margin-bottom: 20px;
    font-weight: 700;
    margin-right: 20px;
}

.customer-form label {
    display: block;
    font-weight: bold;
    margin-top: 10px;
    color: #555;
}

.customer-form input[type="text"],
.customer-form input[type="email"],
.customer-form input[type="tel"] {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
}

.customer-form button {
    width: 100%;
    padding: 10px;
    background-color: #007BFF;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 4px;
    font-size: 18px;
    cursor: pointer;
    margin-top: 10px;
}

.customer-form button:hover {
    background-color: #0056b3;
}

.customer-form h3 {
    margin-top: 20px;
    color: #007BFF;
}
.close-customer{
    position: absolute;
    font-size: 30px;
    right: 15px;
    top: 10px;
}