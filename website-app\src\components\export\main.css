/* .create-order-form-container {
    width: 60%;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    background-color: #fff;
  }
  
  .create-order-form-title {
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
    color: #333;
  }
  
  .create-order-form-label {
    display: block;
    margin-bottom: 10px;
    font-size: 16px;
  }
  
  .create-order-form-input,
  .create-order-form-textarea,
  .create-order-form-select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  .create-order-form-button-container {
    text-align: center;
    margin-top: 20px;
  }
  
  .create-order-form-button {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    background-color: #28a745;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
  }
  
  .create-order-form-reset-button {
    background-color: #dc3545;
  }
  
  .create-order-form-total-amount {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 15px;
  }
   */
   .billing-container {
    margin: 0 auto;
    padding: 20px;
    border-radius: 8px;
    background-color: #f9f9f9;
  }
  
  .invoice-bar {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
  }
  
  .invoice-tab {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  
  .invoice-bar button {
    padding: 10px;
    background-color: #eee;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-right: 5px;
  }
  
  .invoice-bar .active {
    background-color: #007bff;
    color: white;
  }
  
  .top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .form-group-sell {
    margin-right: 20px;
  }
  
  .label-sell {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  
  .input-sell {
    margin-bottom: 5px;
    font-weight: bold;
    outline: none;        /* Xóa viền khi focus */
    box-shadow: none;     /* Xóa đổ bóng */
    background: none;  
    padding: 10px 5px;
    margin-right: 10px;
  }
  
  .button-sell {
    padding: 10px 15px;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .button-sell:hover {
    background-color: #0056b3;
    color: white;
  }
  
  .product-list {
    margin-top: 20px;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
  }
  
  th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: center;
  }
  
  .summary {
    margin-top: 20px;
  }
  
  .result {
    font-size: 18px;
    font-weight: bold;
  }
  .delete_prd{
    color: red;
    cursor: pointer;
  }
  .delete_prd:hover{
background-color: red;
color: white;
  }
  .camera-sell{


    position: absolute;
  }
  .button-capture-sell{
    display: block;
    margin-left: 50%;
    transform: translateX(-50%);
  }
  #suggestions-sell{
    list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
  position: absolute;
  background-color: white;
  width: 187px;
  }
  #suggestions-sell li {
    padding: 8px;
    cursor: pointer;
    border: 1px black solid;
    background-color: #f0f0f0;
  }
  #suggestions-sell li:hover {
    background-color: #2f262698;
  }
  .xx{

  }
  .history{
    padding: 10px 15px;
    background-color: #e7e717;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
  }
  .history:hover{
    background-color: #727221;
  }
  .create_user{
    padding: 10px 15px;
    background-color: #33e717;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
  }
  .create_user:hover{
    background-color: #33e7178e;
  }