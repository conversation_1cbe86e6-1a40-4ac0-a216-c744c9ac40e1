.Modal-title{
    display: flex;
    justify-content: center;
    font-weight: 600;
    font-size: 20px;
}
.divide{
    height:2px;
    margin: 8px;
    background: #000;
}
.order-mgmt-search{
    padding-left: 6px;
    margin-left: 4px;
    width: 95%;
  }
  .btn-add-order{
    border: none;
    background: transparent;
    background: #4568f5;
    color:#fff;
    border-radius: 8px;
    display:block;
    padding: 0 8px;
    height: 34px;
  }
  .search-container{
    display: flex;
    flex: 1;
  }
  .header-order{
    margin:10px 12px;
    display: flex;
    justify-content: space-between;
    position: sticky; /* Đặt phần header ở vị trí cố định */
  top: 0; /* Đặt khoảng cách từ đầu modal */
  background: white; /* Nền trắng để giữ cho nó rõ ràng */
  z-index: 10; /* Đảm bảo header ở trên cùng */
  }
  .dropdown{
    margin-top: 4px;
    background:#fbfbfb;
    border-radius: 10px;
    position: absolute; 
    top: 100%; 
    left: 0; 
    scrollbar-width: thin;
    background-color: white; 
    z-index: 1000; 
    border: 1px solid #ccc; 
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); 
    width: 100%; 
    max-height: 200px; 
    overflow-y: auto; 
  }
  .search-item{
    padding:  5px 10px 5px;
  }
  .search-container-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .search-container-img{
      height:30px;
      width:50px;
      background-size: cover; /* Để ảnh phủ đầy div */
      background-position: center; /* Căn giữa ảnh */
  }
  .search-item:last-child .divide {
    display: none;
}
.search-result-container{
  position: relative;
  flex:1;
}
.search-item:hover{
  background: #ebebeb;
}
.body-modal{
  max-height: calc(80vh - 150px); /* Chiều cao còn lại cho phần body */
  overflow-y: auto; /* Thêm thanh cuộn cho phần nội dung */
  scrollbar-width: thin;
}
.dropdown-item{
  padding: 2px 0; /* Thêm padding cho item */
  cursor: pointer; /* Con trỏ chuột dạng pointer */
}
.dropdown-item:hover{
  background: #efefef;
}
.list-product-title{
  display:flex;
  justify-content: center;
  margin: 32px 0;
  font-weight: 600;
  font-size: 24px;
}
.supplier{
  display: flex;
  justify-content: center;
  align-items: center;
}
table {
  width: 100%; /* Đảm bảo bảng chiếm toàn bộ chiều rộng */
  border-collapse: collapse; /* Gộp viền */
}

.product-status.pending{
  background: yellow;

}
.product-status.deliveried{
  background: greenyellow;
}
.product-status.canceled{
  background: red;
}
.dropdown.supplierOption{
  position: absolute;
   top: 100%;
   left: 0;
   width:180px;
}
.dropdown.supplier:hover{
  background: #eee;
}
.list-product-detail th, .list-product-detail td {
  padding: 8px; /* Khoảng cách trong ô */
  text-align: left; /* Căn trái nội dung */
}

.list-product-detail th {
  text-align: center;
  background-color: #f4f4f4; /* Màu nền cho tiêu đề cột */
}
.list-product-detail td{
  vertical-align: middle;
}
/* Điều chỉnh chiều rộng các cột */
.list-product-detail th:nth-child(1),.list-product-detail td:nth-child(1) { /* STT */
  width: 10px; /* Chiều rộng cho cột STT */
  text-align: center;

}
.list-product-detail th:nth-child(2),.list-product-detail td:nth-child(2) { /* Ảnh Mô Tả */
  width: 120px; /* Chiều rộng cho cột Ảnh Mô Tả */
}
.list-product-detail th:nth-child(3),.list-product-detail td:nth-child(3) { /* Sản Phẩm */
  width: 200px; /* Chiều rộng cho cột Sản Phẩm */
  text-align: center;
}

.list-product-detail th:nth-child(4),.list-product-detail td:nth-child(4) { /* Nhà Cung Cấp */
  width: 100px; /* Chiều rộng cho cột Nhà Cung Cấp */
  text-align: center;
}

.list-product-detail th:nth-child(5),.list-product-detail td:nth-child(5) { /* Số Lượng */
  width: 60px; /* Chiều rộng cho cột Số Lượng */
  padding:0;
}

.list-product-detail th:nth-child(6),.list-product-detail td:nth-child(6) { /* Thành Tiền */
  width: 70px; /* Chiều rộng cho cột Thành Tiền */
  
}
.list-product-detail td:nth-child(6){
  text-align: right;
}
.list-product-detail th:nth-child(7),.list-product-detail  td:nth-child(7) { /* Hành Động */
  width: 90px; /* Chiều rộng cho cột Hành Động */
}
.list-product-detail th:nth-child(8),.list-product-detail td:nth-child(8) { /* Hành Động */
  width: 10px; /* Chiều rộng cho cột Hành Động */
  text-align: center;

}
.list-product-detail th:nth-child(9),.list-product-detail td:nth-child(9) { /* Hành Động */
  width: 10px; /* Chiều rộng cho cột Hành Động */
  text-align: center;
}

.body-container-img-description{
  height:110px;
  margin: 4px 4px;
  border-radius: 20px;
  width:100%;
  background-size: cover; /* Để ảnh phủ đầy div */
  background-position: center;
}
.modal-body-product-name{
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: 500;
}
.modal-body-product-description{
color: #666;
font-size: 13px;
}
.Quantity{
  display: flex;
  align-items: center; 
  justify-content: center;
}
.Quantity-button{
  outline: none;
  padding: 4px 4px;
  cursor: pointer;
  border:none;
  border-radius: 2px;
  background: transparent;
  width: 18px;
  text-align: center;
}
.Quantity-button:hover{
  background: #eee;
}
.Quantity-input{
  width: 32px;
  text-align: center; 
  margin: 0 10px; 
  border:1px solid #4568f5;
  border-radius: 4px;
  height: 24px;
}
.detail-button{
  outline: none;
  border: none;
  background: transparent;
  
}
.dropdownRemove{
  position: absolute;
  top: -20px;
  left:-56px;
  width: 48px;
  height: 32px;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dropdownRemove button{
  border: none;
  background: transparent;

}
.product-status{
  width: 100%;
  padding: 8px 0px;
  background:green;
  text-align: center;
  border-radius: 10px;
  cursor:pointer;
}

.order-tax{
  padding: 18px 0 0 80px;
  font-size: 20px;
  font-weight: 600;
}
.complete-order{
  display: flex;
  justify-content: center;
  margin-top: 32px;
}
.complete-order button{
  border: none;
  background: rgb(29, 230, 29);
  height: 36px;
  width: 110px;
  border-radius: 12px;
  color: #fff;
}