/* RolesGroup Container */
.roles-group {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.roles-group h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

/* Search and Header */
.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.uy-search-container {
  display: flex;
  gap: 10px;
}

.uy-search-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  width: 250px;
}

.create-role-btn {
  padding: 10px 20px;
  background-color: #0056b3;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.create-role-btn:hover {
  background-color: #034994;
}

/* Table Styling */
/* .uy-table-container {
  overflow-x: auto;
} */

.uy-role-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  font-size: 16px;
}

.uy-role-table th, .uy-role-table td {
  padding: 10px;
  text-align: left;
  border: 1px solid #ddd;
}

.uy-role-table th {
  font-weight: bold;
  background-color: #f4f4f9;
  color: #333;
}

/* Action Menu */
.uy-action {
  position: relative;
}

.uy-menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
}

.uy-dropdown-menu {
  position: absolute;
  /* top: 100%;
  left: 0; */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.uy-dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.uy-dropdown-menu li {
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.uy-dropdown-menu li:hover {
  background-color: #f4f4f9;
}

/* Modal Styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

.create-role-form h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
}

.create-role-form input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 14px;
}

.create-role-form button {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.create-role-form button[type="submit"] {
  background-color: #0056b3;
  color: #fff;
}

.create-role-form button[type="submit"]:hover {
  background-color: #014893;
}

.create-role-form .cancel-btn {
  background-color: #f44336;
  color: #fff;
  margin-left: 10px;
}

.create-role-form .cancel-btn:hover {
  background-color: #e53935;
}
