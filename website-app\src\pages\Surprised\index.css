.new-year-container {
    text-align: center;
    padding: 20px;
    background: linear-gradient(to bottom, #1e3c72, #2a5298);
    background-image: url('https://cdn.sforum.vn/sforum/wp-content/uploads/2022/12/hinh-nen-anime-tet-10-1.jpg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: white;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
    border-radius: 10px; /* Nếu cần hiệu ứng góc bo */
  }
  
  
  .new-year-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
    animation: glow 1.5s infinite alternate;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1s ease-out, transform 1s ease-out;
  }
  
  .new-year-message {
    font-size: 1.5rem;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 1s ease-out, transform 1s ease-out;
  }
  
  .new-year-title.visible,
  .new-year-message.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  .confetti {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  
  .fireworks {
    position: relative;
  }
  
  .firework {
    width: 10px;
    height: 10px;
    background: gold;
    border-radius: 50%;
    position: absolute;
    animation: firework 1s infinite ease-in-out;
  }
  
  .firework:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .firework:nth-child(3) {
    animation-delay: 0.4s;
  }
  
  @keyframes glow {
    from {
      text-shadow: 0 0 10px yellow, 0 0 20px gold, 0 0 30px red;
    }
    to {
      text-shadow: 0 0 20px red, 0 0 30px gold, 0 0 40px yellow;
    }
  }
  
  @keyframes firework {
    0% {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
    50% {
      transform: scale(2) translateY(-50px);
      opacity: 0.7;
    }
    100% {
      transform: scale(3) translateY(-100px);
      opacity: 0;
    }
  }
  .new-year-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
    opacity: 0;
    transition: opacity 1s ease-in-out;
  }
  
  .new-year-message {
    font-size: 1.5rem;
    margin-bottom: 20px;
    opacity: 0;
    transition: opacity 1s ease-in-out;
  }
  
  .new-year-title, .new-year-message {
    opacity: 1;
  }
  